"""
Django management command to start the SMPP client.
Usage: python manage.py start_smpp
"""

import time
import signal
import sys
from django.core.management.base import BaseCommand
from django.conf import settings
from sms_app.sms_handler import start_smpp_client, stop_smpp_client, get_smpp_status


class Command(BaseCommand):
    help = 'Start the SMPP client for receiving and sending SMS messages'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--daemon',
            action='store_true',
            help='Run as daemon (background process)',
        )
        parser.add_argument(
            '--status',
            action='store_true',
            help='Show SMPP client status',
        )
        parser.add_argument(
            '--stop',
            action='store_true',
            help='Stop the SMPP client',
        )
    
    def handle(self, *args, **options):
        if options['status']:
            self.show_status()
            return
        
        if options['stop']:
            self.stop_client()
            return
        
        self.start_client(daemon=options['daemon'])
    
    def show_status(self):
        """Show SMPP client status"""
        self.stdout.write("📊 SMPP Client Status:")
        self.stdout.write("-" * 30)
        
        status = get_smpp_status()
        
        for key, value in status.items():
            if key == 'config':
                self.stdout.write(f"Configuration:")
                for config_key, config_value in value.items():
                    if config_key == 'PASSWORD':
                        config_value = '*' * len(str(config_value))
                    self.stdout.write(f"  {config_key}: {config_value}")
            else:
                icon = "✅" if value else "❌"
                self.stdout.write(f"{icon} {key.replace('_', ' ').title()}: {value}")
    
    def stop_client(self):
        """Stop SMPP client"""
        self.stdout.write("🛑 Stopping SMPP client...")
        
        if stop_smpp_client():
            self.stdout.write(
                self.style.SUCCESS("✅ SMPP client stopped successfully")
            )
        else:
            self.stdout.write(
                self.style.ERROR("❌ Failed to stop SMPP client")
            )
    
    def start_client(self, daemon=False):
        """Start SMPP client"""
        self.stdout.write("🚀 Starting SMPP client...")
        
        # Check configuration
        smpp_config = getattr(settings, 'SMPP_CONFIG', {})
        if not smpp_config:
            self.stdout.write(
                self.style.ERROR("❌ SMPP_CONFIG not found in settings")
            )
            return
        
        self.stdout.write(f"📡 Connecting to {smpp_config.get('HOST')}:{smpp_config.get('PORT')}")
        self.stdout.write(f"🆔 System ID: {smpp_config.get('SYSTEM_ID')}")
        
        # Start SMPP client
        if start_smpp_client():
            self.stdout.write(
                self.style.SUCCESS("✅ SMPP client started successfully")
            )
            
            if daemon:
                self.stdout.write("🔄 Running in daemon mode. Press Ctrl+C to stop.")
                
                # Set up signal handlers for graceful shutdown
                def signal_handler(signum, frame):
                    self.stdout.write("\n🛑 Received shutdown signal. Stopping SMPP client...")
                    stop_smpp_client()
                    sys.exit(0)
                
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)
                
                # Keep the process alive
                try:
                    while True:
                        time.sleep(1)
                        # You could add health checks here
                        status = get_smpp_status()
                        if not status['running'] or not status['connected']:
                            self.stdout.write(
                                self.style.WARNING("⚠️ SMPP client disconnected. Attempting restart...")
                            )
                            start_smpp_client()
                            
                except KeyboardInterrupt:
                    self.stdout.write("\n🛑 Stopping SMPP client...")
                    stop_smpp_client()
            else:
                self.stdout.write("ℹ️ SMPP client is running in background.")
                self.stdout.write("ℹ️ Use 'python manage.py start_smpp --status' to check status.")
                self.stdout.write("ℹ️ Use 'python manage.py start_smpp --stop' to stop.")
        else:
            self.stdout.write(
                self.style.ERROR("❌ Failed to start SMPP client")
            )
