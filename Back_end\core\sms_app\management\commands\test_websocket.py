from django.core.management.base import BaseCommand
from django.conf import settings
import asyncio
import websockets
import json
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Test WebSocket connection to verify Django Channels setup'

    def add_arguments(self, parser):
        parser.add_argument(
            '--host',
            type=str,
            default='localhost',
            help='WebSocket host (default: localhost)'
        )
        parser.add_argument(
            '--port',
            type=int,
            default=8000,
            help='WebSocket port (default: 8000)'
        )

    def handle(self, *args, **options):
        host = options['host']
        port = options['port']
        
        self.stdout.write(
            self.style.SUCCESS(f'Testing WebSocket connection to {host}:{port}')
        )
        
        # Run the async test
        asyncio.run(self.test_websocket(host, port))

    async def test_websocket(self, host, port):
        """Test WebSocket connection"""
        uri = f"ws://{host}:{port}/ws/messages/"
        
        try:
            self.stdout.write(f"Attempting to connect to {uri}...")
            
            async with websockets.connect(uri) as websocket:
                self.stdout.write(
                    self.style.SUCCESS("✅ WebSocket connection established!")
                )
                
                # Wait for connection confirmation
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    self.stdout.write(f"📨 Received: {data}")
                    
                    if data.get('type') == 'connection_established':
                        self.stdout.write(
                            self.style.SUCCESS("✅ Connection confirmed by server")
                        )
                        
                        # Send a test ping
                        test_message = {
                            'type': 'ping',
                            'timestamp': '2025-08-19T12:00:00Z'
                        }
                        
                        await websocket.send(json.dumps(test_message))
                        self.stdout.write(f"📤 Sent ping: {test_message}")
                        
                        # Wait for pong response
                        pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        pong_data = json.loads(pong_response)
                        self.stdout.write(f"📨 Received pong: {pong_data}")
                        
                        if pong_data.get('type') == 'pong':
                            self.stdout.write(
                                self.style.SUCCESS("✅ Ping/Pong test successful!")
                            )
                        else:
                            self.stdout.write(
                                self.style.WARNING("❌ Expected pong response, got something else")
                            )
                            
                except asyncio.TimeoutError:
                    self.stdout.write(
                        self.style.WARNING("⏰ Timeout waiting for server response")
                    )
                    
        except ConnectionRefusedError:
            self.stdout.write(
                self.style.ERROR("❌ Connection refused - is the Django server running?")
            )
        except websockets.exceptions.InvalidStatusCode as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Invalid status code: {e}")
            )
            self.stdout.write(
                "This usually means the WebSocket endpoint is not configured correctly"
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Unexpected error: {e}")
            )

        # Test configuration
        self.stdout.write("\n" + "="*50)
        self.stdout.write("Django Configuration:")
        self.stdout.write(f"ASGI_APPLICATION: {getattr(settings, 'ASGI_APPLICATION', 'Not set')}")
        self.stdout.write(f"CHANNEL_LAYERS: {getattr(settings, 'CHANNEL_LAYERS', 'Not set')}")
        self.stdout.write(f"INSTALLED_APPS includes 'channels': {'channels' in settings.INSTALLED_APPS}")
