#!/usr/bin/env python3
"""
Script to run Django with ASGI support for WebSocket functionality
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
    
    # Ensure we're using ASGI
    print("🚀 Starting Django with ASGI support for WebSocket functionality")
    print("📡 WebSocket endpoints will be available at:")
    print("   - ws://localhost:8000/ws/messages/")
    print("   - ws://localhost:8000/ws/notifications/<user_id>/")
    print("   - ws://localhost:8000/ws/assignments/")
    print("=" * 60)
    
    # Run the development server
    # Django 3.0+ automatically uses ASGI when channels is installed
    execute_from_command_line(['manage.py', 'runserver', '0.0.0.0:8000'])
