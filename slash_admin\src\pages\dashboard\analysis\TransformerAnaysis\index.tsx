import React, { useState, useEffect } from "react";
import { Table, Tabs } from "antd";
import type { ColumnsType } from "antd/es/table";
import { Column } from "@ant-design/plots";
import Scrollbar from "@/components/scrollbar";
import TransformerPieChart from "./TransformerPieChart";
import axios from "axios";
import { CircleLoading } from "@/components/loading";
import apiClient from "@/api/apiClient";

const { TabPane } = Tabs;

interface TableData {
	key: string;
	region: string;
	csc: string;
	// Transformer Types
	conservator: number;
	conservatorPercentage: number;
	hermetical: number;
	hermeticalPercentage: number;
	compact: number;
	compactPercentage: number;
	// Capacity
	kva10: number;
	kva10Percentage: number;
	kva25: number;
	kva25Percentage: number;
	kva50: number;
	kva50Percentage: number;
	kva100: number;
	kva100Percentage: number;
	kva200: number;
	kva200Percentage: number;
	kva315: number;
	kva315Percentage: number;
	kva400: number;
	kva400Percentage: number;
	kva500: number;
	kva500Percentage: number;
	kva630: number;
	kva630Percentage: number;
	kva800: number;
	kva800Percentage: number;
	kva1250: number;
	kva1250Percentage: number;
	kva2500: number;
	kva2500Percentage: number;
	kvaOther: number;
	kvaOtherPercentage: number;
	// Cooling Type
	onan: number;
	onanPercentage: number;
	dryType: number;
	dryTypePercentage: number;
	// Manufacturer
	abbTanzania: number;
	abbTanzaniaPercentage: number;
	apex: number;
	apexPercentage: number;
	// Primary Voltage
	v15: number;
	v15Percentage: number;
	v19: number;
	v19Percentage: number;
	v33: number;
	v33Percentage: number;
	other: number;
	otherPercentage: number;
	// Vector Group
	dy1: number;
	dy1Percentage: number;
	dy5: number;
	dy5Percentage: number;
	dy11: number;
	dy11Percentage: number;
	other: number;
	otherPercentage: number;
	// Year of Manufacturing
	y2020: number;
	y2020Percentage: number;
	y2021: number;
	y2021Percentage: number;
	y2022: number;
	y2022Percentage: number;
}

interface CSCData extends TableData {
	key: string;
	csc: string;
}

interface RegionData extends TableData {
	key: string;
	region: string;
	children: CSCData[];
}

// Transformer Types Data
const transformerTypesData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		conservator: 200,
		conservatorPercentage: 40,
		hermetical: 240,
		hermeticalPercentage: 48,
		compact: 60,
		compactPercentage: 12,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				conservator: 100,
				conservatorPercentage: 40,
				hermetical: 120,
				hermeticalPercentage: 48,
				compact: 30,
				compactPercentage: 12,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		conservator: 180,
		conservatorPercentage: 36,
		hermetical: 250,
		hermeticalPercentage: 50,
		compact: 70,
		compactPercentage: 14,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				conservator: 90,
				conservatorPercentage: 36,
				hermetical: 125,
				hermeticalPercentage: 50,
				compact: 35,
				compactPercentage: 14,
			},
		],
	},
];

// Capacity Data
const capacityData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		kva10: 100,
		kva10Percentage: 20,
		kva25: 200,
		kva25Percentage: 40,
		kva50: 200,
		kva50Percentage: 40,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				kva10: 50,
				kva10Percentage: 20,
				kva25: 100,
				kva25Percentage: 40,
				kva50: 100,
				kva50Percentage: 40,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		kva10: 120,
		kva10Percentage: 24,
		kva25: 180,
		kva25Percentage: 36,
		kva50: 200,
		kva50Percentage: 40,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				kva10: 60,
				kva10Percentage: 24,
				kva25: 90,
				kva25Percentage: 36,
				kva50: 100,
				kva50Percentage: 40,
			},
		],
	},
];

// Cooling Type Data
const coolingTypeData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		onan: 300,
		onanPercentage: 60,
		dryType: 200,
		dryTypePercentage: 40,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				onan: 150,
				onanPercentage: 60,
				dryType: 100,
				dryTypePercentage: 40,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		onan: 280,
		onanPercentage: 56,
		dryType: 220,
		dryTypePercentage: 44,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				onan: 140,
				onanPercentage: 56,
				dryType: 110,
				dryTypePercentage: 44,
			},
		],
	},
];

// Manufacturer Data
const manufacturerData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		abbTanzania: 240,
		abbTanzaniaPercentage: 48,
		apex: 260,
		apexPercentage: 52,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				abbTanzania: 120,
				abbTanzaniaPercentage: 48,
				apex: 130,
				apexPercentage: 52,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		abbTanzania: 230,
		abbTanzaniaPercentage: 46,
		apex: 270,
		apexPercentage: 54,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				abbTanzania: 115,
				abbTanzaniaPercentage: 46,
				apex: 135,
				apexPercentage: 54,
			},
		],
	},
];

// Primary Voltage Data
const voltageData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		v15: 300,
		v15Percentage: 60,
		v19: 200,
		v19Percentage: 40,
		v33: 100,
		v33Percentage: 20,
		other: 50,
		otherPercentage: 10,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				v15: 150,
				v15Percentage: 60,
				v19: 100,
				v19Percentage: 40,
				v33: 50,
				v33Percentage: 20,
				other: 25,
				otherPercentage: 10,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		v15: 280,
		v15Percentage: 56,
		v19: 220,
		v19Percentage: 44,
		v33: 110,
		v33Percentage: 22,
		other: 60,
		otherPercentage: 12,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				v15: 140,
				v15Percentage: 56,
				v19: 110,
				v19Percentage: 44,
				v33: 55,
				v33Percentage: 22,
				other: 30,
				otherPercentage: 12,
			},
		],
	},
];

// Vector Group Data
const vectorGroupData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		dy1: 100,
		dy1Percentage: 20,
		dy5: 150,
		dy5Percentage: 30,
		dy11: 120,
		dy11Percentage: 24,
		other: 80,
		otherPercentage: 16,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				dy1: 50,
				dy1Percentage: 20,
				dy5: 75,
				dy5Percentage: 30,
				dy11: 60,
				dy11Percentage: 24,
				other: 40,
				otherPercentage: 16,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		dy1: 120,
		dy1Percentage: 24,
		dy5: 180,
		dy5Percentage: 36,
		dy11: 140,
		dy11Percentage: 28,
		other: 90,
		otherPercentage: 18,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				dy1: 60,
				dy1Percentage: 24,
				dy5: 90,
				dy5Percentage: 36,
				dy11: 70,
				dy11Percentage: 28,
				other: 45,
				otherPercentage: 18,
			},
		],
	},
];

// Year of Manufacturing Data
const manufacturingYearData: RegionData[] = [
	{
		key: "north",
		region: "North",
		csc: "",
		y2020: 160,
		y2020Percentage: 32,
		y2021: 200,
		y2021Percentage: 40,
		y2022: 140,
		y2022Percentage: 28,
		children: [
			{
				key: "north-csc1",
				region: "North",
				csc: "CSC-1",
				y2020: 80,
				y2020Percentage: 32,
				y2021: 100,
				y2021Percentage: 40,
				y2022: 70,
				y2022Percentage: 28,
			},
		],
	},
	{
		key: "south",
		region: "South",
		csc: "",
		y2020: 150,
		y2020Percentage: 30,
		y2021: 190,
		y2021Percentage: 38,
		y2022: 160,
		y2022Percentage: 32,
		children: [
			{
				key: "south-csc1",
				region: "South",
				csc: "CSC-1",
				y2020: 75,
				y2020Percentage: 30,
				y2021: 95,
				y2021Percentage: 38,
				y2022: 80,
				y2022Percentage: 32,
			},
		],
	},
];

// Add this new function to prepare data for graphs
const prepareGraphData = (tabKey: string, data: TableData[]) => {
	const filteredData = data.filter((item) => item.key !== "grand_total");

	switch (tabKey) {
		case "transformer_types":
			return filteredData.flatMap((item) => [
				{ region: item.region, type: "Conservator", value: item.conservator },
				{ region: item.region, type: "Hermetical", value: item.hermetical },
				{ region: item.region, type: "Compact", value: item.compact },
			]);
		case "capacity":
			return filteredData.flatMap((item) => [
				{ region: item.region, type: "10 kVA", value: item.kva10 },
				{ region: item.region, type: "25 kVA", value: item.kva25 },
				{ region: item.region, type: "50 kVA", value: item.kva50 },
				{ region: item.region, type: "100 kVA", value: item.kva100 },
				{ region: item.region, type: "200 kVA", value: item.kva200 },
				{ region: item.region, type: "315 kVA", value: item.kva315 },
				{ region: item.region, type: "400 kVA", value: item.kva400 },
				{ region: item.region, type: "500 kVA", value: item.kva500 },
				{ region: item.region, type: "630 kVA", value: item.kva630 },
				{ region: item.region, type: "800 kVA", value: item.kva800 },
				{ region: item.region, type: "1250 kVA", value: item.kva1250 },
				{ region: item.region, type: "2500 kVA", value: item.kva2500 },
				{ region: item.region, type: "Other", value: item.kvaOther },
			]);
		case "cooling_type":
			return filteredData.flatMap((item) => [
				{ region: item.region, type: "ONAN", value: item.onan },
				{ region: item.region, type: "Dry Type", value: item.dryType },
			]);
		case "manufacturer":
			return filteredData.flatMap((item) => [
				{ region: item.region, type: "ABB Tanzania", value: item.abbTanzania },
				{ region: item.region, type: "Apex", value: item.apex },
			]);
		default:
			return [];
	}
};

// Add this new component for the graph
const TransformerGraph: React.FC<{ tabKey: string; data: TableData[] }> = ({ tabKey, data }) => {
	const graphData = prepareGraphData(tabKey, data);

	const config = {
		data: graphData,
		isGroup: true,
		xField: "region",
		yField: "value",
		seriesField: "type",
		label: {
			position: "middle",
			layout: [{ type: "interval-adjust-position" }, { type: "interval-hide-overlap" }, { type: "adjust-color" }],
		},
		columnStyle: {
			radius: [4, 4, 0, 0],
		},
	};

	return (
		<div className="mt-8">
			<h3 className="text-lg font-semibold mb-4">Graph</h3>
			<Column {...config} height={400} width={800} />
		</div>
	);
};

const TransformerAnalisis: React.FC = () => {
	const [activeTab, setActiveTab] = useState("transformer_types");
	const [loading, setLoading] = useState(false);
	const [data, setData] = useState<RegionData[]>([]);

	const fetchAnalysisData = async (analysisType: string) => {
		setLoading(true);
		try {
			// const response = await axios.get(`${import.meta.env.VITE_APP_BASE_API}/api/transformer/transformer-analysis/${analysisType}/`);
			const response = await apiClient.get<any>({ url: `/api/transformer/transformer-analysis/${analysisType}/`});


			const transformedData: RegionData[] = response.map((region: any) => {
				const baseData = {
					key: region.key,
					region: region.region,
					csc: "",
					children: region.children.map((csc: any) => ({
						key: csc.key,
						region: csc.region,
						csc: csc.csc,
						...getAnalysisTypeData(analysisType, csc, true),
					})),
					...getAnalysisTypeData(analysisType, region, false),
				};
				return baseData;
			});

			console.log("transformedData", transformedData);
			setData(transformedData);
		} catch (error) {
			console.error("Error fetching analysis data:", error);
		} finally {
			setLoading(false);
		}
	};

	const getAnalysisTypeData = (analysisType: string, data: any, isCSC: boolean) => {
		const calculatePercentage = (value: number, total: number) => (total > 0 ? Math.round((value / total) * 100) : 0);

		switch (analysisType) {
			case "transformer_types": {
				const total = data.conservator + data.hermetical + data.compact;
				return {
					conservator: data.conservator,
					hermetical: data.hermetical,
					compact: data.compact,
					conservatorPercentage: isCSC ? data.conservatorPercentage : calculatePercentage(data.conservator, total),
					hermeticalPercentage: isCSC ? data.hermeticalPercentage : calculatePercentage(data.hermetical, total),
					compactPercentage: isCSC ? data.compactPercentage : calculatePercentage(data.compact, total),
				};
			}
			case "cooling_type": {
				const total = data.onan + data.dryType;
				return {
					onan: data.onan,
					dryType: data.dryType,
					onanPercentage: isCSC ? data.onanPercentage : calculatePercentage(data.onan, total),
					dryTypePercentage: isCSC ? data.dryTypePercentage : calculatePercentage(data.dryType, total),
				};
			}
			case "capacity": {
				const total =
					data.kva10 +
					data.kva25 +
					data.kva50 +
					data.kva100 +
					data.kva200 +
					data.kva315 +
					data.kva400 +
					data.kva500 +
					data.kva630 +
					data.kva800 +
					data.kva1250 +
					data.kva2500 +
					data.other; // Changed from data.kvaOther
				return {
					kva10: data.kva10,
					kva25: data.kva25,
					kva50: data.kva50,
					kva100: data.kva100,
					kva200: data.kva200,
					kva315: data.kva315,
					kva400: data.kva400,
					kva500: data.kva500,
					kva630: data.kva630,
					kva800: data.kva800,
					kva1250: data.kva1250,
					kva2500: data.kva2500,
					kvaOther: data.other, // Changed to match API response
					// For region-level data, use the provided percentages
					kva10Percentage: !isCSC ? data.kva10Percentage : calculatePercentage(data.kva10, total),
					kva25Percentage: !isCSC ? data.kva25Percentage : calculatePercentage(data.kva25, total),
					kva50Percentage: !isCSC ? data.kva50Percentage : calculatePercentage(data.kva50, total),
					kva100Percentage: !isCSC ? data.kva100Percentage : calculatePercentage(data.kva100, total),
					kva200Percentage: !isCSC ? data.kva200Percentage : calculatePercentage(data.kva200, total),
					kva315Percentage: !isCSC ? data.kva315Percentage : calculatePercentage(data.kva315, total),
					kva400Percentage: !isCSC ? data.kva400Percentage : calculatePercentage(data.kva400, total),
					kva500Percentage: !isCSC ? data.kva500Percentage : calculatePercentage(data.kva500, total),
					kva630Percentage: !isCSC ? data.kva630Percentage : calculatePercentage(data.kva630, total),
					kva800Percentage: !isCSC ? data.kva800Percentage : calculatePercentage(data.kva800, total),
					kva1250Percentage: !isCSC ? data.kva1250Percentage : calculatePercentage(data.kva1250, total),
					kva2500Percentage: !isCSC ? data.kva2500Percentage : calculatePercentage(data.kva2500, total),
					kvaOtherPercentage: !isCSC ? data.otherPercentage : calculatePercentage(data.other, total), // Changed to match API response
				};
			}
			case "manufacturer": {
				const total = data.abbTanzania + data.apex;
				return {
					abbTanzania: data.abbTanzania,
					apex: data.apex,
					abbTanzaniaPercentage: isCSC ? data.abbTanzaniaPercentage : calculatePercentage(data.abbTanzania, total),
					apexPercentage: isCSC ? data.apexPercentage : calculatePercentage(data.apex, total),
				};
			}
			case "primary_voltage": {
				const total = data.v15 + data.v19 + data.v33 + data.other;
				return {
					v15: data.v15,
					v19: data.v19,
					v33: data.v33,
					other: data.other,
					v15Percentage: isCSC ? data.v15Percentage : calculatePercentage(data.v15, total),
					v19Percentage: isCSC ? data.v19Percentage : calculatePercentage(data.v19, total),
					v33Percentage: isCSC ? data.v33Percentage : calculatePercentage(data.v33, total),
					otherPercentage: isCSC ? data.otherPercentage : calculatePercentage(data.other, total),
				};
			}
			case "vector_group": {
				const total = data.dy1 + data.dy5 + data.dy11 + data.other;
				return {
					dy1: data.dy1,
					dy5: data.dy5,
					dy11: data.dy11,
					other: data.other,
					dy1Percentage: isCSC ? data.dy1Percentage : calculatePercentage(data.dy1, total),
					dy5Percentage: isCSC ? data.dy5Percentage : calculatePercentage(data.dy5, total),
					dy11Percentage: isCSC ? data.dy11Percentage : calculatePercentage(data.dy11, total),
					otherPercentage: isCSC ? data.otherPercentage : calculatePercentage(data.other, total),
				};
			}
			case "manufacturing_year": {
				const total = data.y2020 + data.y2021 + data.y2022;
				return {
					y2020: data.y2020,
					y2021: data.y2021,
					y2022: data.y2022,
					y2020Percentage: isCSC ? data.y2020Percentage : calculatePercentage(data.y2020, total),
					y2021Percentage: isCSC ? data.y2021Percentage : calculatePercentage(data.y2021, total),
					y2022Percentage: isCSC ? data.y2022Percentage : calculatePercentage(data.y2022, total),
				};
			}
			default:
				return {};
		}
	};

	useEffect(() => {
		fetchAnalysisData(activeTab);
	}, [activeTab]);

	// Function to get data based on tab key
	const getDataForTab = () => data;

	// Define columns for each tab
	const getColumns = (tabKey: string): ColumnsType<TableData> => {
		const baseColumns = [
			{
				title: "Region",
				dataIndex: "region",
				key: "region",
				fixed: "right",
			},
			{
				title: "CSC",
				dataIndex: "csc",
				key: "csc",
				fixed: "right",
			},
		];

		const specificColumns = {
			transformer_types: [
				{
					title: "Conservator",
					children: [
						{
							title: "Count",
							dataIndex: "conservator",
							key: "conservator",
						},
						{
							title: "%",
							dataIndex: "conservatorPercentage",
							key: "conservatorPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Hermetical",
					children: [
						{
							title: "Count",
							dataIndex: "hermetical",
							key: "hermetical",
						},
						{
							title: "%",
							dataIndex: "hermeticalPercentage",
							key: "hermeticalPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Compact",
					children: [
						{
							title: "Count",
							dataIndex: "compact",
							key: "compact",
						},
						{
							title: "%",
							dataIndex: "compactPercentage",
							key: "compactPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Total",
					children: [
						{
							title: "Count",
							key: "transformerTotal",
							render: (_, record) => record.conservator + record.hermetical + record.compact,
						},
						{
							title: "%",
							key: "transformerTotalPercentage",
							render: () => "100%",
						},
					],
				},
			],
			capacity: [
				{
					title: "10 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva10",
							key: "kva10",
						},
						{
							title: "%",
							dataIndex: "kva10Percentage",
							key: "kva10Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "25 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva25",
							key: "kva25",
						},
						{
							title: "%",
							dataIndex: "kva25Percentage",
							key: "kva25Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "50 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva50",
							key: "kva50",
						},
						{
							title: "%",
							dataIndex: "kva50Percentage",
							key: "kva50Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "100 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva100",
							key: "kva100",
						},
						{
							title: "%",
							dataIndex: "kva100Percentage",
							key: "kva100Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "200 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva200",
							key: "kva200",
						},
						{
							title: "%",
							dataIndex: "kva200Percentage",
							key: "kva200Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "315 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva315",
							key: "kva315",
						},
						{
							title: "%",
							dataIndex: "kva315Percentage",
							key: "kva315Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "400 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva400",
							key: "kva400",
						},
						{
							title: "%",
							dataIndex: "kva400Percentage",
							key: "kva400Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "500 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva500",
							key: "kva500",
						},
						{
							title: "%",
							dataIndex: "kva500Percentage",
							key: "kva500Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "630 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva630",
							key: "kva630",
						},
						{
							title: "%",
							dataIndex: "kva630Percentage",
							key: "kva630Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "800 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva800",
							key: "kva800",
						},
						{
							title: "%",
							dataIndex: "kva800Percentage",
							key: "kva800Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "1250 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva1250",
							key: "kva1250",
						},
						{
							title: "%",
							dataIndex: "kva1250Percentage",
							key: "kva1250Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "2500 kVA",
					children: [
						{
							title: "Count",
							dataIndex: "kva2500",
							key: "kva2500",
						},
						{
							title: "%",
							dataIndex: "kva2500Percentage",
							key: "kva2500Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Other",
					children: [
						{
							title: "Count",
							dataIndex: "kvaOther",
							key: "kvaOther",
						},
						{
							title: "%",
							dataIndex: "kvaOtherPercentage",
							key: "kvaOtherPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Total",
					children: [
						{
							title: "Count",
							key: "capacityTotal",
							render: (_, record) => {
								return (
									record.kva10 +
									record.kva25 +
									record.kva50 +
									record.kva100 +
									record.kva200 +
									record.kva315 +
									record.kva400 +
									record.kva500 +
									record.kva630 +
									record.kva800 +
									record.kva1250 +
									record.kva2500 +
									record.kvaOther
								);
							},
						},
						{
							title: "%",
							key: "capacityTotalPercentage",
							render: () => "100%",
						},
					],
				},
			],
			cooling_type: [
				{
					title: "ONAN",
					children: [
						{
							title: "Count",
							dataIndex: "onan",
							key: "onan",
						},
						{
							title: "%",
							dataIndex: "onanPercentage",
							key: "onanPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Dry Type",
					children: [
						{
							title: "Count",
							dataIndex: "dryType",
							key: "dryType",
						},
						{
							title: "%",
							dataIndex: "dryTypePercentage",
							key: "dryTypePercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Total",
					children: [
						{
							title: "Count",
							key: "coolingTotal",
							render: (_, record) => record.onan + record.dryType,
						},
						{
							title: "%",
							key: "coolingTotalPercentage",
							render: () => "100%",
						},
					],
				},
			],
			manufacturer: [
				{
					title: "ABB Tanzania",
					children: [
						{
							title: "Count",
							dataIndex: "abbTanzania",
							key: "abbTanzania",
						},
						{
							title: "%",
							dataIndex: "abbTanzaniaPercentage",
							key: "abbTanzaniaPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Apex",
					children: [
						{
							title: "Count",
							dataIndex: "apex",
							key: "apex",
						},
						{
							title: "%",
							dataIndex: "apexPercentage",
							key: "apexPercentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Total",
					children: [
						{
							title: "Count",
							key: "manufacturerTotal",
							render: (_, record) => record.abbTanzania + record.apex,
						},
						{
							title: "%",
							key: "manufacturerTotalPercentage",
							render: () => "100%",
						},
					],
				},
			],
			primary_voltage: [
				{
					title: "15 kV",
					children: [
						{ title: "Count", dataIndex: "v15", key: "v15" },
						{ title: "%", dataIndex: "v15Percentage", key: "v15Percentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "19 kV",
					children: [
						{ title: "Count", dataIndex: "v19", key: "v19" },
						{ title: "%", dataIndex: "v19Percentage", key: "v19Percentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "33 kV",
					children: [
						{ title: "Count", dataIndex: "v33", key: "v33" },
						{ title: "%", dataIndex: "v33Percentage", key: "v33Percentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "Other",
					children: [
						{ title: "Count", dataIndex: "other", key: "other" },
						{ title: "%", dataIndex: "otherPercentage", key: "otherPercentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "Total",
					children: [
						{ title: "Count", key: "voltageTotal", render: (_, record) => record.v15 + record.v19 + record.v33 + record.other },
						{ title: "%", key: "voltageTotalPercentage", render: () => "100%" },
					],
				},
			],
			vector_group: [
				{
					title: "DY1",
					children: [
						{ title: "Count", dataIndex: "dy1", key: "dy1" },
						{ title: "%", dataIndex: "dy1Percentage", key: "dy1Percentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "DY5",
					children: [
						{ title: "Count", dataIndex: "dy5", key: "dy5" },
						{ title: "%", dataIndex: "dy5Percentage", key: "dy5Percentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "DY11",
					children: [
						{ title: "Count", dataIndex: "dy11", key: "dy11" },
						{ title: "%", dataIndex: "dy11Percentage", key: "dy11Percentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "Other",
					children: [
						{ title: "Count", dataIndex: "other", key: "other" },
						{ title: "%", dataIndex: "otherPercentage", key: "otherPercentage", render: (value: number) => `${value}%` },
					],
				},
				{
					title: "Total",
					children: [
						{ 
							title: "Count", 
							key: "vectorTotal", 
							render: (_, record) => record.dy1 + record.dy5 + record.dy11 + record.other 
						},
						{ 
							title: "%", 
							key: "vectorTotalPercentage", 
							render: () => "100%" 
						},
					],
				},
			],
			manufacturing_year: [
				{
					title: "2020",
					children: [
						{ title: "Count", dataIndex: "y2020", key: "y2020" },
						{
							title: "%",
							dataIndex: "y2020Percentage",
							key: "y2020Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "2021",
					children: [
						{ title: "Count", dataIndex: "y2021", key: "y2021" },
						{
							title: "%",
							dataIndex: "y2021Percentage",
							key: "y2021Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "2022",
					children: [
						{ title: "Count", dataIndex: "y2022", key: "y2022" },
						{
							title: "%",
							dataIndex: "y2022Percentage",
							key: "y2022Percentage",
							render: (value: number) => `${value}%`,
						},
					],
				},
				{
					title: "Total",
					children: [
						{ title: "Count", key: "yearTotal", render: (_, record) => record.y2020 + record.y2021 + record.y2022 },
						{ title: "%", key: "yearTotalPercentage", render: () => "100%" },
					],
				},
			],
		};

		return [...baseColumns, ...(specificColumns[tabKey] || [])];
	};

	// Calculate summary data for each tab
	const getSummaryRow = (tabKey: string): TableData => {
		const summary = mockData.reduce(
			(acc, curr) => {
				switch (tabKey) {
					case "transformer_types":
						return {
							...acc,
							conservator: (acc.conservator || 0) + curr.conservator,
							hermetical: (acc.hermetical || 0) + curr.hermetical,
							compact: (acc.compact || 0) + curr.compact,
						};
					case "capacity":
						return {
							...acc,
							kva10: (acc.kva10 || 0) + curr.kva10,
							kva25: (acc.kva25 || 0) + curr.kva25,
							kva50: (acc.kva50 || 0) + curr.kva50,
							kva100: (acc.kva100 || 0) + curr.kva100,
							kva200: (acc.kva200 || 0) + curr.kva200,
							kva315: (acc.kva315 || 0) + curr.kva315,
							kva400: (acc.kva400 || 0) + curr.kva400,
							kva500: (acc.kva500 || 0) + curr.kva500,
							kva630: (acc.kva630 || 0) + curr.kva630,
							kva800: (acc.kva800 || 0) + curr.kva800,
							kva1250: (acc.kva1250 || 0) + curr.kva1250,
							kva2500: (acc.kva2500 || 0) + curr.kva2500,
							kvaOther: (acc.kvaOther || 0) + curr.kvaOther,
						};
					case "cooling_type":
						return {
							...acc,
							onan: (acc.onan || 0) + curr.onan,
							dryType: (acc.dryType || 0) + curr.dryType,
						};
					case "manufacturer":
						return {
							...acc,
							abbTanzania: (acc.abbTanzania || 0) + curr.abbTanzania,
							apex: (acc.apex || 0) + curr.apex,
						};
					default:
						return acc;
				}
			},
			{} as Partial<TableData>,
		);

		return {
			key: "total",
			region: "Total",
			csc: "",
			...mockData[0], // Copy structure from first row
			...summary, // Override with calculated sums
		} as TableData;
	};

	// Add this function to handle Grand Total row rendering
	const renderGrandTotalRow = (tabKey: string, data: TableData[]) => {
		// Only include region-level data (exclude CSC data)
		const regionData = data.filter((item: any) => !item.csc);

		// Calculate grand totals using only region data
		const grandTotal = regionData.reduce((acc, curr) => {
			switch (tabKey) {
				case "transformer_types":
					return {
						conservator: (acc.conservator || 0) + (curr.conservator || 0),
						hermetical: (acc.hermetical || 0) + (curr.hermetical || 0),
						compact: (acc.compact || 0) + (curr.compact || 0),
					};
				case "capacity":
					return {
						kva10: (acc.kva10 || 0) + (curr.kva10 || 0),
						kva25: (acc.kva25 || 0) + (curr.kva25 || 0),
						kva50: (acc.kva50 || 0) + (curr.kva50 || 0),
						kva100: (acc.kva100 || 0) + (curr.kva100 || 0),
						kva200: (acc.kva200 || 0) + (curr.kva200 || 0),
						kva315: (acc.kva315 || 0) + (curr.kva315 || 0),
						kva400: (acc.kva400 || 0) + (curr.kva400 || 0),
						kva500: (acc.kva500 || 0) + (curr.kva500 || 0),
						kva630: (acc.kva630 || 0) + (curr.kva630 || 0),
						kva800: (acc.kva800 || 0) + (curr.kva800 || 0),
						kva1250: (acc.kva1250 || 0) + (curr.kva1250 || 0),
						kva2500: (acc.kva2500 || 0) + (curr.kva2500 || 0),
						other: (acc.other || 0) + (curr.kvaOther || 0),
					};
				case "cooling_type":
					return {
						onan: (acc.onan || 0) + (curr.onan || 0),
						dryType: (acc.dryType || 0) + (curr.dryType || 0),
					};
				case "manufacturer":
					return {
						abbTanzania: (acc.abbTanzania || 0) + (curr.abbTanzania || 0),
						apex: (acc.apex || 0) + (curr.apex || 0),
					};
				case "primary_voltage":
					return {
						v15: (acc.v15 || 0) + (curr.v15 || 0),
						v19: (acc.v19 || 0) + (curr.v19 || 0),
						v33: (acc.v33 || 0) + (curr.v33 || 0),
						other: (acc.other || 0) + (curr.other || 0),
					};
				case "vector_group":
					return {
						dy1: (acc.dy1 || 0) + (curr.dy1 || 0),
						dy5: (acc.dy5 || 0) + (curr.dy5 || 0),
						dy11: (acc.dy11 || 0) + (curr.dy11 || 0),
						other: (acc.other || 0) + (curr.other || 0),
					};
				case "manufacturing_year":
					return {
						y2020: (acc.y2020 || 0) + (curr.y2020 || 0),
						y2021: (acc.y2021 || 0) + (curr.y2021 || 0),
						y2022: (acc.y2022 || 0) + (curr.y2022 || 0),
					};
				default:
					return acc;
			}
		}, {} as any);

		// Calculate percentages for grand totals
		const calculatePercentages = () => {
			switch (tabKey) {
				case "transformer_types": {
					const total = grandTotal.conservator + grandTotal.hermetical + grandTotal.compact;
					return {
						conservatorPercentage: ((grandTotal.conservator / total) * 100).toFixed(1),
						hermeticalPercentage: ((grandTotal.hermetical / total) * 100).toFixed(1),
						compactPercentage: ((grandTotal.compact / total) * 100).toFixed(1),
					};
				}
				case "capacity": {
					const total =
						grandTotal.kva10 +
						grandTotal.kva25 +
						grandTotal.kva50 +
						grandTotal.kva100 +
						grandTotal.kva200 +
						grandTotal.kva315 +
						grandTotal.kva400 +
						grandTotal.kva500 +
						grandTotal.kva630 +
						grandTotal.kva800 +
						grandTotal.kva1250 +
						grandTotal.kva2500 +
						grandTotal.other;
					return {
						kva10Percentage: ((grandTotal.kva10 / total) * 100).toFixed(1),
						kva25Percentage: ((grandTotal.kva25 / total) * 100).toFixed(1),
						kva50Percentage: ((grandTotal.kva50 / total) * 100).toFixed(1),
						kva100Percentage: ((grandTotal.kva100 / total) * 100).toFixed(1),
						kva200Percentage: ((grandTotal.kva200 / total) * 100).toFixed(1),
						kva315Percentage: ((grandTotal.kva315 / total) * 100).toFixed(1),
						kva400Percentage: ((grandTotal.kva400 / total) * 100).toFixed(1),
						kva500Percentage: ((grandTotal.kva500 / total) * 100).toFixed(1),
						kva630Percentage: ((grandTotal.kva630 / total) * 100).toFixed(1),
						kva800Percentage: ((grandTotal.kva800 / total) * 100).toFixed(1),
						kva1250Percentage: ((grandTotal.kva1250 / total) * 100).toFixed(1),
						kva2500Percentage: ((grandTotal.kva2500 / total) * 100).toFixed(1),
						otherPercentage: ((grandTotal.other / total) * 100).toFixed(1),
					};
				}
				case "cooling_type": {
					const total = grandTotal.onan + grandTotal.dryType;
					return {
						onanPercentage: ((grandTotal.onan / total) * 100).toFixed(1),
						dryTypePercentage: ((grandTotal.dryType / total) * 100).toFixed(1),
					};
				}
				case "manufacturer": {
					const total = grandTotal.abbTanzania + grandTotal.apex;
					return {
						abbTanzaniaPercentage: ((grandTotal.abbTanzania / total) * 100).toFixed(1),
						apexPercentage: ((grandTotal.apex / total) * 100).toFixed(1),
					};
				}
				case "primary_voltage": {
					const total = grandTotal.v15 + grandTotal.v19 + grandTotal.v33 + grandTotal.other;
					return {
						v15Percentage: ((grandTotal.v15 / total) * 100).toFixed(1),
						v19Percentage: ((grandTotal.v19 / total) * 100).toFixed(1),
						v33Percentage: ((grandTotal.v33 / total) * 100).toFixed(1),
						otherPercentage: ((grandTotal.other / total) * 100).toFixed(1),
					};
				}
				case "vector_group": {
					const total = grandTotal.dy1 + grandTotal.dy5 + grandTotal.dy11 + grandTotal.other;
					return {
						dy1Percentage: ((grandTotal.dy1 / total) * 100).toFixed(1),
						dy5Percentage: ((grandTotal.dy5 / total) * 100).toFixed(1),
						dy11Percentage: ((grandTotal.dy11 / total) * 100).toFixed(1),
						otherPercentage: ((grandTotal.other / total) * 100).toFixed(1),
					};
				}
				case "manufacturing_year": {
					const total = grandTotal.y2020 + grandTotal.y2021 + grandTotal.y2022;
					return {
						y2020Percentage: ((grandTotal.y2020 / total) * 100).toFixed(1),
						y2021Percentage: ((grandTotal.y2021 / total) * 100).toFixed(1),
						y2022Percentage: ((grandTotal.y2022 / total) * 100).toFixed(1),
					};
				}
				default:
					return {};
			}
		};

		const percentages = calculatePercentages();

		return (
			<Table.Summary.Row>
				<Table.Summary.Cell index={0}>Grand Total</Table.Summary.Cell>
				<Table.Summary.Cell index={1}></Table.Summary.Cell>
				{/* Add cells based on the tab */}
				{getGrandTotalCells(tabKey, grandTotal, percentages)}
			</Table.Summary.Row>
		);
	};

	// Modify the Table component in each TabPane
	const renderTable = (tabKey: string) => {
		const data = getDataForTab(tabKey);

		return (
			<Table
				columns={getColumns(tabKey)}
				dataSource={data}
				pagination={false}
				bordered
				size="middle"
				scroll={{ x: "max-content" }}
				className="shadow-lg"
				summary={(currentData) => renderGrandTotalRow(tabKey, currentData as TableData[])}
			/>
		);
	};

	// Modify the renderContent function to include both table and graph
	const renderContent = (tabKey: string) => {
		const data = getDataForTab(tabKey);

		return (
			<div>
				<Scrollbar>{renderTable(tabKey)}</Scrollbar>
				<div className="flex flex-wrap justify-center gap-4 mt-4">
					<TransformerGraph tabKey={tabKey} data={data} />
					{/* <TransformerPieChart tabKey={tabKey} data={data} /> */}
				</div>
			</div>
		);
	};

	if (loading) {
		return (
			<div className="h-[400px]">
				<CircleLoading />
			</div>
		);
	}

	return (
		<div className="w-full p-4">
			<h2 className="text-xl font-bold mb-4">Transformer Analysis</h2>
			<Tabs activeKey={activeTab} onChange={setActiveTab}>
				<TabPane tab="Transformer Types" key="transformer_types">
					{renderContent("transformer_types")}
				</TabPane>
				<TabPane tab="Capacity" key="capacity">
					{renderContent("capacity")}
				</TabPane>
				<TabPane tab="Cooling Type" key="cooling_type">
					{renderContent("cooling_type")}
				</TabPane>
				{/* <TabPane tab="Manufacturer" key="manufacturer">
					{renderContent("manufacturer")}
				</TabPane> */}
				<TabPane tab="Primary Voltage" key="primary_voltage">
					{renderContent("primary_voltage")}
				</TabPane>
				<TabPane tab="Vector Group" key="vector_group">
					{renderContent("vector_group")}
				</TabPane>
				{/* <TabPane tab="Year of Manufacturing" key="manufacturing_year">
					{renderContent("manufacturing_year")}
				</TabPane> */}
			</Tabs>
		</div>
	);
};

const getGrandTotalCells = (tabKey: string, grandTotal: any, percentages: any) => {
	const getRowTotal = () => {
		switch (tabKey) {
			case "transformer_types":
				return grandTotal.conservator + grandTotal.hermetical + grandTotal.compact;
			case "capacity":
				return (
					grandTotal.kva10 +
					grandTotal.kva25 +
					grandTotal.kva50 +
					grandTotal.kva100 +
					grandTotal.kva200 +
					grandTotal.kva315 +
					grandTotal.kva400 +
					grandTotal.kva500 +
					grandTotal.kva630 +
					grandTotal.kva800 +
					grandTotal.kva1250 +
					grandTotal.kva2500 +
					grandTotal.other
				);
			case "cooling_type":
				return grandTotal.onan + grandTotal.dryType;
			case "manufacturer":
				return grandTotal.abbTanzania + grandTotal.apex;
			case "primary_voltage":
				return grandTotal.v15 + grandTotal.v19 + grandTotal.v33 + grandTotal.other;
			case "vector_group":
				return grandTotal.dy1 + grandTotal.dy5 + grandTotal.dy11 + grandTotal.other;
			case "manufacturing_year":
				return grandTotal.y2020 + grandTotal.y2021 + grandTotal.y2022;
			default:
				return 0;
		}
	};

	const rowTotal = getRowTotal();

	switch (tabKey) {
		case "transformer_types":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.conservator}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.conservatorPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.hermetical}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.hermeticalPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{grandTotal.compact}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>{percentages.compactPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={8}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={9}>100%</Table.Summary.Cell>
				</>
			);
		case "capacity":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.kva10}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.kva10Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.kva25}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.kva25Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{grandTotal.kva50}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>{percentages.kva50Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={8}>{grandTotal.kva100}</Table.Summary.Cell>
					<Table.Summary.Cell index={9}>{percentages.kva100Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={10}>{grandTotal.kva200}</Table.Summary.Cell>
					<Table.Summary.Cell index={11}>{percentages.kva200Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={12}>{grandTotal.kva315}</Table.Summary.Cell>
					<Table.Summary.Cell index={13}>{percentages.kva315Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={14}>{grandTotal.kva400}</Table.Summary.Cell>
					<Table.Summary.Cell index={15}>{percentages.kva400Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={16}>{grandTotal.kva500}</Table.Summary.Cell>
					<Table.Summary.Cell index={17}>{percentages.kva500Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={18}>{grandTotal.kva630}</Table.Summary.Cell>
					<Table.Summary.Cell index={19}>{percentages.kva630Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={20}>{grandTotal.kva800}</Table.Summary.Cell>
					<Table.Summary.Cell index={21}>{percentages.kva800Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={22}>{grandTotal.kva1250}</Table.Summary.Cell>
					<Table.Summary.Cell index={23}>{percentages.kva1250Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={24}>{grandTotal.kva2500}</Table.Summary.Cell>
					<Table.Summary.Cell index={25}>{percentages.kva2500Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={26}>{grandTotal.other}</Table.Summary.Cell>
					<Table.Summary.Cell index={27}>{percentages.otherPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={28}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={29}>100%</Table.Summary.Cell>
				</>
			);
		case "cooling_type":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.onan}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.onanPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.dryType}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.dryTypePercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
				</>
			);
		case "manufacturer":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.abbTanzania}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.abbTanzaniaPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.apex}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.apexPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
				</>
			);
		case "primary_voltage":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.v15}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.v15Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.v19}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.v19Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{grandTotal.v33}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>{percentages.v33Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={8}>{grandTotal.other}</Table.Summary.Cell>
					<Table.Summary.Cell index={9}>{percentages.otherPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={10}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={11}>100%</Table.Summary.Cell>
				</>
			);
		case "vector_group":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.dy1}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.dy1Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.dy5}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.dy5Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{grandTotal.dy11}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>{percentages.dy11Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={8}>{grandTotal.other}</Table.Summary.Cell>
					<Table.Summary.Cell index={9}>{percentages.otherPercentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={10}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={11}>100%</Table.Summary.Cell>
				</>
			);
		case "manufacturing_year":
			return (
				<>
					<Table.Summary.Cell index={2}>{grandTotal.y2020}</Table.Summary.Cell>
					<Table.Summary.Cell index={3}>{percentages.y2020Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={4}>{grandTotal.y2021}</Table.Summary.Cell>
					<Table.Summary.Cell index={5}>{percentages.y2021Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={6}>{grandTotal.y2022}</Table.Summary.Cell>
					<Table.Summary.Cell index={7}>{percentages.y2022Percentage}%</Table.Summary.Cell>
					<Table.Summary.Cell index={8}>{rowTotal}</Table.Summary.Cell>
					<Table.Summary.Cell index={9}>100%</Table.Summary.Cell>
				</>
			);
		default:
			return null;
	}
};

export default TransformerAnalisis;


