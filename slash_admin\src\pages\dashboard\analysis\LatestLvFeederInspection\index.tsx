import React, { useEffect, useState, useCallback } from "react";
import Table, { type ColumnsType } from "antd/es/table";
import type { TablePaginationConfig } from "antd/es/table";
import type { FilterValue, SorterResult } from "antd/es/table/interface";
import transformerService from "@/api/services/transformerService";
import { Button, Card, Popconfirm } from "antd";
import { IconButton, Iconify } from "@/components/icon";
import type { LvFeeder } from "#/entity";
import { toast } from "sonner";
import { usePathname, useRouter } from "@/router/hooks";
import { formatDate } from "@fullcalendar/core/index.js";
import { FilterOutlined } from '@ant-design/icons';
import FilterForm from './FilterForm';
import { debounce } from 'lodash';
import * as XLSX from "xlsx";
import dayjs from 'dayjs';

interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue>;
}

function LatestLvFeederInspection() {
  const { push } = useRouter();
  const pathname = usePathname();
  const [data, setData] = useState<LvFeeder[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [activeFilters, setActiveFilters] = useState({});
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
    },
  });

  const handleFiltersUpdate = useCallback(
    debounce(async (filters: any, currentPage?: number, pageSize?: number) => {
      const formattedParams = {
        ...filters,
        page: currentPage || tableParams.pagination?.current || 1,
        pageSize: pageSize || tableParams.pagination?.pageSize || 10,
        searchType: "LatestLvFeederInspection",
      };

      const cleanParams = (params: any) => {
        return Object.fromEntries(
          Object.entries(params).filter(([_, value]) => value != null && value !== '')
        );
      };

      const activeFilterss = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value != null && value !== '')
      );

      const cleanFilters = cleanParams(formattedParams);
    
      try {
        setLoading(true);
        const response = await transformerService.getBasestationsFiltered(cleanFilters);
        if (response) {
          setData(response.results);
          setTableParams(prev => ({
            ...prev,
            pagination: {
              ...prev.pagination,
              current: currentPage || prev.pagination?.current || 1,
              pageSize: pageSize || prev.pagination?.pageSize || 10,
              total: response.count || 0,
            },
          }));
          setActiveFilters(activeFilterss);
        }
      } catch (error) {
        console.error("Error fetching filtered data:", error);
        toast.error("Failed to apply filters");
      } finally {
        setLoading(false);
      }
    }, 300),
    [tableParams.pagination]
  );

  useEffect(() => {
    handleFiltersUpdate({});
  }, []);

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue>,
    sorter: SorterResult<LvFeeder>
  ) => {
    setTableParams({
      pagination,
      filters,
      sortField: sorter.field as string,
      sortOrder: sorter.order,
    });
    handleFiltersUpdate(activeFilters, pagination.current, pagination.pageSize);
  };

  const handleFilterChange = (values: any) => {
    setActiveFilters(values);
  };

  const handleApplyFilters = () => {
    handleFiltersUpdate(activeFilters);
  };

  const handleResetFilters = () => {
    setActiveFilters({});
    handleFiltersUpdate({});
  };

  const columns: ColumnsType<LvFeeder> = [
    {
      title: "Transformer ID",
      dataIndex: "inspection_data",
      width: 150,
      render: (inspection_data) => inspection_data?.transformer_data,
    },
    {
      title: "Distribution Box",
      dataIndex: "distribution_box_name",
      width: 150,
    },
    {
      title: "R Load Current",
      dataIndex: "R_load_current",
      width: 150,
    },
    {
      title: "S Load Current",
      dataIndex: "S_load_current",
      width: 150,
    },
    {
      title: "T Load Current",
      dataIndex: "T_load_current",
      width: 150,
    },
    {
      title: "Transformer Load",
      dataIndex: "transformer_load",
      width: 150,
    },
    {
      title: "Current Phase Unbalance",
      dataIndex: "current_phase_unbalance",
      width: 150,
    },
    {
      title: "Percentage of Neutral",
      dataIndex: "percentage_of_neutral",
      width: 150,
    },

    {
      title: "Created At",
      dataIndex: "created_at",
      width: 150,
      render: (date) => dayjs(date).format("DD-MM-YYYY HH:mm:ss"),
    },
    {
      title: "Action",
      key: "operation",
      align: "center",
      width: 120,
      render: (_, record) => (
        <div className="flex w-full justify-center text-gray">
          <IconButton
            onClick={() => {
              console.log(`${pathname}/${record.id}`);
              push(`/transformer/inspection/LvFeeder/${record.id}`);
            }}
          >
            <Iconify icon="carbon:view" size={18} />
          </IconButton>
        </div>
      ),
    },
  ];

  const handleExportExcel = () => {
  // Prepare data for export (flatten nested objects if needed)
  const exportData = data.map(item => ({
    "Distribution Box": item.distribution_box_name || "",
    "R Load Current": item.R_load_current || "",
    "S Load Current": item.S_load_current || "",
    "T Load Current": item.T_load_current || "",
    "Transformer Load": item.transformer_load || "",
    "Current Phase Unbalance": item.current_phase_unbalance || "",
    "Percentage of Neutral": item.percentage_of_neutral || "",
    "Created At": item.created_at ? new Date(item.created_at).toLocaleString() : "",
    "Updated At": item.updated_at ? new Date(item.updated_at).toLocaleString() : "",
    // Add more fields if needed
  }));

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "LatestLvFeeder");
  XLSX.writeFile(workbook, "LatestLvFeeder.xlsx");
};

  return (
    <Card
      title="Latest LV Feeder Inspections"
      extra={
        <>
        <Button
          icon={<FilterOutlined />}
          onClick={() => setShowFilters(!showFilters)}
        >
          Filters
        </Button>
         <Button onClick={handleExportExcel} className="ml-2">
                    Export to Excel
                  </Button>
                  </>
      }
    >
      {showFilters && (
        <div className="mt-4 bg-gray-50 p-4 rounded-lg">
          <FilterForm
            filters={activeFilters}
            onFilterChange={handleFilterChange}
            onReset={handleResetFilters}
            onApply={handleApplyFilters}
          />
        </div>
      )}
      <Table
        columns={columns}
        rowKey="id"
        dataSource={data}
        pagination={{
          ...tableParams.pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        loading={loading}
        onChange={handleTableChange}
      />
    </Card>
  );
}

export default LatestLvFeederInspection
