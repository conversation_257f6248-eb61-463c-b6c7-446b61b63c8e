import {
	<PERSON><PERSON>,
	<PERSON>,
	Col,
	Row,
	Space,
	Typography,
	Avatar,
	Select,
	Modal,
	Input,
	Spin,
	Empty,
	Upload,
	Image,
	Timeline,
} from "antd";
import { EditOutlined, UserOutlined, PictureOutlined, ClockCircleOutlined } from "@ant-design/icons";
import React, { useState, useEffect } from "react";
import { useParams } from "@/router/hooks";
import maintenanceService from "@/api/services/maintenanceService";
import { toast } from "sonner";
import dayjs from "dayjs";
import { getFullAvatarUrl } from "@/utils/url";
import type { UploadFile } from "antd/es/upload/interface";
import MaintenanceLogs from "./components/MaintenanceLogs";

const { TextArea } = Input;

interface MaintenanceUpdate {
	id: number;
	maintenance: number;
	user: string;
	user_name: string;
	user_avatar: string;
	message: string;
	timestamp: string;
	images: Array<{ id: number; image: string }>; // Add this for attached images
}

interface MaintenanceDetails {
	id: string;
	code: string;
	details: string;
	asset: string;
	type: string;
	priority: string;
	status: string;
	date_scheduled: string;
	date_due: string;
	date_created: string;
	date_completed: string | null;
	created_by_name: string;
	completed_by_name: string | null;
	assigned_to: number[];
	assigned_to_names: string[];
	updates?: MaintenanceUpdate[];
}

interface MaintenanceLog {
	id: number;
	change_type: string;
	old_value: string;
	new_value: string;
	timestamp: string;
	description: string;
	user_name: string;
}

const WorkOrderDetails: React.FC = () => {
	const { id } = useParams();
	const [isEditing, setIsEditing] = useState(false);
	const [maintenanceData, setMaintenanceData] = useState<MaintenanceDetails | null>(null);
	const [loading, setLoading] = useState(true);
	const [isUpdateModalVisible, setIsUpdateModalVisible] = useState(false);
	const [updateMessage, setUpdateMessage] = useState("");
	const [maintenanceUpdates, setMaintenanceUpdates] = useState<MaintenanceUpdate[]>([]);
	const [updatesLoading, setUpdatesLoading] = useState(true);
	const [updateLoading, setUpdateLoading] = useState(false); // Add this new state
	const [fileList, setFileList] = useState<UploadFile[]>([]);
	const [status, setStatus] = useState<string>("");
	const [isStatusUpdating, setIsStatusUpdating] = useState(false);
	const [logs, setLogs] = useState<MaintenanceLog[]>([]);
	const [logsLoading, setLogsLoading] = useState(false);

	// Fetch maintenance data and updates
	const fetchMaintenanceData = async () => {
		try {
			const response = await maintenanceService.getMaintenanceById(id);
			setMaintenanceData(response);
		} catch (error) {
			toast.error("Failed to fetch maintenance details");
			console.error("Error fetching maintenance details:", error);
			setLoading(false);
		}
	};

	// Fetch maintenance updates separately
	const fetchMaintenanceUpdates = async () => {
		if (!id) return;

		setUpdatesLoading(true);
		try {
			const response = await maintenanceService.getMaintenanceUpdates(id);
			setMaintenanceUpdates(response);
		} catch (error) {
			toast.error("Failed to fetch maintenance updates");
			console.error("Error fetching maintenance updates:", error);
		} finally {
			setUpdatesLoading(false);
		}
	};

	const fetchLogs = async () => {
		if (!id) return;

		try {
			setLogsLoading(true);
			const response = await maintenanceService.getMaintenanceLogs(id);
			setLogs(response);
		} catch (error) {
			console.error("Error fetching logs:", error);
			toast.error("Failed to fetch activity logs");
		} finally {
			setLogsLoading(false);
		}
	};

	useEffect(() => {
		if (id) {
			fetchMaintenanceData();
			fetchMaintenanceUpdates();
			fetchLogs();
		}
	}, [id]);

	const handleSaveUpdate = async () => {
		try {
			if (!id) {
				toast.error("Maintenance ID is missing");
				return;
			}

			if (!updateMessage.trim()) {
				toast.error("Update message cannot be empty");
				return;
			}

			setUpdateLoading(true);

			// Create FormData to handle both text and files
			const formData = new FormData();
			formData.append("message", updateMessage);

			// Append files if any
			fileList.forEach((file) => {
				if (file.originFileObj) {
					formData.append("images", file.originFileObj);
				}
			});

			// Pass the numeric ID directly
			await maintenanceService.createMaintenanceUpdate(id, formData);
			await fetchMaintenanceUpdates();

			setUpdateMessage("");
			setFileList([]);
			setIsUpdateModalVisible(false);
			toast.success("Update added successfully");
		} catch (error) {
			toast.error("Failed to add update");
			console.error("Error adding update:", error);
		} finally {
			setUpdateLoading(false);
		}
	};

	const handleFileChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
		setFileList(newFileList);
	};

	const handleStatusChange = async (newStatus: string) => {
		try {
			setIsStatusUpdating(true);
			await maintenanceService.updateMaintenance(id, { status: newStatus });
			setStatus(newStatus);
			await fetchLogs(); // Refresh logs after status change
			toast.success("Status updated successfully");
		} catch (error) {
			console.error("Error updating status:", error);
			toast.error("Failed to update status");
		} finally {
			setIsStatusUpdating(false);
		}
	};

	return (
		<>
			<Row gutter={24}>
				{/* Left Section: Work Order Details */}
				<Col span={12}>
					<Card title="Work Order Details">
						{/* Status Section */}
						<div className="mb-4">
							<Typography.Title level={5}>Status</Typography.Title>
							<Space>
								<Select
									value={maintenanceData?.status || "New"}
									style={{ width: 300 }}
									onChange={handleStatusChange}
									disabled={isStatusUpdating}
								>
									<Option value="New">New</Option>
									<Option value="Assigned">Assigned</Option>
									<Option value="In Progress">In Progress</Option>
									<Option value="Waiting For Parts">Waiting For Parts</Option>
									<Option value="Complete">Complete</Option>
								</Select>
								{isStatusUpdating && <Spin size="small" />}
							</Space>
						</div>

						{/* Details Section */}
						<div className="mb-4">
							<Typography.Title level={5}>Details</Typography.Title>
							<div className="space-y-3">
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Code:</span>
									<span>{maintenanceData?.code}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Details:</span>
									<span>{maintenanceData?.details}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Type:</span>
									<span>{maintenanceData?.type}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Priority:</span>
									<Button type="primary">{maintenanceData?.priority}</Button>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Date Scheduled:</span>
									<span>
										{maintenanceData?.date_scheduled
											? dayjs(maintenanceData.date_scheduled).format("MMM DD, YYYY h:mm A")
											: "Not scheduled"}
									</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Date Due:</span>
									<span>{dayjs(maintenanceData?.date_due).format("MMM DD, YYYY h:mm A")}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Est Hours:</span>
									<span>2 Hour(s)</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Assigned To:</span>
									<span>{maintenanceData?.assigned_to_names?.join(", ") || "No users assigned"}</span>
								</div>
							</div>
						</div>

						{/* Worker Details Section */}
						<div className="mb-4">
							<Typography.Title level={5}>Worker Details</Typography.Title>
							<div className="space-y-3">
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Created By:</span>
									<span>{maintenanceData?.created_by_name || "N/A"}</span>
								</div>
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Assigned To:</span>
									<span>{maintenanceData?.assigned_to_names?.join(", ") || "No users assigned"}</span>
								</div>
								{maintenanceData?.status === "Complete" && (
									<div className="flex items-center gap-2">
										<span className="font-medium min-w-[100px]">Completed By:</span>
										<span>{maintenanceData?.completed_by_name || "N/A"}</span>
									</div>
								)}
								<div className="flex items-center gap-2">
									<span className="font-medium min-w-[100px]">Date Created:</span>
									<span>
										{maintenanceData?.date_created ? dayjs(maintenanceData.date_created).format("MMM DD, YYYY") : "N/A"}
									</span>
								</div>
								{maintenanceData?.status === "Complete" && (
									<div className="flex items-center gap-2">
										<span className="font-medium min-w-[100px]">Date Completed:</span>
										<span>
											{maintenanceData?.date_completed
												? dayjs(maintenanceData.date_completed).format("MMM DD, YYYY")
												: "N/A"}
										</span>
									</div>
								)}
							</div>
						</div>
					</Card>
				</Col>

				{/* Right Section: Updates */}
				<Col span={12}>
					<Card
						title="UPDATES"
						extra={
							<Button type="link" onClick={() => setIsUpdateModalVisible(true)}>
								Add Update
							</Button>
						}
					>
						{updatesLoading ? (
							<div className="text-center py-4">
								<Spin />
							</div>
						) : (
							<ul className="space-y-4">
								{maintenanceUpdates.length > 0 ? (
									maintenanceUpdates.map((update) => (
										<li key={update.id} className="flex items-start gap-3">
											<Avatar
												size={40}
												src={getFullAvatarUrl(update.user_avatar)}
												style={{ backgroundColor: "#697bff" }}
												icon={<UserOutlined />}
											/>
											<div className="flex-1">
												<Typography.Text strong>{update.user_name}</Typography.Text>
												<p className="my-1">{update.message}</p>
												{update.images && update.images.length > 0 && (
													<div className="my-2">
														<Image.PreviewGroup>
															<Space size={8}>
																{update.images.map((img) => (
																	<Image
																		key={img.id}
																		src={getFullAvatarUrl(img.image)}
																		width={100}
																		height={100}
																		style={{ objectFit: "cover", borderRadius: "8px" }}
																		alt="Update attachment"
																	/>
																))}
															</Space>
														</Image.PreviewGroup>
													</div>
												)}
												<Typography.Text type="secondary">
													{dayjs(update.timestamp).format("MMM DD, YYYY h:mm A")}
												</Typography.Text>
											</div>
										</li>
									))
								) : (
									<Empty description="No updates available" />
								)}
							</ul>
						)}
					</Card>
				</Col>

				<Col span={12}>
					<MaintenanceLogs logs={logs} loading={logsLoading} />
				</Col>
			</Row>

			{/* Add Update Modal */}
			<Modal
				title="Add Work Order Update"
				open={isUpdateModalVisible}
				onCancel={() => {
					setIsUpdateModalVisible(false);
					setUpdateMessage("");
					setFileList([]);
				}}
				onOk={handleSaveUpdate}
				okText="Save"
				confirmLoading={updateLoading}
			>
				<div className="space-y-4">
					<TextArea
						rows={4}
						placeholder="Enter work order update details..."
						value={updateMessage}
						onChange={(e) => setUpdateMessage(e.target.value)}
					/>

					<Upload
						listType="picture-card"
						fileList={fileList}
						onChange={handleFileChange}
						multiple
						beforeUpload={() => false} // Prevent auto upload
						maxCount={5} // Maximum 5 images
						accept="image/*" // Accept only images
					>
						{fileList.length >= 5 ? null : (
							<div>
								<PictureOutlined />
								<div style={{ marginTop: 8 }}>Upload</div>
							</div>
						)}
					</Upload>
				</div>
			</Modal>
		</>
	);
};

export default WorkOrderDetails;
