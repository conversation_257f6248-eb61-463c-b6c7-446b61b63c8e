from django.core.management.base import BaseCommand
from account.models import Region, CSCCenter, Substation, Feeder


class Command(BaseCommand):
    help = 'Insert region, CSC centers, substations, and feeders data into the database'

    def handle(self, *args, **kwargs):
        # First, delete all existing data
        try:
            self.stdout.write("Deleting existing data...")
            
            # Delete in reverse order to avoid foreign key constraints
            self.stdout.write("Deleting Feeder records...")
            Feeder.objects.all().delete()
            
            self.stdout.write("Deleting Substation records...")
            Substation.objects.all().delete()
            
            self.stdout.write("Deleting CSCCenter records...")
            CSCCenter.objects.all().delete()
            
            self.stdout.write("Deleting Region records...")
            Region.objects.all().delete()
            
            self.stdout.write(self.style.SUCCESS("Successfully deleted all existing data"))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error while deleting data: {str(e)}')
            )
            return
        
        from ...combine.region_data import region_data
        from ...combine.new_substation_data import substation_data


        # Merge the datasets
        def merge_data(region_data, substations_data):
            merged_data = []

            for region in region_data:
                region_name = region["name"]
                merged_region = {
                    "CSC Code": region["CSC Code"],
                    "name": region_name,
                    "children": {
                        "csc_centers": region["children"],
                        "substations": []
                    }
                }

                # Find matching substations for this region
                for substation_region in substations_data:
                    if region_name == substation_region["name"]:
                        merged_region["children"]["substations"] = substation_region["substations"]
                        break

                merged_data.append(merged_region)

            return merged_data

        # Perform the merge
        merged_result = merge_data(region_data, substation_data)

        # Output the result
        import json
        # print(json.dumps(merged_result, indent=4))

        # Insert data into the database
        try:
            for region_data in merged_result:
                # Create or get the region
                region, created = Region.objects.get_or_create(
                    csc_code=region_data["CSC Code"],
                    name=region_data["name"]
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully created region: {region.name}')
                )

                # Create CSC centers
                for csc_data in region_data["children"]["csc_centers"]:
                    csc = CSCCenter.objects.create(
                        csc_code=csc_data["CSC Code"],
                        name=csc_data["name"],
                        region=region
                    )
                    self.stdout.write(
                        self.style.SUCCESS(f'Created CSC center: {csc.name}')
                    )

                # Create substations and feeders
                for substation_data in region_data["children"]["substations"]:
                    substation = Substation.objects.create(
                        name=substation_data["name"],
                        region=region
                    )
                    self.stdout.write(
                        self.style.SUCCESS(f'Created substation: {substation.name}')
                    )

                    # Create feeders for each substation
                    for feeder_data in substation_data["feeders"]:
                        feeder = Feeder.objects.create(
                            feeder_name=feeder_data["feeder_name"],  # feeder_name is required
                            substation=substation,                    # substation is required
                            voltage_level=feeder_data.get("voltage_level", None),  # None if missing
                            peak_load=feeder_data.get("peak_load", None),           # None if missing
                            length=feeder_data.get("length", None),                 # None if missing
                            number_of_transformer=feeder_data.get("number_of_transformer", None)  # None if missing
                        )
                        self.stdout.write(
                            self.style.SUCCESS(f'Created feeder: {feeder.feeder_name}')
                        )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error occurred during data insertion: {str(e)}')
            )
