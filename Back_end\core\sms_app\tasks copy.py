from celery import shared_task
# from django.contrib.auth import get_user_model
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import logging
import time
import socket
import re
from datetime import timedelta
from django.conf import settings
import smpplib.client
import smpplib.consts as consts
from smpplib.client import Client
from account.models import User

from .models import (
    SMSMessage, Notification, Analytics, TimelineEvent,
    ProgressReport, ApprovalRequest, Reply, Department,
    Attachment, ChecklistItem, ProgressUpdate,
    ApprovalHistory, AssignmentStatus, DetailedReport,
    MessageTemplate, SystemSettings, AssignmentHistory
)

# User = get_user_model()
logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()


# @shared_task
# def listen_for_sms():
#     """
#     Celery task to listen for incoming SMS and delivery reports continuously.
#     Maintains an SMPP transceiver binding and processes deliver_sm PDUs.
#     """
#     try:
#         # Initialize SMPP client
#         client = smpplib.client.Client(
#             settings.SMPP_CONFIG['HOST'],
#             settings.SMPP_CONFIG['PORT'],
#             timeout=1.0
#         )
#         client.connect()
#         client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             interface_version=0x34,  # SMPP v3.4
#             addr_ton=consts.SMPP_TON_NATNL,
#             addr_npi=consts.SMPP_NPI_ISDN,
#             address_range=''
#         )
#         logger.info(f"Connected and bound to SMSC {settings.SMPP_CONFIG['HOST']}:{settings.SMPP_CONFIG['PORT']}")

#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 try:
#                     message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
#                 except Exception:
#                     message = str(pdu.short_message)
#                 logger.info(f"Received deliver_sm from {pdu.source_addr}: {message}")

#                 # Check if this is a delivery receipt (esm_class & 0x0C == 0x04 or 0x08 per SMPP spec)
#                 if pdu.esm_class & 0x0C in [0x04, 0x08]:
#                     receipted_message_id = None
#                     if hasattr(pdu, 'receipted_message_id') and pdu.receipted_message_id:
#                         receipted_message_id = pdu.receipted_message_id.decode('ascii') if isinstance(pdu.receipted_message_id, bytes) else str(pdu.receipted_message_id)
#                     else:
#                         # Parse message_id from short_message (e.g., "id:100011200801250814070611431291 ...")
#                         match = re.search(r'id:(\S+)', message)
#                         if match:
#                             receipted_message_id = match.group(1)
#                             logger.info(f"Parsed message_id from short_message: {receipted_message_id}")
#                         else:
#                             logger.warning("Could not parse message_id from short_message")

#                     if receipted_message_id:
#                         logger.info(f"Delivery Report received for message_id: {receipted_message_id}, message: {message}")
#                         try:
#                             messages = SMSMessage.objects.filter(message_id=receipted_message_id).order_by('-sent_at')
#                             if messages.exists():
#                                 msg = messages.first()  # Take the most recent one
#                                 msg.status = 'delivered'
#                                 msg.delivered_at = timezone.now()
#                                 msg.save()
#                                 logger.info(f"Updated SMSMessage status to delivered for message_id: {receipted_message_id}")
#                                 if messages.count() > 1:
#                                     logger.warning(f"Multiple SMSMessage records found for message_id: {receipted_message_id}, count: {messages.count()}")
#                             else:
#                                 logger.warning(f"No SMSMessage found for message_id: {receipted_message_id}")
#                         except Exception as e:
#                             logger.error(f"Error updating SMSMessage for message_id {receipted_message_id}: {str(e)}")
#                     else:
#                         logger.warning("Delivery report received but no valid receipted_message_id found")

#                 # Handle incoming SMS (non-delivery-receipt)
#                 else:
#                     logger.info(f"Incoming SMS received from {pdu.source_addr} to {pdu.destination_addr}: {message}")
#                     try:
#                         # Extract message_id if available (optional for MO messages)
#                         mo_message_id = None
#                         if hasattr(pdu, 'message_id') and pdu.message_id:
#                             mo_message_id = pdu.message_id.decode('ascii') if isinstance(pdu.message_id, bytes) else str(pdu.message_id)

#                         # Save the incoming SMS to the database
#                         incoming_sms = SMSMessage.objects.create(
#                             phone_number=pdu.source_addr,
#                             content=message,
#                             status='received',
#                             category='general',
#                             priority='medium',
#                             message_id=mo_message_id
#                         )
#                         logger.info(f"Saved incoming SMS with ID {incoming_sms.id} from {pdu.source_addr}")
#                     except Exception as e:
#                         logger.error(f"Error saving incoming SMS: {str(e)}")

#                 # Send deliver_sm_resp to acknowledge the PDU
#                 client.send_pdu(pdu.require_ack())

#         client.set_message_received_handler(handle_pdu)

#         # Keep the connection alive with periodic enquire_link PDUs
#         last_enquire_link = time.time()
#         enquire_link_interval = 30  # Send enquire_link every 30 seconds

#         try:
#             while True:
#                 try:
#                     # Process incoming PDUs
#                     client.read_once()

#                     # Send enquire_link periodically to maintain the connection
#                     if time.time() - last_enquire_link >= enquire_link_interval:
#                         client.send_pdu(smpplib.smpp.make_pdu('enquire_link', client=client))
#                         logger.debug("Sent enquire_link PDU")
#                         last_enquire_link = time.time()

#                 except socket.timeout:
#                     logger.debug("Socket timeout while listening for PDUs")
#                 except Exception as e:
#                     logger.error(f"Error while listening for PDUs: {str(e)}")
#                     break

#         except KeyboardInterrupt:
#             logger.info("Received shutdown signal, unbinding...")
#             client.unbind()
#             client.disconnect()
#             logger.info("Disconnected from SMSC")
#             return "Listener stopped"

#     except Exception as e:
#         logger.exception("Failed to initialize or maintain SMPP connection")
#         if 'client' in locals() and client.state != smpplib.smpp.SMPP_CLIENT_STATE_CLOSED:
#             try:
#                 client.unbind()
#                 client.disconnect()
#             except:
#                 pass
#         raise


import logging
import time
import socket
import re
from celery import shared_task
import smpplib
from smpplib import consts
from django.utils import timezone
from django.conf import settings
from sms_app.models import SMSMessage  # Adjust import based on your project structure

logger = logging.getLogger(__name__)

@shared_task(bind=True, max_retries=5, default_retry_delay=60)
def listen_for_sms(self):
    """
    Celery task to listen for incoming SMS and delivery reports continuously.
    Maintains an SMPP transceiver binding and processes deliver_sm PDUs.
    """
    client = None
    try:
        # Initialize SMPP client
        client = smpplib.client.Client(
            settings.SMPP_CONFIG['HOST'],
            settings.SMPP_CONFIG['PORT'],
            timeout=1.0
        )
        client.connect()
        client.bind_transceiver(
            system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
            password=settings.SMPP_CONFIG['PASSWORD'],
            interface_version=0x34,  # SMPP v3.4
            addr_ton=consts.SMPP_TON_NATNL,
            addr_npi=consts.SMPP_NPI_ISDN,
            address_range=''
        )
        logger.info(f"Connected and bound to SMSC {settings.SMPP_CONFIG['HOST']}:{settings.SMPP_CONFIG['PORT']}")

        def handle_pdu(pdu):
            try:
                if pdu.command == 'deliver_sm':
                    # Decode the message based on data_coding
                    try:
                        message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
                    except Exception as decode_error:
                        logger.warning(f"Failed to decode short_message: {str(decode_error)}")
                        message = str(pdu.short_message)

                    logger.info(f"Received deliver_sm from {pdu.source_addr}: {message}")

                    # Check if this is a delivery receipt (esm_class & 0x0C == 0x04 or 0x08 per SMPP spec)
                    if pdu.esm_class & 0x0C in [0x04, 0x08]:
                        receipted_message_id = None
                        if hasattr(pdu, 'receipted_message_id') and pdu.receipted_message_id:
                            receipted_message_id = pdu.receipted_message_id.decode('ascii') if isinstance(pdu.receipted_message_id, bytes) else str(pdu.receipted_message_id)
                        else:
                            # Parse message_id from short_message (e.g., "id:100011200801250814070611431291 ...")
                            match = re.search(r'id:(\S+)', message)
                            if match:
                                receipted_message_id = match.group(1)
                                logger.info(f"Parsed message_id from short_message: {receipted_message_id}")
                            else:
                                logger.warning("Could not parse message_id from short_message")

                        if receipted_message_id:
                            logger.info(f"Delivery Report received for message_id: {receipted_message_id}, message: {message}")
                            try:
                                messages = SMSMessage.objects.filter(message_id=receipted_message_id).order_by('-sent_at')
                                if messages.exists():
                                    msg = messages.first()  # Take the most recent one
                                    msg.status = 'delivered'
                                    msg.delivered_at = timezone.now()
                                    msg.save()
                                    logger.info(f"Updated SMSMessage status to delivered for message_id: {receipted_message_id}")
                                    if messages.count() > 1:
                                        logger.warning(f"Multiple SMSMessage records found for message_id: {receipted_message_id}, count: {messages.count()}")
                                else:
                                    logger.warning(f"No SMSMessage found for message_id: {receipted_message_id}")
                            except Exception as db_error:
                                logger.error(f"Error updating SMSMessage for message_id {receipted_message_id}: {str(db_error)}")
                        else:
                            logger.warning("Delivery report received but no valid receipted_message_id found")

                    # Handle incoming SMS (non-delivery-receipt)
                    else:
                        logger.info(f"Incoming SMS received from {pdu.source_addr} to {pdu.destination_addr}: {message}")
                        try:
                            # Extract message_id if available (optional for MO messages)
                            mo_message_id = None
                            if hasattr(pdu, 'message_id') and pdu.message_id:
                                mo_message_id = pdu.message_id.decode('ascii') if isinstance(pdu.message_id, bytes) else str(pdu.message_id)

                            # Save the incoming SMS to the database
                            incoming_sms = SMSMessage.objects.create(
                                phone_number=pdu.source_addr,
                                content=message,
                                status='received',
                                category='general',
                                priority='medium',
                                message_id=mo_message_id
                            )
                            logger.info(f"Saved incoming SMS with ID {incoming_sms.id} from {pdu.source_addr}")
                        except Exception as db_error:
                            logger.error(f"Error saving incoming SMS: {str(db_error)}")

                    # Send deliver_sm_resp to acknowledge the PDU
                    try:
                        client.send_pdu(pdu.require_ack())
                        logger.debug(f"Sent deliver_sm_resp for sequence_number: {pdu.sequence}")
                    except Exception as ack_error:
                        logger.error(f"Failed to send deliver_sm_resp: {str(ack_error)}")
            except Exception as pdu_error:
                logger.error(f"Error processing PDU {pdu.command}: {str(pdu_error)}")

        client.set_message_received_handler(handle_pdu)

        # Keep the connection alive with periodic enquire_link PDUs
        last_enquire_link = time.time()
        enquire_link_interval = 30  # Send enquire_link every 30 seconds

        while True:
            try:
                # Process incoming PDUs
                client.read_once()

                # Send enquire_link periodically to maintain the connection
                if time.time() - last_enquire_link >= enquire_link_interval:
                    try:
                        client.send_pdu(smpplib.smpp.make_pdu('enquire_link', client=client))
                        logger.debug("Sent enquire_link PDU")
                        last_enquire_link = time.time()
                    except Exception as enquire_error:
                        logger.error(f"Failed to send enquire_link: {str(enquire_error)}")
                        raise  # Trigger reconnection by raising the exception

            except socket.timeout:
                logger.debug("Socket timeout while listening for PDUs")
            except Exception as loop_error:
                logger.error(f"Error in PDU listening loop: {str(loop_error)}")
                # Raise the exception to trigger Celery's retry mechanism
                raise self.retry(exc=loop_error, countdown=60, max_retries=5)

    except Exception as e:
        logger.error(f"Failed to initialize or maintain SMPP connection: {str(e)}")
        if client and client.state != smpplib.smpp.SMPP_CLIENT_STATE_CLOSED:
            try:
                client.unbind()
                client.disconnect()
                logger.info("Disconnected from SMSC")
            except Exception as disconnect_error:
                logger.error(f"Error during disconnect: {str(disconnect_error)}")
        # Retry the task after a delay
        raise self.retry(exc=e, countdown=60, max_retries=5)

    finally:
        if client and client.state != smpplib.smpp.SMPP_CLIENT_STATE_CLOSED:
            try:
                client.unbind()
                client.disconnect()
                logger.info("Disconnected from SMSC in finally block")
            except Exception as disconnect_error:
                logger.error(f"Error during final disconnect: {str(disconnect_error)}")

@shared_task
def send_sms(sms_id):
    """
    Celery task to send SMS via SMPP with delivery report handling.
    """

    print("OOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOO")
    try:
        # sms_message = SMSMessage.objects.get(id=sms_id)

        try:
            sms_message = Reply.objects.get(id=sms_id)
        except Reply.DoesNotExist:
            logger.error(f"Reply with id {sms_id} does not exist. Skipping SMS send.")
            return {"status": "failed", "error": "Reply not found"}

        logger.info(f"SMS_Message: {sms_message}")

        client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'], timeout=1.0)
        client.connect()
        client.bind_transceiver(
            system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
            password=settings.SMPP_CONFIG['PASSWORD'],
            interface_version=0x34,
            addr_ton=consts.SMPP_TON_NATNL,
            addr_npi=consts.SMPP_NPI_ISDN,
            address_range=''
        )

        message_id_holder = [None]  # Mutable list to hold message_id
        delivery_report_received = [False]  # Track if delivery report is received

        def sent_handler(pdu):
            logger.info(f"Message sent handler called with PDU command: {pdu.command}, status: {pdu.status}")
            if pdu.status != consts.SMPP_ESME_ROK:
                raise Exception(f"Submit SM failed with status {pdu.status}")
            message_id_holder[0] = pdu.message_id.decode('ascii') if isinstance(pdu.message_id, bytes) else str(pdu.message_id)
            logger.info(f"Message ID from sent handler: {message_id_holder[0]}")

        client.set_message_sent_handler(sent_handler)

        def handle_pdu(pdu):
            if pdu.command == 'deliver_sm':
                try:
                    message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
                except Exception:
                    message = str(pdu.short_message)
                logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")

                # Check if this is a delivery receipt (esm_class & 0x0C == 0x04 or 0x08 per SMPP spec)
                if pdu.esm_class & 0x0C in [0x04, 0x08]:
                    receipted_message_id = None
                    if hasattr(pdu, 'receipted_message_id') and pdu.receipted_message_id:
                        receipted_message_id = pdu.receipted_message_id.decode('ascii') if isinstance(pdu.receipted_message_id, bytes) else str(pdu.receipted_message_id)
                    else:
                        # Parse message_id from short_message (e.g., "id:100011200801250814070611431291 ...")
                        match = re.search(r'id:(\S+)', message)
                        if match:
                            receipted_message_id = match.group(1)
                            logger.info(f"Parsed message_id from short_message: {receipted_message_id}")
                        else:
                            logger.warning("Could not parse message_id from short_message")

                    if receipted_message_id:
                        logger.info(f"Delivery Report received for message_id: {receipted_message_id}, message: {message}")
                        try:
                            # Use filter() instead of get() to handle multiple matches
                            messages = SMSMessage.objects.filter(message_id=receipted_message_id).order_by('-sent_at')
                            if messages.exists():
                                msg = messages.first()  # Take the most recent one
                                msg.status = 'delivered'
                                msg.delivered_at = timezone.now()
                                msg.save()
                                delivery_report_received[0] = True
                                logger.info(f"Updated SMSMessage status to delivered for message_id: {receipted_message_id}")
                                if messages.count() > 1:
                                    logger.warning(f"Multiple SMSMessage records found for message_id: {receipted_message_id}, count: {messages.count()}")
                            else:
                                logger.warning(f"No SMSMessage found for message_id: {receipted_message_id}")
                        except Exception as e:
                            logger.error(f"Error updating SMSMessage for message_id {receipted_message_id}: {str(e)}")
                    else:
                        logger.warning("Delivery report received but no valid receipted_message_id found")

                client.send_pdu(pdu.require_ack())

        client.set_message_received_handler(handle_pdu)

        encoded_msg = sms_message.content.encode('utf-16be')

        # Send the message and capture the sent PDU
        sent_pdu = client.send_message(
            source_addr=getattr(sms_message, 'from_address', '908'),
            destination_addr="**********",
            short_message=encoded_msg,
            data_coding=0x08,
            registered_delivery=True,
            source_addr_npi=consts.SMPP_NPI_ISDN,
            dest_addr_npi=consts.SMPP_NPI_ISDN,
            source_addr_ton=consts.SMPP_TON_NATNL,
            dest_addr_ton=consts.SMPP_TON_NATNL,
        )

        # Read the response PDU to trigger the sent handler
        client.read_once()

        if message_id_holder[0] is None:
            raise Exception("Failed to retrieve message_id from submit_sm_resp")

        sms_message.message_id = message_id_holder[0]
        logger.info(f"Message ID: {sms_message.message_id}")

        sms_message.status = 'sent'
        sms_message.sent_at = timezone.now()
        sms_message.save()

        # Listen for delivery report with a 30-second timeout
        end_time = time.time() + 30
        while time.time() < end_time and not delivery_report_received[0]:
            try:
                client.read_once()
            except socket.timeout:
                logger.debug("Socket timeout while waiting for delivery report")
            except Exception as e:
                logger.error(f"Error while waiting for delivery report: {str(e)}")
                break

        if not delivery_report_received[0]:
            logger.warning("No delivery report received within 30 seconds")

        client.unbind()
        client.disconnect()
        return f"SMS sent with ID {sms_message.message_id}"

    except SMSMessage.DoesNotExist:
        logger.error(f"SMS with ID {sms_id} does not exist")
        return "SMS not found"
    except Exception as e:
        logger.exception("Failed to send SMS")
        if 'sms_message' in locals():
            sms_message.status = 'failed'
            sms_message.error_message = str(e)
            sms_message.save()
        raise


@shared_task
def send_sms(sms_id):
    """
    Celery task to send SMS via SMPP with delivery report handling.
    """

    try:

        try:
            sms_message = SMSMessage.objects.get(id=sms_id)
        except SMSMessage.DoesNotExist:
            logger.error(f"SMS with id {sms_id} does not exist. Skipping SMS send.")
            return {"status": "failed", "error": "SMS not found"}

        logger.info(f"SMS_Message: {sms_message}")

        client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'], timeout=1.0)
        client.connect()
        client.bind_transceiver(
            system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
            password=settings.SMPP_CONFIG['PASSWORD'],
            interface_version=0x34,
            addr_ton=consts.SMPP_TON_NATNL,
            addr_npi=consts.SMPP_NPI_ISDN,
            address_range=''
        )

        message_id_holder = [None]  # Mutable list to hold message_id
        delivery_report_received = [False]  # Track if delivery report is received

        def sent_handler(pdu):
            logger.info(f"Message sent handler called with PDU command: {pdu.command}, status: {pdu.status}")
            if pdu.status != consts.SMPP_ESME_ROK:
                raise Exception(f"Submit SM failed with status {pdu.status}")
            message_id_holder[0] = pdu.message_id.decode('ascii') if isinstance(pdu.message_id, bytes) else str(pdu.message_id)
            logger.info(f"Message ID from sent handler: {message_id_holder[0]}")

        client.set_message_sent_handler(sent_handler)

        def handle_pdu(pdu):
            if pdu.command == 'deliver_sm':
                try:
                    message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
                except Exception:
                    message = str(pdu.short_message)
                logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")

                # Check if this is a delivery receipt (esm_class & 0x0C == 0x04 or 0x08 per SMPP spec)
                if pdu.esm_class & 0x0C in [0x04, 0x08]:
                    receipted_message_id = None
                    if hasattr(pdu, 'receipted_message_id') and pdu.receipted_message_id:
                        receipted_message_id = pdu.receipted_message_id.decode('ascii') if isinstance(pdu.receipted_message_id, bytes) else str(pdu.receipted_message_id)
                    else:
                        # Parse message_id from short_message (e.g., "id:100011200801250814070611431291 ...")
                        match = re.search(r'id:(\S+)', message)
                        if match:
                            receipted_message_id = match.group(1)
                            logger.info(f"Parsed message_id from short_message: {receipted_message_id}")
                        else:
                            logger.warning("Could not parse message_id from short_message")

                    if receipted_message_id:
                        logger.info(f"Delivery Report received for message_id: {receipted_message_id}, message: {message}")
                        try:
                            # Use filter() instead of get() to handle multiple matches
                            messages = SMSMessage.objects.filter(message_id=receipted_message_id).order_by('-sent_at')
                            if messages.exists():
                                msg = messages.first()  # Take the most recent one
                                msg.status = 'delivered'
                                msg.delivered_at = timezone.now()
                                msg.save()
                                delivery_report_received[0] = True
                                logger.info(f"Updated SMSMessage status to delivered for message_id: {receipted_message_id}")
                                if messages.count() > 1:
                                    logger.warning(f"Multiple SMSMessage records found for message_id: {receipted_message_id}, count: {messages.count()}")
                            else:
                                logger.warning(f"No SMSMessage found for message_id: {receipted_message_id}")
                        except Exception as e:
                            logger.error(f"Error updating SMSMessage for message_id {receipted_message_id}: {str(e)}")
                    else:
                        logger.warning("Delivery report received but no valid receipted_message_id found")

                client.send_pdu(pdu.require_ack())

        client.set_message_received_handler(handle_pdu)

        encoded_msg = sms_message.content.encode('utf-16be')

        # Send the message and capture the sent PDU
        sent_pdu = client.send_message(
            source_addr=getattr(sms_message, 'from_address', '908'),
            destination_addr=sms_message.to_address,
            short_message=encoded_msg,
            data_coding=0x08,
            registered_delivery=True,
            source_addr_npi=consts.SMPP_NPI_ISDN,
            dest_addr_npi=consts.SMPP_NPI_ISDN,
            source_addr_ton=consts.SMPP_TON_NATNL,
            dest_addr_ton=consts.SMPP_TON_NATNL,
        )

        # Read the response PDU to trigger the sent handler
        client.read_once()

        if message_id_holder[0] is None:
            raise Exception("Failed to retrieve message_id from submit_sm_resp")

        sms_message.message_id = message_id_holder[0]
        logger.info(f"Message ID: {sms_message.message_id}")

        sms_message.status = 'sent'
        sms_message.sent_at = timezone.now()
        sms_message.save()

        # Listen for delivery report with a 30-second timeout
        end_time = time.time() + 30
        while time.time() < end_time and not delivery_report_received[0]:
            try:
                client.read_once()
            except socket.timeout:
                logger.debug("Socket timeout while waiting for delivery report")
            except Exception as e:
                logger.error(f"Error while waiting for delivery report: {str(e)}")
                break

        if not delivery_report_received[0]:
            logger.warning("No delivery report received within 30 seconds")

        client.unbind()
        client.disconnect()
        return f"SMS sent with ID {sms_message.message_id}"

    except SMSMessage.DoesNotExist:
        logger.error(f"SMS with ID {sms_id} does not exist")
        return "SMS not found"
    except Exception as e:
        logger.exception("Failed to send SMS")
        if 'sms_message' in locals():
            sms_message.status = 'failed'
            sms_message.error_message = str(e)
            sms_message.save()
        raise

# @shared_task
# def send_sms(sms_id):
#     try:
#         sms_message = SMSMessage.objects.get(id=sms_id)

#         print("SMS_Message************************************************************************************************************", sms_message)

#         client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'])
#         client.connect()
#         client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             interface_version=0x34,
#             addr_ton=consts.SMPP_TON_INTL,
#             addr_npi=consts.SMPP_NPI_ISDN,
#             address_range=''
#         ) 

#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 try:
#                     message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
#                 except Exception:
#                     message = str(pdu.short_message)
#                 logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")
#                 client.send_pdu(pdu.require_ack())
#                 if hasattr(pdu, 'receipted_message_id'):
#                     try:
#                         msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
#                         msg.status = 'delivered'
#                         msg.delivered_at = timezone.now()
#                         msg.save()
#                     except SMSMessage.DoesNotExist:
#                         pass

#         client.set_message_received_handler(handle_pdu)

#         encoded_msg = sms_message.message.encode('utf-16be')

#         pdu = client.send_message(
#             source_addr=sms_message.from_address,
#             destination_addr=sms_message.to_address,
#             short_message=encoded_msg,
#             data_coding=0x08,
#             registered_delivery=True,
#             source_addr_ton=consts.SMPP_TON_INTL,
#             source_addr_npi=consts.SMPP_NPI_ISDN,
#             dest_addr_ton=consts.SMPP_TON_INTL,
#             dest_addr_npi=consts.SMPP_NPI_ISDN,
#         )

#         sms_message.status = 'sent'
#         sms_message.message_id = pdu.message_id
#         sms_message.sent_at = timezone.now()
#         sms_message.save()

#         client.listen(timeout=5)
#         client.unbind()
#         client.disconnect()
#         return f"SMS sent with ID {pdu.message_id}"

#     except SMSMessage.DoesNotExist:
#         logger.error(f"SMS with ID {sms_id} does not exist")
#         return "SMS not found"
#     except Exception as e:
#         logger.exception("Failed to send SMS")
#         if 'sms_message' in locals():
#             sms_message.status = 'failed'
#             sms_message.error_message = str(e)
#             sms_message.save()
#         raise


# @shared_task
# def send_sms(sms_id):
#     try:
#         sms_message = SMSMessage.objects.get(id=sms_id)

#         print("SMS_Message************************************************************************************************************", sms_message)

#         client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'])
#         client.connect()
#         client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             interface_version=0x34,
#             addr_ton=consts.SMPP_TON_INTL,
#             addr_npi=consts.SMPP_NPI_ISDN,
#             address_range=''
#         )

#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 try:
#                     message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
#                 except Exception:
#                     message = str(pdu.short_message)
#                 logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")
#                 client.send_pdu(pdu.require_ack())
#                 if hasattr(pdu, 'receipted_message_id'):
#                     try:
#                         msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
#                         msg.status = 'delivered'
#                         msg.delivered_at = timezone.now()
#                         msg.save()
#                     except SMSMessage.DoesNotExist:
#                         pass

#         client.set_message_received_handler(handle_pdu)

#         encoded_msg = sms_message.message.encode('utf-16be')

#         # Send the message and capture the response
#         response = client.send_message(
#             source_addr=sms_message.from_address,
#             destination_addr=sms_message.to_address,
#             short_message=encoded_msg,
#             data_coding=0x08,
#             registered_delivery=True,
#             source_addr_ton=consts.SMPP_TON_INTL,
#             source_addr_npi=consts.SMPP_NPI_ISDN,
#             dest_addr_ton=consts.SMPP_TON_INTL,
#             dest_addr_npi=consts.SMPP_NPI_ISDN,
#         )

#         # Check if the response is a submit_sm_resp PDU and extract message_id
#         if response.command == 'submit_sm_resp' and hasattr(response, 'message_id'):
#             sms_message.message_id = response.message_id
#         else:
#             raise Exception("Failed to retrieve message_id from submit_sm_resp")

#         sms_message.status = 'sent'
#         sms_message.sent_at = timezone.now()
#         sms_message.save()

#         client.listen(timeout=5)
#         client.unbind()
#         client.disconnect()
#         return f"SMS sent with ID {sms_message.message_id}"

#     except SMSMessage.DoesNotExist:
#         logger.error(f"SMS with ID {sms_id} does not exist")
#         return "SMS not found"
#     except Exception as e:
#         logger.exception("Failed to send SMS")
#         if 'sms_message' in locals():
#             sms_message.status = 'failed'
#             sms_message.error_message = str(e)
#             sms_message.save()
#         raise


# @shared_task
# def send_sms(sms_id):
#     try:
#         sms_message = SMSMessage.objects.get(id=sms_id)

#         print("SMS_Message************************************************************************************************************", sms_message)

#         client = Client(settings.SMPP_CONFIG['HOST'], settings.SMPP_CONFIG['PORT'])
#         client.connect()
#         client.bind_transceiver(
#             system_id=settings.SMPP_CONFIG['SYSTEM_ID'],
#             password=settings.SMPP_CONFIG['PASSWORD'],
#             interface_version=0x34,
#             # addr_ton=consts.SMPP_TON_INTL,
#             addr_ton=consts.SMPP_TON_NATNL,
#             addr_npi=consts.SMPP_NPI_ISDN,
#             address_range=''
#         )

#         def handle_pdu(pdu):
#             if pdu.command == 'deliver_sm':
#                 try:
#                     message = pdu.short_message.decode('utf-16be') if pdu.data_coding == 0x08 else pdu.short_message.decode('utf-8')
#                 except Exception:
#                     message = str(pdu.short_message)
#                 logger.info(f"DLR or MO received from {pdu.source_addr}: {message}")
#                 client.send_pdu(pdu.require_ack())
#                 if hasattr(pdu, 'receipted_message_id'):
#                     try:
#                         msg = SMSMessage.objects.get(message_id=pdu.receipted_message_id)
#                         msg.status = 'delivered'
#                         msg.delivered_at = timezone.now()
#                         msg.save()
#                     except SMSMessage.DoesNotExist:
#                         pass

#         client.set_message_received_handler(handle_pdu)

#         encoded_msg = sms_message.message.encode('utf-16be')

#         # Send the message and capture the response
#         response = client.send_message(
#             source_addr=sms_message.from_address,
#             destination_addr=sms_message.to_address,
#             short_message=encoded_msg,
#             data_coding=0x08,
#             registered_delivery=True,
#             # source_addr_ton=consts.SMPP_TON_INTL,
#             source_addr_npi=consts.SMPP_NPI_ISDN,
#             # dest_addr_ton=consts.SMPP_TON_INTL,
#             dest_addr_npi=consts.SMPP_NPI_ISDN,

#             source_addr_ton=consts.SMPP_TON_NATNL,
#             dest_addr_ton=consts.SMPP_TON_NATNL,
#         )

#         # Debug the response
#         logger.info(f"Response command: {getattr(response, 'command', 'No command attribute')}")
#         logger.info(f"Response attributes: {dir(response)}")
#         logger.info(f"Response content: {response}")

#         # Check if the response is a submit_sm_resp PDU and extract message_id
#         if getattr(response, 'command', None) == 'submit_sm_resp' and hasattr(response, 'message_id'):
#             sms_message.message_id = response.message_id
#             logger.info(f"Message ID: {sms_message.message_id}")
#         else:
#             logger.error(f"Failed to retrieve message_id. Command: {getattr(response, 'command', 'Unknown')}, Has message_id: {hasattr(response, 'message_id')}")
#             raise Exception("Failed to retrieve message_id from submit_sm_resp")

#         sms_message.status = 'sent'
#         sms_message.sent_at = timezone.now()
#         sms_message.save()

#         client.listen(timeout=5)
#         client.unbind()
#         client.disconnect()
#         return f"SMS sent with ID {sms_message.message_id}"

#     except SMSMessage.DoesNotExist:
#         logger.error(f"SMS with ID {sms_id} does not exist")
#         return "SMS not found"
#     except Exception as e:
#         logger.exception("Failed to send SMS")
#         if 'sms_message' in locals():
#             sms_message.status = 'failed'
#             sms_message.error_message = str(e)
#             sms_message.save()
#         raise

@shared_task
def send_bulk_sms(campaign_id):
    try:
        campaign = SMSCampaign.objects.get(id=campaign_id)
        recipients = [r.strip() for r in campaign.recipients.replace('\n', ',').split(',') if r.strip()]

        count = 0
        for recipient in recipients:
            sms_message = SMSMessage.objects.create(
                to_address=recipient,
                message=campaign.template.content,
                created_by=campaign.created_by,
                status='pending'
            )
            send_sms.delay(sms_message.id)
            count += 1

        campaign.is_sent = True
        campaign.save()

        return f"Queued {count} messages for campaign {campaign.name}"

    except Exception as e:
        logger.exception("Failed to send bulk SMS")
        raise


@shared_task
def send_notification_task(user_id, notification_type, title, message, case_id=None, message_id=None):
    """Send notification to user and broadcast via WebSocket"""
    try:
        user = User.objects.get(id=user_id)

        # Create notification in database
        notification = Notification.objects.create(
            type=notification_type,
            title=title,
            message=message,
            user=user,
            case_id=case_id,
            message_id=message_id
        )

        # Send real-time notification via WebSocket
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                f'notifications_{user_id}',
                {
                    'type': 'notification_message',
                    'notification': {
                        'id': str(notification.id),
                        'type': notification.type,
                        'title': notification.title,
                        'message': notification.message,
                        'timestamp': notification.timestamp.isoformat(),
                        'read': notification.read,
                        'case_id': notification.case_id,
                        'message_id': str(notification.message_id) if notification.message_id else None
                    }
                }
            )

        logger.info(f"Notification sent to user {user.username}: {title}")
        return f"Notification sent successfully"

    except User.DoesNotExist:
        logger.error(f"User with ID {user_id} not found")
        return "User not found"
    except Exception as e:
        logger.error(f"Failed to send notification: {str(e)}")
        raise


@shared_task
def update_analytics_task():
    """Update analytics data daily"""
    try:
        today = timezone.now().date()

        # Calculate analytics for today
        total_cases = SMSMessage.objects.filter(timestamp__date=today).count()
        resolved_cases = SMSMessage.objects.filter(
            timestamp__date=today,
            status__in=['replied', 'closed']
        ).count()
        pending_cases = SMSMessage.objects.filter(
            timestamp__date=today,
            status__in=['new', 'in-progress']
        ).count()

        # Calculate average response time (simplified)
        avg_response_time = 2.5  # This would be calculated from actual reply times

        # Get category and status counts
        category_counts = {}
        for category, label in SMSMessage.CATEGORY_CHOICES:
            count = SMSMessage.objects.filter(
                timestamp__date=today,
                category=category
            ).count()
            category_counts[category] = count

        status_counts = {}
        for status_choice, label in SMSMessage.STATUS_CHOICES:
            count = SMSMessage.objects.filter(
                timestamp__date=today,
                status=status_choice
            ).count()
            status_counts[status_choice] = count

        # Create or update analytics record
        analytics, created = Analytics.objects.get_or_create(
            date=today,
            defaults={
                'total_cases': total_cases,
                'resolved_cases': resolved_cases,
                'pending_cases': pending_cases,
                'average_response_time': avg_response_time,
                'category_counts': category_counts,
                'status_counts': status_counts,
                'daily_trends': []
            }
        )

        if not created:
            analytics.total_cases = total_cases
            analytics.resolved_cases = resolved_cases
            analytics.pending_cases = pending_cases
            analytics.average_response_time = avg_response_time
            analytics.category_counts = category_counts
            analytics.status_counts = status_counts
            analytics.save()

        logger.info(f"Analytics updated for {today}")
        return f"Analytics updated successfully for {today}"

    except Exception as e:
        logger.error(f"Failed to update analytics: {str(e)}")
        raise


@shared_task
def auto_assign_messages_task():
    """Auto-assign new messages based on workload and availability"""
    try:
        # Get unassigned messages
        unassigned_messages = SMSMessage.objects.filter(
            assigned_to__isnull=True,
            status='new'
        ).order_by('timestamp')

        if not unassigned_messages.exists():
            return "No unassigned messages found"

        # Get available technicians (users with technician role)
        technicians = User.objects.filter(
            role='technician',
            is_active=True
        )

        if not technicians.exists():
            return "No available technicians found"

        # Simple round-robin assignment
        assigned_count = 0
        for i, message in enumerate(unassigned_messages[:10]):  # Limit to 10 at a time
            technician = technicians[i % len(technicians)]

            message.assigned_to = technician
            message.save()

            # Create timeline event
            TimelineEvent.objects.create(
                message=message,
                type='assignment',
                user=technician,  # System assignment
                description=f"Auto-assigned to {technician.get_full_name() or technician.username}"
            )

            # Send notification
            send_notification_task.delay(
                str(technician.id),
                'assignment',
                'New Case Assigned',
                f'Case {message.case_id} has been assigned to you',
                message.case_id,
                str(message.id)
            )

            assigned_count += 1

        logger.info(f"Auto-assigned {assigned_count} messages")
        return f"Auto-assigned {assigned_count} messages"

    except Exception as e:
        logger.error(f"Failed to auto-assign messages: {str(e)}")
        raise


@shared_task
def handle_incoming_sms_task(from_number, to_number, message_text):
    """Handle incoming SMS messages and create cases"""
    try:
        # Create new SMS message case
        sms_message = SMSMessage.objects.create(
            phone_number=from_number,
            content=message_text,
            status='new',
            category='general',  # Default category, can be auto-categorized later
            priority='medium',   # Default priority
            tags=['incoming-sms']
        )

        # Create timeline event
        TimelineEvent.objects.create(
            message=sms_message,
            type='message',
            user=None,  # System generated
            description=f"Incoming SMS received from {from_number}"
        )

        # Send real-time notification to all connected users
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'messages',
                {
                    'type': 'new_message_broadcast',
                    'message': {
                        'id': str(sms_message.id),
                        'phone_number': sms_message.phone_number,
                        'content': sms_message.content,
                        'case_id': sms_message.case_id,
                        'status': sms_message.status,
                        'category': sms_message.category,
                        'priority': sms_message.priority,
                        'timestamp': sms_message.timestamp.isoformat()
                    }
                }
            )

        # Auto-categorize based on keywords
        auto_categorize_message.delay(str(sms_message.id))

        # Send auto-reply
        send_auto_reply.delay(str(sms_message.id))

        logger.info(f"Created new case {sms_message.case_id} from incoming SMS")
        return f"Created case {sms_message.case_id}"

    except Exception as e:
        logger.error(f"Failed to handle incoming SMS: {str(e)}")
        raise


@shared_task
def auto_categorize_message(message_id):
    """Auto-categorize messages based on content keywords"""
    try:
        message = SMSMessage.objects.get(id=message_id)
        content_lower = message.content.lower()

        # Define keyword mappings
        category_keywords = {
            'power-outage': ['power', 'outage', 'electricity', 'blackout', 'no power', 'power cut'],
            'wire-cut': ['wire', 'cable', 'cut', 'broken', 'damaged', 'fallen', 'down'],
            'fallen-pole': ['pole', 'fallen', 'down', 'collapsed', 'damaged pole'],
            'corruption': ['corruption', 'bribe', 'illegal', 'fraud', 'misconduct'],
            'billing': ['bill', 'billing', 'payment', 'charge', 'cost', 'invoice', 'money'],
        }

        priority_keywords = {
            'critical': ['emergency', 'urgent', 'critical', 'dangerous', 'fire', 'sparking', 'shock'],
            'high': ['important', 'serious', 'major', 'significant'],
            'low': ['minor', 'small', 'little', 'when possible'],
        }

        # Check for category keywords
        for category, keywords in category_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                message.category = category
                break

        # Check for priority keywords
        for priority, keywords in priority_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                message.priority = priority
                break

        message.save()

        # Create timeline event if category or priority was updated
        TimelineEvent.objects.create(
            message=message,
            type='status_change',
            user=None,  # System generated
            description=f"Auto-categorized as {message.category} with {message.priority} priority"
        )

        logger.info(f"Auto-categorized message {message.case_id}: {message.category} ({message.priority})")
        return f"Categorized as {message.category}"

    except SMSMessage.DoesNotExist:
        logger.error(f"Message with ID {message_id} not found")
        return "Message not found"
    except Exception as e:
        logger.error(f"Failed to auto-categorize message: {str(e)}")
        raise


@shared_task
def send_auto_reply(message_id):
    """Send automatic reply to incoming SMS"""
    try:
        message = SMSMessage.objects.get(id=message_id)

        # Get appropriate auto-reply based on category
        auto_replies = {
            'power-outage': "Thank you for reporting the power outage. Our team has been notified and will investigate immediately. We'll keep you updated on the restoration progress.",
            'wire-cut': "Thank you for reporting the wire damage. This is a safety priority. Our emergency team has been dispatched to secure the area and make repairs.",
            'fallen-pole': "Thank you for reporting the fallen pole. Our emergency response team has been notified and will respond immediately to secure the area.",
            'corruption': "Thank you for your report. This matter will be forwarded to our compliance department for investigation. Your identity will be kept confidential.",
            'billing': "Thank you for your billing inquiry. Our customer service team will review your account and respond within 24 hours.",
            'general': "Thank you for contacting us. Your message has been received and assigned case number {case_id}. We will respond as soon as possible."
        }

        reply_text = auto_replies.get(message.category, auto_replies['general'])
        reply_text = reply_text.format(case_id=message.case_id)

        # Create reply record
        Reply.objects.create(
            message=message,
            content=reply_text,
            sender=None,  # System generated
            is_from_customer=False,
            timestamp=timezone.now()
        )

        # Create timeline event
        TimelineEvent.objects.create(
            message=message,
            type='reply',
            user=None,  # System generated
            description="Auto-reply sent to customer"
        )

        # Here you would integrate with your SMS sending service
        # For now, we'll just log it
        logger.info(f"Auto-reply sent for case {message.case_id}: {reply_text}")

        return f"Auto-reply sent for case {message.case_id}"

    except SMSMessage.DoesNotExist:
        logger.error(f"Message with ID {message_id} not found")
        return "Message not found"
    except Exception as e:
        logger.error(f"Failed to send auto-reply: {str(e)}")
        raise
