import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon, CalendarIcon, UserIcon, ExclamationTriangleIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { User } from '../../../../types';
import { mockUsers } from '../../../../data/mockData';

interface EnhancedAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAssign: (assignment: {
    userId: string;
    comment: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    dueDate: string;
    estimatedHours: number;
    skillsRequired: string[];
  }) => void;
  currentAssignee?: User;
  messageId: string;
  caseId: string;
}

export default function EnhancedAssignmentModal({
  isOpen,
  onClose,
  onAssign,
  currentAssignee,
  messageId,
  caseId
}: EnhancedAssignmentModalProps) {
  const [selectedUserId, setSelectedUserId] = useState(currentAssignee?.id || '');
  const [comment, setComment] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'critical'>('medium');
  const [dueDate, setDueDate] = useState('');
  const [estimatedHours, setEstimatedHours] = useState(2);
  const [skillsRequired, setSkillsRequired] = useState<string[]>([]);
  const [newSkill, setNewSkill] = useState('');

  const availableUsers = mockUsers.filter(user => 
    user.isActive && (user.role === 'technician' || user.role === 'supervisor')
  );

  const availableSkills = [
    'Electrical Work', 'Customer Service', 'Emergency Response', 
    'Technical Analysis', 'Field Operations', 'Safety Protocols'
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUserId) return;

    onAssign({
      userId: selectedUserId,
      comment,
      priority,
      dueDate,
      estimatedHours,
      skillsRequired
    });

    // Reset form
    setComment('');
    setPriority('medium');
    setDueDate('');
    setEstimatedHours(2);
    setSkillsRequired([]);
    onClose();
  };

  const addSkill = () => {
    if (newSkill.trim() && !skillsRequired.includes(newSkill.trim())) {
      setSkillsRequired([...skillsRequired, newSkill.trim()]);
      setNewSkill('');
    }
  };

  const removeSkill = (skill: string) => {
    setSkillsRequired(skillsRequired.filter(s => s !== skill));
  };

  const getPriorityColor = (p: string) => {
    switch (p) {
      case 'low': return 'border-green-300 bg-green-50 text-green-700';
      case 'medium': return 'border-yellow-300 bg-yellow-50 text-yellow-700';
      case 'high': return 'border-orange-300 bg-orange-50 text-orange-700';
      case 'critical': return 'border-red-300 bg-red-50 text-red-700';
      default: return 'border-gray-300 bg-gray-50 text-gray-700';
    }
  };

  const getUserStatusColor = (user: User) => {
    // Mock status based on user role for demo
    const status = user.role === 'supervisor' ? 'available' : 
                  Math.random() > 0.7 ? 'busy' : 'available';
    
    switch (status) {
      case 'available': return 'bg-green-400';
      case 'busy': return 'bg-yellow-400';
      default: return 'bg-gray-400';
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-sky-100 rounded-lg">
                <UserIcon className="h-6 w-6 text-sky-600" />
              </div>
              <div>
                <Dialog.Title className="text-lg font-medium text-gray-900">
                  {currentAssignee ? 'Reassign Case' : 'Assign Case'}
                </Dialog.Title>
                <p className="text-sm text-gray-500">Case: {caseId}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* User Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Assign to Team Member *
              </label>
              <div className="grid grid-cols-1 gap-3">
                {availableUsers.map((user) => (
                  <div
                    key={user.id}
                    className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedUserId === user.id
                        ? 'border-sky-500 bg-sky-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedUserId(user.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="assignee"
                        value={user.id}
                        checked={selectedUserId === user.id}
                        onChange={() => setSelectedUserId(user.id)}
                        className="text-sky-600 focus:ring-sky-500"
                      />
                      <div className="h-10 w-10 bg-sky-500 rounded-full flex items-center justify-center text-white font-medium">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900">{user.name}</h4>
                          <div className={`w-2 h-2 rounded-full ${getUserStatusColor(user)}`}></div>
                        </div>
                        <p className="text-sm text-gray-500 capitalize">{user.role} • {user.department}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                            Available
                          </span>
                          <span className="text-xs text-gray-400">
                            Current load: {Math.floor(Math.random() * 5) + 1} cases
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Priority and Estimated Hours */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Priority Level
                </label>
                <div className="grid grid-cols-2 gap-3">
                  {(['low', 'medium', 'high', 'critical'] as const).map((p) => (
                    <div
                      key={p}
                      className={`relative border rounded-lg p-3 cursor-pointer transition-all ${
                        priority === p
                          ? 'border-sky-500 bg-sky-50'
                          : getPriorityColor(p)
                      }`}
                      onClick={() => setPriority(p)}
                    >
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="priority"
                          value={p}
                          checked={priority === p}
                          onChange={() => setPriority(p)}
                          className="text-sky-600 focus:ring-sky-500"
                        />
                        <div className="flex items-center space-x-2">
                          {p === 'critical' && <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />}
                          <span className="text-sm font-medium capitalize">{p}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <label htmlFor="estimatedHours" className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Hours
                </label>
                <input
                  type="number"
                  id="estimatedHours"
                  min="0.5"
                  max="40"
                  step="0.5"
                  value={estimatedHours}
                  onChange={(e) => setEstimatedHours(parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Due Date */}
            <div>
              <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-2">
                Due Date *
              </label>
              <div className="relative">
                <input
                  type="datetime-local"
                  id="dueDate"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  min={new Date().toISOString().slice(0, 16)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                  required
                />
                <CalendarIcon className="absolute right-3 top-3 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* Skills Required */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Skills Required
              </label>
              <div className="flex gap-2 mb-3">
                <select
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">Select a skill...</option>
                  {availableSkills.filter(skill => !skillsRequired.includes(skill)).map(skill => (
                    <option key={skill} value={skill}>{skill}</option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={addSkill}
                  disabled={!newSkill}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {skillsRequired.map((skill) => (
                  <span
                    key={skill}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-sky-100 text-sky-800"
                  >
                    {skill}
                    <button
                      type="button"
                      onClick={() => removeSkill(skill)}
                      className="ml-2 text-sky-600 hover:text-sky-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>

            {/* Assignment Comment */}
            <div>
              <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-2">
                Assignment Instructions
              </label>
              <div className="relative">
                <textarea
                  id="comment"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  placeholder="Add specific instructions, context, or requirements for this assignment..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent resize-none"
                />
                <DocumentTextIcon className="absolute right-3 top-3 h-4 w-4 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!selectedUserId || !dueDate}
                className="flex-1 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {currentAssignee ? 'Reassign Case' : 'Assign Case'}
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}