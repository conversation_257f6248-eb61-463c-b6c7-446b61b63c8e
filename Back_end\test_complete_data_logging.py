#!/usr/bin/env python
"""
Test script to verify that delete operations now log complete record data for restoration.
"""

import os
import sys
import django
import json

# Add the core directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from transformer.models import Basestation, TransformerData
from logs.models import ActivityLog

User = get_user_model()


def test_complete_data_logging():
    """Test that delete operations log complete record data"""
    
    # Create a test client
    client = Client()
    
    # Create or get a test user
    user, created = User.objects.get_or_create(
        username='testuser_complete_data',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Login the user
    client.force_login(user)
    print("✅ User authenticated")
    
    # Clear existing activity logs for clean testing
    ActivityLog.objects.filter(user=user).delete()
    print("🧹 Cleared existing activity logs for test user")
    
    # Test 1: Create test basestations for deletion testing
    print("\n🧪 Test 1: Creating test basestations")
    
    basestation1 = Basestation.objects.create(
        station_code='TEST-COMPLETE-001',
        substation='Test Complete Substation 1',
        feeder='Test Complete Feeder 1',
        address='Test Complete Address 1',
        region='Test Complete Region',
        csc='TESTC01',
        station_type='Distribution',
        created_by=user,
        updated_by=user
    )
    
    basestation2 = Basestation.objects.create(
        station_code='TEST-COMPLETE-002',
        substation='Test Complete Substation 2',
        feeder='Test Complete Feeder 2',
        address='Test Complete Address 2',
        region='Test Complete Region',
        csc='TESTC02',
        station_type='Transmission',
        created_by=user,
        updated_by=user
    )
    
    print(f"✅ Created test basestations: {basestation1.station_code}, {basestation2.station_code}")
    
    # Test 2: Create test transformers
    print("\n🧪 Test 2: Creating test transformers")
    
    transformer1 = TransformerData.objects.create(
        basestation=basestation1,
        trafo_type='Conservator',
        capacity='100',
        primary_voltage='15',
        colling_type='ONAN',
        serial_number='TEST-COMPLETE-SN-001',
        service_type='Dedicated',
        status='New',
        manufacturer='ABB Tanzania',
        vector_group='DY1',
        impedance_voltage=5.5,
        created_by=user,
        updated_by=user
    )
    
    transformer2 = TransformerData.objects.create(
        basestation=basestation2,
        trafo_type='Hermatical',
        capacity='200',
        primary_voltage='19',
        colling_type='Dry Type',
        serial_number='TEST-COMPLETE-SN-002',
        service_type='Public',
        status='Maintained',
        manufacturer='Siemens',
        vector_group='DY5',
        impedance_voltage=6.0,
        created_by=user,
        updated_by=user
    )
    
    print(f"✅ Created test transformers: {transformer1.id}, {transformer2.id}")
    
    # Test 3: Test single basestation deletion
    print("\n🧪 Test 3: Testing single basestation deletion with complete data logging")
    
    response = client.delete(f'/api/transformer/basestations/{basestation1.station_code}/')
    print(f"Delete response status: {response.status_code}")
    
    # Check activity log
    delete_logs = ActivityLog.objects.filter(
        user=user,
        action='DELETE',
        model_name='Basestation'
    ).order_by('-timestamp')
    
    if delete_logs.exists():
        log_entry = delete_logs.first()
        changes = json.loads(log_entry.changes)
        
        print(f"✅ Delete activity logged successfully!")
        print(f"📝 Record ID: {log_entry.record_id}")
        print(f"🔍 Has complete record data: {'deleted_record' in changes}")
        
        if 'deleted_record' in changes:
            deleted_record = changes['deleted_record']
            print(f"✅ Complete record data captured:")
            print(f"   - Station Code: {deleted_record.get('station_code')}")
            print(f"   - Substation: {deleted_record.get('substation')}")
            print(f"   - Feeder: {deleted_record.get('feeder')}")
            print(f"   - Address: {deleted_record.get('address')}")
            print(f"   - Region: {deleted_record.get('region')}")
            print(f"   - CSC: {deleted_record.get('csc')}")
            print(f"   - Station Type: {deleted_record.get('station_type')}")
            print(f"   - Created By: {deleted_record.get('created_by', {}).get('username')}")
            print(f"   - Updated By: {deleted_record.get('updated_by', {}).get('username')}")
            print(f"✅ All necessary data for restoration is available!")
        else:
            print(f"❌ Complete record data not found in changes")
    else:
        print(f"❌ No delete activity log found")
    
    # Test 4: Test single transformer deletion
    print("\n🧪 Test 4: Testing single transformer deletion with complete data logging")
    
    response = client.delete(f'/api/transformer/transformerdata/{transformer1.id}/')
    print(f"Delete response status: {response.status_code}")
    
    # Check activity log
    transformer_delete_logs = ActivityLog.objects.filter(
        user=user,
        action='DELETE',
        model_name='TransformerData'
    ).order_by('-timestamp')
    
    if transformer_delete_logs.exists():
        log_entry = transformer_delete_logs.first()
        changes = json.loads(log_entry.changes)
        
        print(f"✅ Transformer delete activity logged successfully!")
        print(f"📝 Record ID: {log_entry.record_id}")
        print(f"🔍 Has complete record data: {'deleted_record' in changes}")
        
        if 'deleted_record' in changes:
            deleted_record = changes['deleted_record']
            print(f"✅ Complete transformer record data captured:")
            print(f"   - ID: {deleted_record.get('id')}")
            print(f"   - Trafo Type: {deleted_record.get('trafo_type')}")
            print(f"   - Capacity: {deleted_record.get('capacity')}")
            print(f"   - Serial Number: {deleted_record.get('serial_number')}")
            print(f"   - Manufacturer: {deleted_record.get('manufacturer')}")
            print(f"   - Basestation Data: {deleted_record.get('basestation', {}).get('station_code')}")
            print(f"✅ All necessary transformer data for restoration is available!")
        else:
            print(f"❌ Complete transformer record data not found in changes")
    else:
        print(f"❌ No transformer delete activity log found")
    
    # Test 5: Test bulk deletion
    print("\n🧪 Test 5: Testing bulk deletion with complete data logging")
    
    # Create additional basestations for bulk delete test
    basestation3 = Basestation.objects.create(
        station_code='TEST-BULK-COMPLETE-001',
        substation='Test Bulk Complete Substation 1',
        feeder='Test Bulk Complete Feeder 1',
        address='Test Bulk Complete Address 1',
        region='Test Bulk Complete Region',
        csc='TESTBC01',
        station_type='Distribution',
        created_by=user,
        updated_by=user
    )
    
    basestation4 = Basestation.objects.create(
        station_code='TEST-BULK-COMPLETE-002',
        substation='Test Bulk Complete Substation 2',
        feeder='Test Bulk Complete Feeder 2',
        address='Test Bulk Complete Address 2',
        region='Test Bulk Complete Region',
        csc='TESTBC02',
        station_type='Transmission',
        created_by=user,
        updated_by=user
    )
    
    # Test bulk delete
    bulk_delete_data = {
        'station_codes': ['TEST-BULK-COMPLETE-001', 'TEST-BULK-COMPLETE-002']
    }
    
    response = client.post(
        '/api/transformer/basestations/bulk_delete/',
        data=json.dumps(bulk_delete_data),
        content_type='application/json'
    )
    print(f"Bulk delete response status: {response.status_code}")
    
    # Check bulk delete activity log
    bulk_delete_logs = ActivityLog.objects.filter(
        user=user,
        action='BULK_DELETE',
        model_name='Basestation'
    ).order_by('-timestamp')
    
    if bulk_delete_logs.exists():
        log_entry = bulk_delete_logs.first()
        changes = json.loads(log_entry.changes)
        
        print(f"✅ Bulk delete activity logged successfully!")
        print(f"📝 Record ID: {log_entry.record_id}")
        print(f"🔍 Has complete records data: {'deleted_records' in changes}")
        print(f"🔍 Deleted count: {changes.get('deleted_count', 0)}")
        
        if 'deleted_records' in changes:
            deleted_records = changes['deleted_records']
            print(f"✅ Complete bulk records data captured:")
            print(f"   - Number of records: {len(deleted_records)}")
            for i, record in enumerate(deleted_records, 1):
                print(f"   - Record {i}: {record.get('station_code')} - {record.get('substation')}")
            print(f"✅ All necessary data for bulk restoration is available!")
        else:
            print(f"❌ Complete bulk records data not found in changes")
    else:
        print(f"❌ No bulk delete activity log found")
    
    # Test 6: Summary of all logged activities
    print("\n🧪 Test 6: Summary of all logged activities")
    
    all_logs = ActivityLog.objects.filter(user=user).order_by('-timestamp')
    print(f"📊 Total activity logs created: {all_logs.count()}")
    
    print("\n📋 Activity log summary:")
    for i, log in enumerate(all_logs, 1):
        changes = json.loads(log.changes)
        has_complete_data = 'deleted_record' in changes or 'deleted_records' in changes
        
        print(f"{i}. {log.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {log.action} {log.model_name}")
        print(f"   Record ID: {log.record_id}")
        print(f"   Complete data: {'✅ Yes' if has_complete_data else '❌ No'}")
        
        if has_complete_data:
            if 'deleted_record' in changes:
                print(f"   Single record restoration data available")
            elif 'deleted_records' in changes:
                print(f"   Bulk records restoration data available ({len(changes['deleted_records'])} records)")
    
    print("\n✅ Complete data logging tests completed!")
    print("\n🎯 Key Improvements:")
    print("   • Delete operations now capture complete record data")
    print("   • Single deletes save full record in 'deleted_record' field")
    print("   • Bulk deletes save all records in 'deleted_records' array")
    print("   • All data necessary for restoration is preserved")
    print("   • Includes related data (user info, basestation data for transformers)")
    print("   • Timestamps added for audit trail")


if __name__ == '__main__':
    test_complete_data_logging()
