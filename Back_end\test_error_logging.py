#!/usr/bin/env python
"""
Test script to verify improved error logging functionality.
"""

import os
import sys
import django

# Add the core directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from logs.models import ErrorLog
import json

User = get_user_model()


def test_error_logging():
    """Test that error logging now includes detailed exception information"""
    
    # Create a test client
    client = Client()
    
    # Create or get a test user
    user, created = User.objects.get_or_create(
        username='testuser_error',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Login the user
    client.force_login(user)
    print("✅ User authenticated")
    
    # Clear existing error logs for clean testing
    ErrorLog.objects.all().delete()
    print("🧹 Cleared existing error logs")
    
    # Test 1: Try to create a basestation with invalid data to trigger an error
    print("\n🧪 Test 1: Testing error logging with invalid basestation creation")
    
    invalid_data = {
        'station_code': '',  # Empty station code should cause validation error
        'region': 'INVALID_REGION_THAT_DOES_NOT_EXIST',
        'csc': 'INVALID_CSC',
        'substation': 'Test Substation',
        'feeder': 'Test Feeder',
        'address': 'Test Address',
        'station_type': 'INVALID_TYPE'
    }
    
    response = client.post('/api/transformer/basestations/', data=json.dumps(invalid_data), content_type='application/json')
    print(f"Response Status: {response.status_code}")
    
    # Check if error was logged
    error_logs = ErrorLog.objects.filter(user=user).order_by('-timestamp')
    if error_logs.exists():
        latest_error = error_logs.first()
        print(f"✅ Error logged successfully!")
        print(f"📝 Error Message: {latest_error.message}")
        print(f"🔍 Contains exception type: {'Exception' in latest_error.message or 'Error' in latest_error.message}")
        print(f"🔍 Contains method name: {'BasestationViewSet' in latest_error.message}")
        print(f"📅 Timestamp: {latest_error.timestamp}")
        
        # Check if the message format is improved (contains exception details)
        if 'failed:' in latest_error.message.lower() and ':' in latest_error.message:
            print("✅ Error message format is improved with detailed exception info")
        else:
            print("❌ Error message still uses generic format")
    else:
        print("❌ No error was logged")
    
    # Test 2: Try to create a transformer with invalid data
    print("\n🧪 Test 2: Testing error logging with invalid transformer creation")
    
    invalid_transformer_data = {
        'basestation': 99999,  # Non-existent basestation ID
        'trafo_type': 'INVALID_TYPE',
        'capacity': 'INVALID_CAPACITY',
        'primary_voltage': 'INVALID_VOLTAGE'
    }
    
    response = client.post('/api/transformer/transformerdata/', data=json.dumps(invalid_transformer_data), content_type='application/json')
    print(f"Response Status: {response.status_code}")
    
    # Check latest error logs
    error_logs = ErrorLog.objects.filter(user=user).order_by('-timestamp')[:2]
    if len(error_logs) > 1:
        latest_error = error_logs[0]
        print(f"✅ New error logged successfully!")
        print(f"📝 Error Message: {latest_error.message}")
        print(f"🔍 Contains exception type: {'Exception' in latest_error.message or 'Error' in latest_error.message}")
        print(f"🔍 Contains method name: {'TransformerDataViewSet' in latest_error.message}")
        
        # Check if the message format is improved
        if 'failed:' in latest_error.message.lower() and ':' in latest_error.message:
            print("✅ Error message format is improved with detailed exception info")
        else:
            print("❌ Error message still uses generic format")
    
    # Test 3: Test batch processing error logging
    print("\n🧪 Test 3: Testing batch processing error logging")
    
    # Try to trigger an error in batch processing by using invalid parameters
    response = client.get('/api/transformer/basestationsFiltered/?page=invalid&pageSize=2000&searchType=BaseStation')
    print(f"Response Status: {response.status_code}")
    
    # Check all error logs
    all_error_logs = ErrorLog.objects.filter(user=user).order_by('-timestamp')
    print(f"\n📊 Total error logs created: {all_error_logs.count()}")
    
    print("\n📋 Summary of all error logs:")
    for i, error_log in enumerate(all_error_logs[:5], 1):  # Show first 5
        print(f"{i}. {error_log.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {error_log.message[:100]}...")
        
        # Check if it's the new format
        if 'failed:' in error_log.message.lower():
            print(f"   ✅ Uses improved format")
        else:
            print(f"   ❌ Uses old generic format")
    
    # Test 4: Check error log API endpoint
    print("\n🧪 Test 4: Testing error log API endpoint")
    
    response = client.get('/api/logs/errors/')
    print(f"Error logs API Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Retrieved {len(data.get('results', []))} error logs via API")
        
        if data.get('results'):
            first_error = data['results'][0]
            print(f"📝 First error message: {first_error.get('message', '')[:100]}...")
    
    print("\n✅ Error logging tests completed!")
    print("\n🎯 Key Improvements:")
    print("   • Error messages now include specific exception types")
    print("   • Error messages include the exact method/function that failed")
    print("   • Error messages include the actual exception details")
    print("   • Format: 'ClassName.method_name() failed: ExceptionType: detailed_message'")


if __name__ == '__main__':
    test_error_logging()
