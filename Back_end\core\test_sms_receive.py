#!/usr/bin/env python
"""
Test script for receiving SMS messages via SMPP.
Usage: python test_sms_receive.py
"""

import os
import sys
import django
import time
import signal
import threading

# Add the core directory to Python path
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from sms_app.sms_handler import (
    SMPPClient, start_smpp_client, stop_smpp_client, 
    get_smpp_status, smpp_client, smpp_running
)
from sms_app.models import SMSMessage


def test_direct_smpp_receive():
    """Test receiving SMS directly via SMPP client"""
    print("🧪 Testing Direct SMPP SMS Receiving")
    print("=" * 40)
    
    # Create SMPP client
    client = SMPPClient()
    
    try:
        # Connect
        print("🔌 Connecting to SMPP server...")
        if not client.connect():
            print("❌ Failed to connect to SMPP server")
            return False
        
        print("✅ Connected successfully")
        print("📥 Listening for incoming SMS messages...")
        print("📝 Send an SMS to 908 to test receiving")
        print("⏹️ Press Ctrl+C to stop listening")
        
        # Set up signal handler for graceful shutdown
        def signal_handler(signum, frame):
            print("\n🛑 Stopping SMS receiver...")
            client.running = False
            client.disconnect()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Start listening
        client.running = True
        client.listen_for_messages()
        
        return True
        
    except Exception as e:
        print(f"❌ Error during SMS receive test: {e}")
        return False
    finally:
        # Disconnect
        client.disconnect()
        print("🔌 Disconnected from SMPP server")


def test_integrated_sms_receive():
    """Test receiving SMS via integrated handler"""
    print("🧪 Testing Integrated SMS Receiving")
    print("=" * 40)
    
    try:
        # Start SMPP client
        print("🚀 Starting integrated SMPP client...")
        if not start_smpp_client():
            print("❌ Failed to start SMPP client")
            return False
        
        # Wait a moment for connection
        time.sleep(2)
        
        # Check status
        status = get_smpp_status()
        print(f"📊 SMPP Status: {status}")
        
        if not status['connected']:
            print("❌ SMPP client not connected")
            return False
        
        print("✅ Integrated SMPP client started successfully")
        print("📥 Listening for incoming SMS messages...")
        print("📝 Send an SMS to 908 to test receiving")
        print("💾 Messages will be saved to database")
        print("⏹️ Press Ctrl+C to stop listening")
        
        # Set up signal handler for graceful shutdown
        def signal_handler(signum, frame):
            print("\n🛑 Stopping integrated SMS receiver...")
            stop_smpp_client()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Monitor for new messages
        last_message_count = SMSMessage.objects.count()
        print(f"📊 Current message count: {last_message_count}")
        
        while True:
            time.sleep(5)  # Check every 5 seconds
            
            current_count = SMSMessage.objects.count()
            if current_count > last_message_count:
                new_messages = current_count - last_message_count
                print(f"📨 {new_messages} new message(s) received! Total: {current_count}")
                
                # Show latest messages
                latest_messages = SMSMessage.objects.order_by('-created_at')[:new_messages]
                for msg in latest_messages:
                    print(f"  📱 From: {msg.customer_phone}")
                    print(f"  💬 Message: {msg.content}")
                    print(f"  🕐 Time: {msg.created_at}")
                    print("  " + "-" * 30)
                
                last_message_count = current_count
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping integrated SMS receiver...")
        stop_smpp_client()
        return True
    except Exception as e:
        print(f"❌ Error during integrated SMS receive test: {e}")
        return False


def test_message_processing():
    """Test message processing and auto-reply functionality"""
    print("🧪 Testing Message Processing")
    print("=" * 40)
    
    try:
        # Check if SMPP client is running
        status = get_smpp_status()
        if not status['running']:
            print("🚀 Starting SMPP client for message processing test...")
            if not start_smpp_client():
                print("❌ Failed to start SMPP client")
                return False
            time.sleep(2)
        
        print("✅ SMPP client is running")
        print("📥 Monitoring message processing...")
        print("📝 Send an SMS to 908 to test processing and auto-reply")
        print("🔄 The system will:")
        print("  1. Receive the SMS")
        print("  2. Save it to database")
        print("  3. Process and categorize it")
        print("  4. Send an auto-reply")
        print("⏹️ Press Ctrl+C to stop monitoring")
        
        # Set up signal handler
        def signal_handler(signum, frame):
            print("\n🛑 Stopping message processing monitor...")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Monitor database for changes
        last_count = SMSMessage.objects.count()
        print(f"📊 Current message count: {last_count}")
        
        while True:
            time.sleep(3)  # Check every 3 seconds
            
            current_count = SMSMessage.objects.count()
            if current_count > last_count:
                new_messages = current_count - last_count
                print(f"\n📨 {new_messages} new message(s) processed!")
                
                # Show latest messages with details
                latest_messages = SMSMessage.objects.order_by('-created_at')[:new_messages]
                for msg in latest_messages:
                    print(f"  📱 From: {msg.customer_phone}")
                    print(f"  💬 Content: {msg.content}")
                    print(f"  📂 Category: {msg.category}")
                    print(f"  🎯 Priority: {msg.priority}")
                    print(f"  📊 Status: {msg.status}")
                    print(f"  🕐 Received: {msg.created_at}")
                    
                    # Check for replies
                    replies = msg.replies.all()
                    if replies:
                        print(f"  💬 Replies ({len(replies)}):")
                        for reply in replies:
                            print(f"    - {reply.content} (by {reply.sender})")
                    
                    print("  " + "=" * 40)
                
                last_count = current_count
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping message processing monitor...")
        return True
    except Exception as e:
        print(f"❌ Error during message processing test: {e}")
        return False


def show_recent_messages():
    """Show recent messages from database"""
    print("📊 Recent SMS Messages")
    print("=" * 40)
    
    try:
        messages = SMSMessage.objects.order_by('-created_at')[:10]
        
        if not messages:
            print("📭 No messages found in database")
            return
        
        print(f"📨 Showing {len(messages)} most recent messages:")
        print()
        
        for i, msg in enumerate(messages, 1):
            print(f"{i}. 📱 From: {msg.customer_phone}")
            print(f"   💬 Content: {msg.content}")
            print(f"   📂 Category: {msg.category}")
            print(f"   📊 Status: {msg.status}")
            print(f"   🕐 Time: {msg.created_at}")
            
            # Show replies
            replies = msg.replies.all()
            if replies:
                print(f"   💬 Replies ({len(replies)}):")
                for reply in replies:
                    print(f"     - {reply.content}")
            
            print("   " + "-" * 30)
        
    except Exception as e:
        print(f"❌ Error showing recent messages: {e}")


def main():
    """Run SMS receiving tests"""
    print("📥 SMS Receiving Test Suite")
    print("=" * 50)
    
    print("\nSelect test to run:")
    print("1. Direct SMPP Receive Test")
    print("2. Integrated Handler Receive Test")
    print("3. Message Processing Test")
    print("4. Show Recent Messages")
    print("5. SMPP Status")
    
    choice = input("\nEnter choice (1-5): ").strip()
    
    if choice == "1":
        test_direct_smpp_receive()
    elif choice == "2":
        test_integrated_sms_receive()
    elif choice == "3":
        test_message_processing()
    elif choice == "4":
        show_recent_messages()
    elif choice == "5":
        status = get_smpp_status()
        print("\n📊 SMPP Client Status:")
        print("=" * 30)
        for key, value in status.items():
            if key == 'config':
                print("Configuration:")
                for config_key, config_value in value.items():
                    if config_key == 'PASSWORD':
                        config_value = '*' * len(str(config_value))
                    print(f"  {config_key}: {config_value}")
            else:
                icon = "✅" if value else "❌"
                print(f"{icon} {key.replace('_', ' ').title()}: {value}")
    else:
        print("❌ Invalid choice")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
