import React from 'react';
import { User } from '../../../../types';
import { mockUsers } from '../../../../data/mockData';

interface AssignmentPanelProps {
  onAssign: (user: User) => void;
  currentAssignee?: User;
}

export default function AssignmentPanel({ onAssign, currentAssignee }: AssignmentPanelProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="text-sm font-medium text-gray-900 mb-3">Assign to:</h4>
      <div className="grid grid-cols-1 gap-2">
        {mockUsers.filter(user => user.isActive && user.role !== 'admin').map((user) => (
          <button
            key={user.id}
            onClick={() => onAssign(user)}
            className={`p-3 text-left rounded-lg border transition-colors ${
              currentAssignee?.id === user.id
                ? 'border-sky-500 bg-sky-50'
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
          >
            <div className="font-medium text-sm text-gray-900">{user.name}</div>
            <div className="text-xs text-gray-500">{user.department} • {user.role}</div>
          </button>
        ))}
      </div>
    </div>
  );
}