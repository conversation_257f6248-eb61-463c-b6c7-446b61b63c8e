import React, { useState, useEffect } from 'react';
import { User } from '../../../../types';
import apiClient from '../../../../api/apiClient';

interface AssignmentPanelProps {
  onAssign: (user: User) => void;
  currentAssignee?: User;
}

export default function AssignmentPanel({ onAssign, currentAssignee }: AssignmentPanelProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiClient.get({ url: '/api/users/' });
        setUsers(response.results || response);
      } catch (error) {
        console.error('Error fetching users:', error);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, []);

  if (loading) {
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Assign to:</h4>
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="text-sm font-medium text-gray-900 mb-3">Assign to:</h4>
      <div className="grid grid-cols-1 gap-2">
        {users.filter(user => user.isActive && user.role !== 'admin').map((user) => (
          <button
            key={user.id}
            onClick={() => onAssign(user)}
            className={`p-3 text-left rounded-lg border transition-colors ${
              currentAssignee?.id === user.id
                ? 'border-sky-500 bg-sky-50'
                : 'border-gray-200 hover:border-gray-300 bg-white'
            }`}
          >
            <div className="font-medium text-sm text-gray-900">{user.name}</div>
            <div className="text-xs text-gray-500">{user.department} • {user.role}</div>
          </button>
        ))}
      </div>
    </div>
  );
}