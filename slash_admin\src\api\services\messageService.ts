import apiClient from "../apiClient";


export enum MessageApi {

  getMessages = "/api/messages/",
  // addReply = "/api/messages/${id}/add_reply/",
  // DETAIL: (id: string) => `/messages/${id}/`,
  // ASSIGN: (id: string) => `/messages/${id}/assign/`,
  // UPDATE_STATUS: (id: string) => `/messages/${id}/update_status/`,
  // ARCHIVE: (id: string) => `/messages/${id}/archive/`,
  
  // BULK_ASSIGN: '/bulk-assign/',
}

const MessageApi2 = {
  addReply: (id: string) => `/api/messages/${id}/add_reply/`,
  deleteMessage: (id: string) => `/api/messages/${id}/`,

  getReplies: (id: string) => `/api/replies/?message_id=${id}`,
};




const getMessages = async (params?: {  status?: string;  category?: string;  priority?: string;  assigned_to?: string;  search?: string;  page?: number;}) =>
  apiClient.get<any>({ url: MessageApi.getMessages, params });


const getReplies = async (id: string) => {
  return await apiClient.get({ url: MessageApi2.getReplies(id) });
};

const addReply = async (id: string, content: string) => {
  console.log("id", id, "content", content);
  const data = { content };
  return await apiClient.post({ url: MessageApi2.addReply(id), data });
};

const deleteMessage = async (id: string) => {
  return await apiClient.delete({ url: MessageApi2.deleteMessage(id) });
};



const messageService = {

  getMessages,
  addReply,
  deleteMessage,
  getReplies
};

export default messageService;










