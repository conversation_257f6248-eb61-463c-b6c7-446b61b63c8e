import apiClient from "../apiClient";


export enum MessageApi {

  getMessages = "api/messages/",
  // addReply = "/messages/${id}/add_reply/",
  // DETAIL: (id: string) => `/messages/${id}/`,
  // ASSIGN: (id: string) => `/messages/${id}/assign/`,
  // UPDATE_STATUS: (id: string) => `/messages/${id}/update_status/`,
  // ARCHIVE: (id: string) => `/messages/${id}/archive/`,

  // BULK_ASSIGN: '/bulk-assign/',
}

const MessageApi2 = {
  addReply: (id: string) => `/messages/${id}/add_reply/`,
  deleteMessage: (id: string) => `/messages/${id}/`,

  getReplies: (id: string) => `/replies/?message_id=${id}`,
};




interface GetMessagesParams {
  status?: string;
  category?: string;
  priority?: string;
  assigned_to?: string;
  search?: string;
  page?: number;
  page_size?: number;
  tags?: string;
  date_start?: string;
  date_end?: string;
  archived?: boolean;
  ordering?: string;
}

interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

const getMessages = async (params?: GetMessagesParams): Promise<PaginatedResponse<any>> => {
  try {
    console.log('messageService.getMessages called with params:', params);
    console.log('API URL:', MessageApi.getMessages);

    const response = await apiClient.get<PaginatedResponse<any>>({
      url: MessageApi.getMessages,
      params
    });

    console.log('messageService - Raw API response:', response);
    console.log('messageService - Response type:', typeof response);
    console.log('messageService - Response keys:', Object.keys(response || {}));

    return response;
  } catch (error: any) {
    console.error('messageService - API Error in getMessages:', error);
    console.error('messageService - Error details:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });
    throw error;
  }
};


interface GetRepliesParams {
  message_id: string;
  page?: number;
  page_size?: number;
  phone_number?: string;
  search?: string;
  sender_type?: 'customer' | 'agent';
}

const getReplies = async (params: GetRepliesParams): Promise<PaginatedResponse<any>> => {
  const { message_id, ...queryParams } = params;
  return await apiClient.get<PaginatedResponse<any>>({
    url: `/api/replies/`,
    params: { message_id, ...queryParams }
  });
};

const addReply = async (id: string, content: string) => {
  console.log("id", id, "content", content);
  const data = { content };
  return await apiClient.post({ url: MessageApi2.addReply(id), data });
};

const deleteMessage = async (id: string) => {
  return await apiClient.delete({ url: MessageApi2.deleteMessage(id) });
};



const messageService = {

  getMessages,
  addReply,
  deleteMessage,
  getReplies
};

export default messageService;










