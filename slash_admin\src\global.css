/* font */
@import "@fontsource-variable/open-sans";
@import "@fontsource-variable/inter";

/* editor */
@import "react-quill/dist/quill.snow.css";
/* simplebar */
@import "simplebar-react/dist/simplebar.min.css";

/* tailwind */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --c-bg: transparent;
  --c-scrollbar: #aaa;
  --c-scrollbar-hover: #777;
}
html {
  background-color: var(--c-bg);
  overflow: auto;
}
html.dark {
  --c-bg: transparent;
  --c-scrollbar: #555;
  --c-scrollbar-hover: #888;
}

/* native scrollbar customize */
::-webkit-scrollbar {
  width: 7px;
  cursor: pointer !important;
}
::-webkit-scrollbar:horizontal {
  height: 7px;
}
::-webkit-scrollbar-track,
::-webkit-scrollbar-corner {
  background: var(--c-bg);
  border-radius: 10px;
}
::-webkit-scrollbar-thumb {
  background: var(--c-scrollbar);
  border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--c-scrollbar-hover);
}

/* simplebar customize */
.simplebar-scrollbar::before {
  border-radius: 10px;
  background: var(--c-scrollbar-hover);
}
