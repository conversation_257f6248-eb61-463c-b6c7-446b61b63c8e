#!/usr/bin/env python
"""
Test script to verify WebSocket functionality for the SMS app.
"""

import os
import sys
import django
import asyncio
import websockets
import json
from datetime import datetime

# Add the core directory to Python path
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from sms_app.models import SMSMessage

User = get_user_model()

async def test_message_websocket():
    """Test the message WebSocket consumer"""
    try:
        uri = "ws://localhost:8000/ws/messages/"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to message WebSocket")
            
            # Send a test message
            test_message = {
                "type": "message_update",
                "message_id": "test-id",
                "status": "in-progress",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Sent test message")
            
            # Wait for response (optional)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 Received response: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received (this is normal for this test)")
            
            print("✅ Message WebSocket test completed")
            
    except Exception as e:
        print(f"❌ Message WebSocket test failed: {e}")

async def test_notification_websocket():
    """Test the notification WebSocket consumer"""
    try:
        # Use a test user ID
        user_id = "test-user"
        uri = f"ws://localhost:8000/ws/notifications/{user_id}/"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to notification WebSocket")
            
            # Send a test notification
            test_notification = {
                "type": "mark_read",
                "notification_id": "test-notification-id"
            }
            
            await websocket.send(json.dumps(test_notification))
            print("📤 Sent test notification")
            
            print("✅ Notification WebSocket test completed")
            
    except Exception as e:
        print(f"❌ Notification WebSocket test failed: {e}")

async def test_assignment_websocket():
    """Test the assignment WebSocket consumer"""
    try:
        uri = "ws://localhost:8000/ws/assignments/"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to assignment WebSocket")
            print("✅ Assignment WebSocket test completed")
            
    except Exception as e:
        print(f"❌ Assignment WebSocket test failed: {e}")

async def main():
    """Run all WebSocket tests"""
    print("🧪 Starting WebSocket tests...")
    print("📝 Note: Make sure the Django server is running on localhost:8000")
    print()
    
    await test_message_websocket()
    print()
    await test_notification_websocket()
    print()
    await test_assignment_websocket()
    print()
    print("🎉 All WebSocket tests completed!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"❌ Test runner failed: {e}")
