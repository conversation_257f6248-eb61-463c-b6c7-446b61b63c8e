#!/usr/bin/env python
"""
Simple test script to verify improved error logging functionality.
"""

import os
import sys
import django

# Add the core directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from logs.utils import log_error
from logs.models import ErrorLog

User = get_user_model()


def test_error_logging_format():
    """Test that error logging format is improved"""
    
    # Create or get a test user
    user, created = User.objects.get_or_create(
        username='testuser_error_simple',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Clear existing error logs for clean testing
    ErrorLog.objects.filter(user=user).delete()
    print("🧹 Cleared existing error logs for test user")
    
    # Test 1: Simulate the old generic error message format
    print("\n🧪 Test 1: Old generic error message format")
    
    try:
        # Simulate an error
        raise ValueError("This is a test validation error")
    except Exception as e:
        # Old format (what we DON'T want)
        old_message = 'Failed to create base station'
        print(f"❌ Old format: {old_message}")
        
        # New format (what we DO want)
        new_message = f'BasestationViewSet.create() failed: {type(e).__name__}: {str(e)}'
        print(f"✅ New format: {new_message}")
        
        # Log with new format
        log_error(
            level='ERROR',
            message=new_message,
            user=user,
            traceback=str(e),
            ip_address='127.0.0.1'
        )
    
    # Test 2: Test different exception types
    print("\n🧪 Test 2: Different exception types")
    
    test_exceptions = [
        (KeyError("'required_field' key not found"), "TransformerDataViewSet.update()"),
        (AttributeError("'NoneType' object has no attribute 'save'"), "InspectionViewSet.create()"),
        (IntegrityError("UNIQUE constraint failed"), "BasestationViewSet.bulk_delete()"),
        (ValidationError("Invalid data provided"), "LVFeederViewSet.update()"),
    ]
    
    for exception, method_name in test_exceptions:
        try:
            raise exception
        except Exception as e:
            new_message = f'{method_name} failed: {type(e).__name__}: {str(e)}'
            print(f"✅ {new_message}")
            
            log_error(
                level='ERROR',
                message=new_message,
                user=user,
                traceback=str(e),
                ip_address='127.0.0.1'
            )
    
    # Test 3: Check logged errors
    print("\n🧪 Test 3: Verify logged errors")
    
    error_logs = ErrorLog.objects.filter(user=user).order_by('-timestamp')
    print(f"📊 Total error logs created: {error_logs.count()}")
    
    print("\n📋 Error log messages:")
    for i, error_log in enumerate(error_logs, 1):
        print(f"{i}. {error_log.message}")
        
        # Check if it follows the new format
        if 'failed:' in error_log.message and ':' in error_log.message:
            # Check if it contains method name
            has_method = any(keyword in error_log.message for keyword in ['ViewSet', 'create()', 'update()', 'delete()'])
            # Check if it contains exception type
            has_exception_type = any(exc_type in error_log.message for exc_type in ['Error', 'Exception'])
            
            if has_method and has_exception_type:
                print(f"   ✅ Perfect format: Contains method name and exception type")
            elif has_method:
                print(f"   ✅ Good format: Contains method name")
            elif has_exception_type:
                print(f"   ✅ Good format: Contains exception type")
            else:
                print(f"   ⚠️  Basic format: Uses 'failed:' pattern")
        else:
            print(f"   ❌ Old format: Generic error message")
    
    # Test 4: Compare old vs new format benefits
    print("\n🧪 Test 4: Format comparison")
    
    old_format_examples = [
        "Failed to create base station",
        "Failed to update transformer data",
        "Failed to delete inspection",
        "Failed to bulk delete transformers"
    ]
    
    new_format_examples = [
        "BasestationViewSet.create() failed: ValidationError: station_code is required",
        "TransformerDataViewSet.update() failed: AttributeError: 'NoneType' object has no attribute 'save'",
        "InspectionViewSet.destroy() failed: PermissionError: User does not have permission to delete",
        "TransformerDataViewSet.bulk_delete() failed: IntegrityError: FOREIGN KEY constraint failed"
    ]
    
    print("\n📊 Comparison:")
    print("OLD FORMAT (Generic):")
    for old_msg in old_format_examples:
        print(f"   ❌ {old_msg}")
    
    print("\nNEW FORMAT (Detailed):")
    for new_msg in new_format_examples:
        print(f"   ✅ {new_msg}")
    
    print("\n🎯 Benefits of New Format:")
    print("   • Identifies exact method/function that failed")
    print("   • Shows specific exception type (ValidationError, AttributeError, etc.)")
    print("   • Includes detailed error message from the exception")
    print("   • Makes debugging much easier and faster")
    print("   • Helps identify patterns in errors")
    print("   • Provides context for error resolution")
    
    print("\n✅ Error logging format improvement test completed!")


# Import the exception types we need for testing
from django.core.exceptions import ValidationError
from django.db import IntegrityError


if __name__ == '__main__':
    test_error_logging_format()
