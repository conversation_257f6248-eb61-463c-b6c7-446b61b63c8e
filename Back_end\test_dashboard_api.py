#!/usr/bin/env python
"""
Test script to verify the dashboard statistics API endpoint.
"""

import os
import sys
import django
import json

# Add the core directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from transformer.views import dashboard_statistics
from django.test import RequestFactory

User = get_user_model()


def test_dashboard_api():
    """Test the dashboard statistics API"""
    
    # Create a test client
    client = Client()
    
    # Create or get a test user
    user, created = User.objects.get_or_create(
        username='testuser_dashboard',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Login the user
    client.force_login(user)
    print("✅ User authenticated")
    
    # Test 1: Test dashboard API without region filter
    print("\n🧪 Test 1: Testing dashboard API without region filter")
    
    try:
        response = client.get('/api/transformer/dashboard-statistics/')
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Dashboard API response successful!")
            
            # Check main sections
            required_sections = ['basestations', 'transformers', 'inspections', 'lvFeeders']
            for section in required_sections:
                if section in data:
                    print(f"✅ {section} section present")
                    if 'total' in data[section]:
                        print(f"   - Total {section}: {data[section]['total']}")
                else:
                    print(f"❌ {section} section missing")
            
            # Check basestation statistics
            if 'basestations' in data:
                bs_stats = data['basestations']
                print(f"\n📊 Basestation Statistics:")
                print(f"   - Total: {bs_stats.get('total', 0)}")
                print(f"   - By Type: {len(bs_stats.get('byType', {}))} types")
                print(f"   - By Region: {len(bs_stats.get('byRegion', {}))} regions")
                print(f"   - By CSC: {len(bs_stats.get('byCSC', {}))} CSCs")
            
            # Check transformer statistics
            if 'transformers' in data:
                tr_stats = data['transformers']
                print(f"\n🔧 Transformer Statistics:")
                print(f"   - Total: {tr_stats.get('total', 0)}")
                print(f"   - By Type: {len(tr_stats.get('byType', {}))} types")
                print(f"   - By Capacity: {len(tr_stats.get('byCapacity', {}))} capacities")
                print(f"   - By Manufacturer: {len(tr_stats.get('byManufacturer', {}))} manufacturers")
            
            # Check inspection statistics
            if 'inspections' in data:
                in_stats = data['inspections']
                print(f"\n🔍 Inspection Statistics:")
                print(f"   - Total: {in_stats.get('total', 0)}")
                print(f"   - By Body Condition: {len(in_stats.get('byBodyCondition', {}))} conditions")
                print(f"   - Voltage Ranges: {len(in_stats.get('byVoltageRanges', {}))} ranges")
            
            # Check LV feeder statistics
            if 'lvFeeders' in data:
                lv_stats = data['lvFeeders']
                print(f"\n⚡ LV Feeder Statistics:")
                print(f"   - Total: {lv_stats.get('total', 0)}")
                print(f"   - By Fuse Rating: {len(lv_stats.get('byFuseRating', {}))} ratings")
                print(f"   - Load Current Ranges: {len(lv_stats.get('byLoadCurrentRanges', {}))} ranges")
            
        else:
            print(f"❌ Dashboard API failed with status: {response.status_code}")
            print(f"Response content: {response.content.decode()}")
            
    except Exception as e:
        print(f"❌ Error testing dashboard API: {str(e)}")
    
    # Test 2: Test dashboard API with region filter
    print("\n🧪 Test 2: Testing dashboard API with region filter")
    
    try:
        # First, get available regions
        from transformer.models import Basestation
        regions = Basestation.objects.values_list('region', flat=True).distinct()
        available_regions = [r for r in regions if r]
        
        if available_regions:
            test_region = available_regions[0]
            print(f"Testing with region: {test_region}")
            
            response = client.get(f'/api/transformer/dashboard-statistics/?region={test_region}')
            print(f"Response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Dashboard API with region filter successful!")
                print(f"   - Region filter applied: {data.get('region_filter')}")
                
                # Check that totals are different (should be less than or equal to total)
                if 'basestations' in data:
                    filtered_total = data['basestations'].get('total', 0)
                    print(f"   - Filtered basestation total: {filtered_total}")
                
            else:
                print(f"❌ Dashboard API with region filter failed: {response.status_code}")
        else:
            print("⚠️ No regions available for testing region filter")
            
    except Exception as e:
        print(f"❌ Error testing dashboard API with region filter: {str(e)}")
    
    # Test 3: Test API structure matches mockup
    print("\n🧪 Test 3: Verifying API structure matches mockup requirements")
    
    try:
        response = client.get('/api/transformer/dashboard-statistics/')
        if response.status_code == 200:
            data = response.json()
            
            # Check mockup structure requirements
            mockup_checks = {
                'basestations.total': 'basestations' in data and 'total' in data['basestations'],
                'basestations.byType': 'basestations' in data and 'byType' in data['basestations'],
                'basestations.byRegion': 'basestations' in data and 'byRegion' in data['basestations'],
                'transformers.total': 'transformers' in data and 'total' in data['transformers'],
                'transformers.byType': 'transformers' in data and 'byType' in data['transformers'],
                'transformers.byCapacity': 'transformers' in data and 'byCapacity' in data['transformers'],
                'transformers.byManufacturer': 'transformers' in data and 'byManufacturer' in data['transformers'],
                'inspections.total': 'inspections' in data and 'total' in data['inspections'],
                'inspections.byBodyCondition': 'inspections' in data and 'byBodyCondition' in data['inspections'],
                'inspections.byVoltageRanges': 'inspections' in data and 'byVoltageRanges' in data['inspections'],
                'lvFeeders.total': 'lvFeeders' in data and 'total' in data['lvFeeders'],
                'lvFeeders.byFuseRating': 'lvFeeders' in data and 'byFuseRating' in data['lvFeeders'],
            }
            
            print("📋 Mockup structure verification:")
            for check_name, check_result in mockup_checks.items():
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}")
            
            all_passed = all(mockup_checks.values())
            if all_passed:
                print("🎉 All mockup structure requirements met!")
            else:
                print("⚠️ Some mockup structure requirements not met")
                
    except Exception as e:
        print(f"❌ Error verifying mockup structure: {str(e)}")
    
    print("\n✅ Dashboard API testing completed!")
    print("\n🎯 API Endpoint Information:")
    print("   - URL: /api/transformer/dashboard-statistics/")
    print("   - Method: GET")
    print("   - Optional Query Parameter: region (e.g., ?region=North Region)")
    print("   - Authentication: Required")
    print("   - Response Format: JSON")
    print("\n📖 Usage Examples:")
    print("   - All data: GET /api/transformer/dashboard-statistics/")
    print("   - Filtered by region: GET /api/transformer/dashboard-statistics/?region=North Region")


if __name__ == '__main__':
    test_dashboard_api()
