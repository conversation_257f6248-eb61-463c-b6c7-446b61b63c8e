#!/usr/bin/env python
"""
Test script for SMPP Celery tasks.
Tests the listen_for_sms and send_sms tasks.
"""

import os
import sys
import django

# Add the core directory to Python path
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.conf import settings
from sms_app.tasks import listen_for_sms, send_sms
from sms_app.models import SMSMessage


def test_smpp_config():
    """Test SMPP configuration"""
    print("🧪 Testing SMPP Configuration")
    print("=" * 40)
    
    try:
        config = getattr(settings, 'SMPP_CONFIG', {})
        
        if not config:
            print("❌ SMPP_CONFIG not found in settings")
            return False
        
        required_keys = ['HOST', 'PORT', 'SYSTEM_ID', 'PASSWORD']
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing required config key: {key}")
                return False
            else:
                value = config[key]
                if key == 'PASSWORD':
                    value = '*' * len(str(value))
                print(f"✅ {key}: {value}")
        
        print("✅ SMPP configuration is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error testing SMPP config: {e}")
        return False


def test_task_imports():
    """Test task imports"""
    print("\n🧪 Testing Task Imports")
    print("=" * 40)
    
    try:
        print("✅ listen_for_sms task imported successfully")
        print("✅ send_sms task imported successfully")
        
        # Test task signatures
        print(f"✅ listen_for_sms signature: {listen_for_sms.name}")
        print(f"✅ send_sms signature: {send_sms.name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing task imports: {e}")
        return False


def test_sms_model():
    """Test SMS model with new fields"""
    print("\n🧪 Testing SMS Model")
    print("=" * 40)
    
    try:
        # Test creating SMS message
        test_sms = SMSMessage.objects.create(
            phone_number="251912345678",
            content="Test message for SMPP tasks",
            status="pending",
            category="general",
            priority="medium"
        )
        
        print(f"✅ SMS message created with ID: {test_sms.id}")
        print(f"✅ Phone number: {test_sms.phone_number}")
        print(f"✅ Content: {test_sms.content}")
        print(f"✅ Status: {test_sms.status}")
        
        # Test new SMPP fields
        test_sms.message_id = "test_message_id_123"
        test_sms.status = "sent"
        test_sms.save()
        
        print(f"✅ Message ID field: {test_sms.message_id}")
        print(f"✅ Updated status: {test_sms.status}")
        
        # Clean up
        test_sms.delete()
        print("✅ Test SMS message cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing SMS model: {e}")
        return False


def test_send_sms_task_creation():
    """Test creating SMS for send task"""
    print("\n🧪 Testing Send SMS Task Creation")
    print("=" * 40)
    
    try:
        # Create test SMS
        test_sms = SMSMessage.objects.create(
            phone_number="251912345678",
            content="Test SMS for send task",
            status="pending",
            category="general",
            priority="medium"
        )
        
        print(f"✅ Test SMS created with ID: {test_sms.id}")
        print(f"📱 Phone: {test_sms.phone_number}")
        print(f"💬 Content: {test_sms.content}")
        
        # Note: We won't actually send it since SMPP server is not available
        print("ℹ️ SMS ready for sending (not sent due to SMPP server unavailability)")
        print(f"ℹ️ To send: send_sms.delay('{test_sms.id}')")
        
        # Clean up
        test_sms.delete()
        print("✅ Test SMS cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing send SMS task creation: {e}")
        return False


def test_management_commands():
    """Test management commands"""
    print("\n🧪 Testing Management Commands")
    print("=" * 40)
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # Test send_test_sms command help
        out = StringIO()
        call_command('send_test_sms', '--help', stdout=out)
        help_output = out.getvalue()
        
        if 'Send a test SMS message via SMPP' in help_output:
            print("✅ send_test_sms command available")
        else:
            print("❌ send_test_sms command help not working")
            return False
        
        # Test start_sms_listener command help
        out = StringIO()
        call_command('start_sms_listener', '--help', stdout=out)
        help_output = out.getvalue()
        
        if 'Start the SMS listener task' in help_output:
            print("✅ start_sms_listener command available")
        else:
            print("❌ start_sms_listener command help not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing management commands: {e}")
        return False


def test_smpp_library():
    """Test SMPP library imports"""
    print("\n🧪 Testing SMPP Library")
    print("=" * 40)
    
    try:
        import smpplib.client
        import smpplib.consts
        
        print("✅ smpplib.client imported successfully")
        print("✅ smpplib.consts imported successfully")
        
        # Test creating client (won't connect)
        client = smpplib.client.Client('127.0.0.1', 2775, timeout=1.0)
        print("✅ SMPP client object created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing SMPP library: {e}")
        return False


def show_usage_examples():
    """Show usage examples"""
    print("\n📖 Usage Examples")
    print("=" * 40)
    
    print("🚀 Starting SMS Listener:")
    print("  # Synchronous (blocks current process)")
    print("  python manage.py start_sms_listener --sync")
    print()
    print("  # Asynchronous (requires Celery worker)")
    print("  python manage.py start_sms_listener --async")
    print()
    print("  # Direct task call")
    print("  from sms_app.tasks import listen_for_sms")
    print("  listen_for_sms.delay()  # Async")
    print("  listen_for_sms()        # Sync")
    print()
    
    print("📤 Sending SMS:")
    print("  # Via management command")
    print("  python manage.py send_test_sms --phone 251912345678 --message 'Hello!'")
    print()
    print("  # Asynchronous (requires Celery worker)")
    print("  python manage.py send_test_sms --phone 251912345678 --async")
    print()
    print("  # Direct task call")
    print("  from sms_app.tasks import send_sms")
    print("  from sms_app.models import SMSMessage")
    print("  sms = SMSMessage.objects.create(phone_number='251912345678', content='Hello!')")
    print("  send_sms.delay(str(sms.id))  # Async")
    print("  send_sms(str(sms.id))        # Sync")
    print()
    
    print("🔧 Celery Worker:")
    print("  # Start Celery worker for async tasks")
    print("  celery -A core worker -l info")
    print()
    
    print("📊 Monitoring:")
    print("  # Check SMS status in Django admin or shell")
    print("  python manage.py shell")
    print("  >>> from sms_app.models import SMSMessage")
    print("  >>> SMSMessage.objects.all()")


def main():
    """Run all SMPP task tests"""
    print("🚀 SMPP Celery Tasks Test Suite")
    print("=" * 50)
    
    tests = [
        test_smpp_config,
        test_task_imports,
        test_sms_model,
        test_send_sms_task_creation,
        test_management_commands,
        test_smpp_library,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All SMPP task tests passed!")
        print()
        print("✅ SMPP Configuration: Valid")
        print("✅ Task Imports: Working")
        print("✅ SMS Model: Updated with SMPP fields")
        print("✅ Send SMS Task: Ready")
        print("✅ Management Commands: Available")
        print("✅ SMPP Library: Working")
        print()
        print("🚀 Ready to use SMPP tasks!")
        
        show_usage_examples()
        
    else:
        print(f"⚠️ {total - passed} test(s) failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
        sys.exit(1)
