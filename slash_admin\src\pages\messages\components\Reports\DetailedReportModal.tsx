import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon, PaperClipIcon, CheckIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { useDropzone } from 'react-dropzone';
import { SMSMessage, DetailedReport, ChecklistItem } from '../../../../types';

interface DetailedReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (report: DetailedReport) => void;
  message: SMSMessage;
}

export default function DetailedReportModal({
  isOpen,
  onClose,
  onSubmit,
  message
}: DetailedReportModalProps) {
  const [formData, setFormData] = useState({
    title: `Resolution Report - ${message.caseId}`,
    summary: '',
    detailedDescription: '',
    actionsTaken: '',
    outcome: '',
    recommendations: '',
    followUpRequired: false,
    followUpDate: '',
    followUpNotes: ''
  });

  const [attachments, setAttachments] = useState<File[]>([]);
  const [checklist, setChecklist] = useState<ChecklistItem[]>([
    { id: '1', text: 'Issue identified and analyzed', completed: false },
    { id: '2', text: 'Customer contacted and informed', completed: false },
    { id: '3', text: 'Technical solution implemented', completed: false },
    { id: '4', text: 'Solution tested and verified', completed: false },
    { id: '5', text: 'Customer satisfaction confirmed', completed: false },
    { id: '6', text: 'Documentation completed', completed: false }
  ]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      setAttachments(prev => [...prev, ...acceptedFiles]);
    },
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxSize: 10 * 1024 * 1024 // 10MB
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const report: DetailedReport = {
      id: Date.now().toString(),
      messageId: message.id,
      caseId: message.caseId,
      ...formData,
      checklist,
      attachments: attachments.map(file => ({
        id: Date.now().toString() + Math.random(),
        name: file.name,
        url: URL.createObjectURL(file),
        type: file.type.startsWith('image/') ? 'image' : 'document',
        size: file.size,
        uploadedAt: new Date(),
        uploadedBy: { id: '1', name: 'Current User', email: '', role: 'technician', department: '', isActive: true }
      })),
      createdAt: new Date(),
      createdBy: { id: '1', name: 'Current User', email: '', role: 'technician', department: '', isActive: true }
    };

    onSubmit(report);
    onClose();
  };

  const toggleChecklistItem = (itemId: string) => {
    setChecklist(prev =>
      prev.map(item =>
        item.id === itemId
          ? { ...item, completed: !item.completed, completedAt: !item.completed ? new Date() : undefined }
          : item
      )
    );
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-sky-100 rounded-lg">
                <DocumentTextIcon className="h-6 w-6 text-sky-600" />
              </div>
              <div>
                <Dialog.Title className="text-lg font-medium text-gray-900">
                  Detailed Resolution Report
                </Dialog.Title>
                <p className="text-sm text-gray-500">Case: {message.caseId}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Report Title</label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Case Summary</label>
                <input
                  type="text"
                  required
                  value={formData.summary}
                  onChange={(e) => setFormData({ ...formData, summary: e.target.value })}
                  placeholder="Brief summary of the issue and resolution"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                />
              </div>
            </div>

            {/* Detailed Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Detailed Description</label>
              <textarea
                required
                value={formData.detailedDescription}
                onChange={(e) => setFormData({ ...formData, detailedDescription: e.target.value })}
                rows={4}
                placeholder="Provide a comprehensive description of the issue, investigation process, and findings..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
              />
            </div>

            {/* Actions Taken */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Actions Taken</label>
              <textarea
                required
                value={formData.actionsTaken}
                onChange={(e) => setFormData({ ...formData, actionsTaken: e.target.value })}
                rows={3}
                placeholder="List all actions taken to resolve the issue..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
              />
            </div>

            {/* Outcome and Recommendations */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Outcome</label>
                <textarea
                  required
                  value={formData.outcome}
                  onChange={(e) => setFormData({ ...formData, outcome: e.target.value })}
                  rows={3}
                  placeholder="Describe the final outcome and current status..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Recommendations</label>
                <textarea
                  value={formData.recommendations}
                  onChange={(e) => setFormData({ ...formData, recommendations: e.target.value })}
                  rows={3}
                  placeholder="Any recommendations for preventing similar issues..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                />
              </div>
            </div>

            {/* Task Checklist */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">Task Checklist</label>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                {checklist.map(item => (
                  <div key={item.id} className="flex items-center space-x-3">
                    <button
                      type="button"
                      onClick={() => toggleChecklistItem(item.id)}
                      className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-colors ${
                        item.completed
                          ? 'bg-green-500 border-green-500 text-white'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {item.completed && <CheckIcon className="h-3 w-3" />}
                    </button>
                    <span className={`text-sm ${
                      item.completed ? 'text-gray-500 line-through' : 'text-gray-900'
                    }`}>
                      {item.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Follow-up Section */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center mb-4">
                <input
                  type="checkbox"
                  checked={formData.followUpRequired}
                  onChange={(e) => setFormData({ ...formData, followUpRequired: e.target.checked })}
                  className="h-4 w-4 text-sky-600 focus:ring-sky-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm font-medium text-gray-700">
                  Follow-up Required
                </label>
              </div>
              
              {formData.followUpRequired && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Follow-up Date</label>
                    <input
                      type="date"
                      value={formData.followUpDate}
                      onChange={(e) => setFormData({ ...formData, followUpDate: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Follow-up Notes</label>
                    <textarea
                      value={formData.followUpNotes}
                      onChange={(e) => setFormData({ ...formData, followUpNotes: e.target.value })}
                      rows={2}
                      placeholder="Notes for follow-up actions..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-sky-500 focus:border-sky-500"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Attachments</label>
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? 'border-sky-400 bg-sky-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <input {...getInputProps()} />
                <PaperClipIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  {isDragActive
                    ? 'Drop files here...'
                    : 'Drag & drop files here, or click to select'}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Images, PDFs, and documents up to 10MB
                </p>
              </div>

              {/* Attachment List */}
              {attachments.length > 0 && (
                <div className="mt-4 space-y-2">
                  {attachments.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <PaperClipIcon className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{file.name}</p>
                          <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeAttachment(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors"
              >
                Submit Report
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}