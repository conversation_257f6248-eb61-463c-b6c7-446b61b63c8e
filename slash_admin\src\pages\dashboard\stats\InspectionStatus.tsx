import React, { useState, useEffect } from 'react';
import { Table, Spin, DatePicker, Space, Button, Form, Row, Col } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';
import Scrollbar from "@/components/scrollbar";
import dayjs from 'dayjs';
import type { RangePickerProps } from 'antd/es/date-picker';
import apiClient from '@/api/apiClient';
import { C, co } from 'node_modules/@fullcalendar/core/internal-common';

const { RangePicker } = DatePicker;

interface InspectionData {
  key: string;
  region: string;
  csc?: string;
  children?: InspectionData[];
  inspected: number;
  notInspected: number;
  inspectedPercentage: number;
  notInspectedPercentage: number;
}

interface FilterValues {
  dateRange: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null;
}

const InspectionStatus: React.FC = () => {
  const [data, setData] = useState<InspectionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [form] = Form.useForm();

  const columns: ColumnsType<InspectionData> = [
    {
      title: 'Region/CSC',
      dataIndex: 'region',
      key: 'region',
      fixed: 'left',
      width: 200,
    },
    {
      title: 'CSC',
      dataIndex: 'csc',
      key: 'csc',
      width: 150,
    },
    {
      title: 'Inspected',
      children: [
        {
          title: 'Count',
          dataIndex: 'inspected',
          key: 'inspected',
          width: 100,
        },
        {
          title: '%',
          dataIndex: 'inspectedPercentage',
          key: 'inspectedPercentage',
          width: 80,
          render: (value: number) => `${value}%`,
        },
      ],
    },
    {
      title: 'Not Inspected',
      children: [
        {
          title: 'Count',
          dataIndex: 'notInspected',
          key: 'notInspected',
          width: 100,
        },
        {
          title: '%',
          dataIndex: 'notInspectedPercentage',
          key: 'notInspectedPercentage',
          width: 80,
          render: (value: number) => `${value}%`,
        },
      ],
    },
    {
      title: 'Total',
      children: [
        {
          title: 'Count',
          key: 'total',
          width: 100,
          render: (_, record) => record.inspected + record.notInspected,
        },
        {
          title: '%',
          key: 'totalPercentage',
          width: 80,
          render: () => '100%',
        },
      ],
    },
  ];

  const calculatePercentage = (value: number, total: number): number => {
    return total === 0 ? 0 : Math.round((value / total) * 100);
  };

  const fetchData = async (filters?: FilterValues) => {
    console.log('Fetching data...', filters);
    setLoading(true);
    try {
      let url = `/api/transformer/inspection-status/`;
      
      // Add date range parameters if they exist
      if (filters?.dateRange && filters.dateRange[0] && filters.dateRange[1]) {
        console.log('Date range:', filters.dateRange);
        const startDate = filters.dateRange[0].format('YYYY-MM-DD');
        const endDate = filters.dateRange[1].format('YYYY-MM-DD');
        url += `?start_date=${startDate}&end_date=${endDate}`;

        console.log('url with date range:', url);
      }

      // console.log('start_date:', startDate);
      // console.log('end_date:', endDate);
      // console.log('url:', url);

      // const response = await axios.get(url);
      const response = await apiClient.get<any>({ url: url });
      console.log(" response.data'sSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS", response.data);
      const transformedData = response.map((region: any) => {
        const regionTotal = region.inspected + region.not_inspected;
        
        return {
          key: region.key,
          region: region.region,
          inspected: region.inspected,
          notInspected: region.not_inspected,
          inspectedPercentage: calculatePercentage(region.inspected, regionTotal),
          notInspectedPercentage: calculatePercentage(region.not_inspected, regionTotal),
          children: region.children?.map((csc: any) => {
            const cscTotal = csc.inspected + csc.not_inspected;
            return {
              key: csc.key,
              region: csc.region,
              csc: csc.csc,
              inspected: csc.inspected,
              notInspected: csc.not_inspected,
              inspectedPercentage: calculatePercentage(csc.inspected, cscTotal),
              notInspectedPercentage: calculatePercentage(csc.not_inspected, cscTotal),
            };
          }),
        };
      });
      setData(transformedData);
    } catch (error) {
      console.error('Error fetching inspection status:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleFilter = async (values: FilterValues) => {
    await fetchData(values);
  };

  const handleReset = () => {
    form.resetFields();
    fetchData();
  };

  const renderGrandTotalRow = (data: InspectionData[]) => {
    const totalInspected = data.reduce((sum, region) => sum + region.inspected, 0);
    const totalNotInspected = data.reduce((sum, region) => sum + region.notInspected, 0);
    const grandTotal = totalInspected + totalNotInspected;
    
    return (
      <Table.Summary.Row>
        <Table.Summary.Cell index={0}>Grand Total</Table.Summary.Cell>
        <Table.Summary.Cell index={1}></Table.Summary.Cell>
        <Table.Summary.Cell index={2}>{totalInspected}</Table.Summary.Cell>
        <Table.Summary.Cell index={3}>
          {calculatePercentage(totalInspected, grandTotal)}%
        </Table.Summary.Cell>
        <Table.Summary.Cell index={4}>{totalNotInspected}</Table.Summary.Cell>
        <Table.Summary.Cell index={5}>
          {calculatePercentage(totalNotInspected, grandTotal)}%
        </Table.Summary.Cell>
        <Table.Summary.Cell index={6}>{grandTotal}</Table.Summary.Cell>
        <Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
      </Table.Summary.Row>
    );
  };

  // Disallow selecting future dates
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    return current && current > dayjs().endOf('day');
  };

  if (loading) {
    return (
      <div className="h-[400px] flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      <h2 className="text-xl font-bold mb-4">Inspection Status</h2>
      
      {/* Filter Form */}
      <Form
        form={form}
        onFinish={handleFilter}
        className="mb-4"
        layout="vertical"
      >
        <Row gutter={16}>
          <Col xs={24} sm={12} md={8}>
            <Form.Item 
              name="dateRange"
              label="Filter by Date Range"
            >
              <RangePicker
                className="w-full"
                disabledDate={disabledDate}
                format="YYYY-MM-DD"
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12} md={8} className="flex items-end">
            <Space>
              <Button type="primary" htmlType="submit">
                Apply Filter
              </Button>
              <Button onClick={handleReset}>
                Reset
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      <Scrollbar>
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          bordered
          size="middle"
          scroll={{ x: 'max-content' }}
          className="shadow-lg"
          summary={(currentData) => renderGrandTotalRow(currentData as InspectionData[])}
        />
      </Scrollbar>
    </div>
  );
};

export default InspectionStatus;
