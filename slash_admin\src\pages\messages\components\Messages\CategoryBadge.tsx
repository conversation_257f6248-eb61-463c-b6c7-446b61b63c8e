import React from 'react';
import clsx from 'clsx';

interface CategoryBadgeProps {
  category: 'power-outage' | 'wire-cut' | 'fallen-pole' | 'corruption' | 'billing' | 'general' | 'other';
}

const categoryConfig = {
  'power-outage': {
    label: 'Power Outage',
    className: 'bg-red-100 text-red-700'
  },
  'wire-cut': {
    label: 'Wire Cut',
    className: 'bg-orange-100 text-orange-700'
  },
  'fallen-pole': {
    label: 'Fallen Pole',
    className: 'bg-red-100 text-red-700'
  },
  'corruption': {
    label: 'Corruption',
    className: 'bg-purple-100 text-purple-700'
  },
  'billing': {
    label: 'Billing',
    className: 'bg-blue-100 text-blue-700'
  },
  'general': {
    label: 'General',
    className: 'bg-gray-100 text-gray-700'
  },
  'other': {
    label: 'Other',
    className: 'bg-gray-100 text-gray-700'
  }
};

export default function CategoryBadge({ category }: CategoryBadgeProps) {
  const config = categoryConfig[category];
  
  return (
    <span className={clsx(
      'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
      config.className
    )}>
      {config.label}
    </span>
  );
}