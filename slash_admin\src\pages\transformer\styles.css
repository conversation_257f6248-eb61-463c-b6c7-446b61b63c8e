/* General Styles */
.dashboard-container {
  font-family: Arial, sans-serif;
  padding: 20px;
}

.section {
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 15px;
  background-color: #f9f9f9;
}

h3 {
  margin-top: 0;
  color: #333;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

button {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-button {
  background-color: #28a745;
  color: white;
}

.save-button {
  background-color: #007bff;
  color: white;
}

.cancel-button {
  background-color: #dc3545;
  color: white;
}

/* Lists */
.country-list,
.state-list {
  list-style-type: none;
  padding: 0;
}

.country-item,
.state-item {
  margin: 5px 0;
  padding: 5px;
  border-bottom: 1px solid #ddd;
}

.job-count {
  font-weight: bold;
  color: #007bff;
}

.status {
  font-size: 0.9em;
  padding: 3px 6px;
  border-radius: 4px;
}

.enabled {
  background-color: #28a745;
  color: white;
}

.disabled {
  background-color: #dc3545;
  color: white;
}

/* Dropdowns */
.dropdown {
  padding: 8px;
  margin-top: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 100%;
  max-width: 300px;
}

/* Skill Metadata */
.skill-section p {
  margin: 5px 0;
}

.skill-section strong {
  font-weight: bold;
  color: #333;
}