# import os
# from celery import Celery

# # Set the default Django settings module for the 'celery' program.
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

# app = Celery('core')

# # Using a string here means the worker doesn't have to serialize
# # the configuration object to child processes.
# app.config_from_object('django.conf:settings', namespace='CELERY')

# # Load task modules from all registered Django apps.
# app.autodiscover_tasks()


# @app.task(bind=True)
# def debug_task(self):
#     print(f'Request: {self.request!r}')




# import os
# from celery import Celery
# from celery.signals import worker_ready
# from django.conf import settings

# # Set the default Django settings module for the 'celery' program.
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_project.settings')

# app = Celery('sms_project')

# # Using a string here means the worker doesn't have to serialize
# # the configuration object to child processes.
# app.config_from_object('django.conf:settings', namespace='CELERY')

# # Load task modules from all registered Django apps.
# app.autodiscover_tasks()


# @app.task(bind=True)
# def debug_task(self):
#     print(f'Request: {self.request!r}')

# @worker_ready.connect
# def on_worker_ready(**kwargs):
#     """
#     Trigger the listen_for_sms task when the Celery worker is ready.
#     """
#     from sms_app.tasks import listen_for_sms
#     logger = logging.getLogger(__name__)
#     logger.info("Celery worker is ready, starting listen_for_sms task...")
#     listen_for_sms.delay()  # Run asynchronously to avoid blocking the worker




import os
import logging
from celery import Celery
from celery.signals import worker_ready
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

app = Celery('core')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')

@worker_ready.connect
def on_worker_ready(**kwargs):
    """
    Trigger the listen_for_sms task when the Celery worker is ready.
    """
    from sms_app.tasks import listen_for_sms
    logger = logging.getLogger(__name__)
    logger.info("Celery worker is ready, starting listen_for_sms task...")
    listen_for_sms.delay()  # Run asynchronously to avoid blocking the worker