# SKIP CI in commit message to skip hooks
skip_output:
  - meta
  - success
  - summary

pre-commit:
  parallel: true
  commands:
    format:
      glob: "*.{js,jsx,ts,tsx,json,md}"
      run: npx @biomejs/biome format --write --no-errors-on-unmatched {staged_files}
      stage_fixed: true
    lint:
      glob: "*.{js,jsx,ts,tsx}"
      run: npx @biomejs/biome lint --no-errors-on-unmatched {staged_files}
    check-types:
      glob: "*.{ts,tsx}"
      run: npx tsc --noEmit

commit-msg:
  commands:
    commitlint:
      run: npx commitlint --edit {1}
      # 允许空提交信息
      skip: merge|rebase
