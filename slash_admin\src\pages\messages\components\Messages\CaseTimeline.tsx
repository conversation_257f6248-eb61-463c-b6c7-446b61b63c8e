import React from 'react';
import { format } from 'date-fns';
import { TimelineEvent } from '../../../../types';
import {
  ChatBubbleLeftIcon,
  UserIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface CaseTimelineProps {
  events: TimelineEvent[];
}

export default function CaseTimeline({ events }: CaseTimelineProps) {
  const getEventIcon = (type: string) => {
    switch (type) {
      case 'message':
        return ChatBubbleLeftIcon;
      case 'assignment':
        return UserIcon;
      case 'status_change':
        return ArrowPathIcon;
      case 'reply':
        return ChatBubbleLeftIcon;
      case 'report':
        return ClipboardDocumentListIcon;
      case 'note':
        return DocumentTextIcon;
      default:
        return ExclamationTriangleIcon;
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'message':
        return 'text-blue-600 bg-blue-100';
      case 'assignment':
        return 'text-purple-600 bg-purple-100';
      case 'status_change':
        return 'text-green-600 bg-green-100';
      case 'reply':
        return 'text-sky-600 bg-sky-100';
      case 'report':
        return 'text-orange-600 bg-orange-100';
      case 'note':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (events.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <ClipboardDocumentListIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p>No timeline events yet</p>
      </div>
    );
  }

  return (
    <div className="flow-root">
      <ul className="-mb-8">
        {events.map((event, eventIdx) => {
          const Icon = getEventIcon(event.type);
          const colorClasses = getEventColor(event.type);
          
          return (
            <li key={event.id}>
              <div className="relative pb-8">
                {eventIdx !== events.length - 1 ? (
                  <span
                    className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                    aria-hidden="true"
                  />
                ) : null}
                <div className="relative flex space-x-3">
                  <div>
                    <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${colorClasses}`}>
                      <Icon className="h-4 w-4" aria-hidden="true" />
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-900">{event.user.name}</span>
                        <span className="text-gray-500 ml-2">{event.description}</span>
                      </div>
                      <p className="mt-0.5 text-xs text-gray-500">
                        {format(event.timestamp, 'MMM d, yyyy h:mm a')}
                      </p>
                    </div>
                    {event.metadata && (
                      <div className="mt-2 text-sm text-gray-700 bg-gray-50 rounded-lg p-3">
                        {typeof event.metadata === 'string' ? (
                          <p>{event.metadata}</p>
                        ) : (
                          <pre className="whitespace-pre-wrap text-xs">
                            {JSON.stringify(event.metadata, null, 2)}
                          </pre>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
}