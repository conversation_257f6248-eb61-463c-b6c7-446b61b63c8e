import apiClient from "../apiClient";

// import type { Organization } from "#/entity";

export enum OrgApi {
	// Org = "/org",
	Org = "auth/regions/",
	updateOrganization = "auth/regions/:id/",

	createRegion = "auth/regions/",
	updateRegion = "auth/regions/:csc_code/",
	getRegionOnly = "auth/regionsOnly/",
	deleteRegion = "auth/regions/:csc_code/",

	createCSC = "auth/csc/",
	updateCSC = "auth/csc/:csc_code/",
	getRegionCSC = "auth/csc/",
	deleteCSC = "auth/csc/:csc_code/",
	getCSCOnly = "auth/regions/:csc_code/csc/",
}

const getOrgList = () => apiClient.get<any>({ url: OrgApi.Org });
const updateOrganization = (key: string, data: Partial<any>) =>
	apiClient.put<any>({ url: OrgApi.updateOrganization.replace(":id", key), data });

const createRegion = (data: any) => apiClient.post<any>({ url: OrgApi.createRegion, data });
const updateRegion = (csc_code: string, data: Partial<any>) =>
	apiClient.put<any>({ url: OrgApi.updateRegion.replace(":csc_code", csc_code), data });

const deleteRegion = async (csc_code: string): Promise<void> =>
	apiClient.delete<any>({ url: OrgApi.deleteRegion.replace(":csc_code", csc_code) });

const getRegionOnly = () => apiClient.get<any>({ url: OrgApi.getRegionOnly });

const createCSC = (data: any) => apiClient.post<any>({ url: OrgApi.createCSC, data });
const updateCSC = (csc_code: string, data: Partial<any>) =>
	apiClient.put<any>({ url: OrgApi.updateCSC.replace(":csc_code", csc_code), data });

const deleteCSC = async (csc_code: string): Promise<void> =>
	apiClient.delete<any>({ url: OrgApi.deleteCSC.replace(":csc_code", csc_code) });

const getCSCOnly = (csc_code: string) => apiClient.get<any>({ url: OrgApi.getCSCOnly.replace(":csc_code", csc_code) });

const orgService = {
	getOrgList,
	updateOrganization,
	createRegion,
	updateRegion,
	getRegionOnly,
	deleteRegion,

	createCSC,
	updateCSC,
	deleteCSC,
	getCSCOnly,

	deleteSubstation: async (id: string) => {
		// const response = await apiClient.delete<any>({ url: `${import.meta.env.VITE_APP_BASE_API}/auth/substation/${id}/` });
		const response = await apiClient.delete<any>({ url: `/auth/substation/${id}/` });
		return response;
	},

	deleteFeeder: async (id: string) => {
		const response = await apiClient.delete<any>({ url: `/auth/feeders/${id}/` });
		return response;
	},

	createSubstation: async (data: any) => {
		// const response = await apiClient.post<any>({ url: `${import.meta.env.VITE_APP_BASE_API}/auth/substation/`, data });
		const response = await apiClient.post<any>({ url: `/auth/substation/`, data });
		return response;
	},

	createFeeder: async (data: any) => {
		const response = await apiClient.post<any>({ url: '/auth/feeders/', data });
		return response;
	},

	updateSubstation: async (id: number, data: any) => {
		const response = await apiClient.put<any>({ url: `/auth/substation/${id}/`, data });
		return response;
	},

	addFeeder: async (substationId: number | string, data: any) => {
		try {
			// Ensure substationId is properly formatted
			const feederData = {
				...data,
				substation: substationId
			};
			console.log('Sending feeder data:', feederData); // Debug log

			const response = await apiClient.post<any>({ 
				url: '/auth/feeders/', 
				data: feederData 
			});
			return response;
		} catch (error: any) {
			console.error('Add Feeder Error:', error.response?.data || error);
			if (error.response?.data?.error) {
				throw new Error(error.response.data.error);
			}
			if (error.response?.status === 401) {
				throw new Error('Please login to continue');
			}
			throw new Error('Failed to add feeder');
		}
	},

	updateFeeder: async (id: string, data: any) => {
		try {
			// Only send the feeder data without substation field
			const feederData = {
				feeder_name: data.feeder_name,
				voltage_level: data.voltage_level,
				peak_load: data.peak_load,
				length: data.length,
				number_of_transformer: data.number_of_transformer
			};
			
			const response = await apiClient.put<any>({ 
				url: `/auth/feeders/${id}/`, 
				data: feederData
			});
			return response;
		} catch (error: any) {
			console.error('Update Feeder Error:', error);
			if (error.response?.status === 401) {
				throw new Error('Please login to continue');
			}
			throw error;
		}
	},

	getFeedersBySubstation: async (substationId: number) => {
		const response = await apiClient.get<any>({ 
			url: `/auth/substations/${substationId}/feeders/` 
		});
		return response;
	}
};

export default orgService;










