from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import random

from sms_app.models import (
    SMSMessage, Reply, Department, MessageTemplate, 
    TimelineEvent, Notification
)

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate database with sample data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create departments
        departments = [
            {'name': 'Technical Support', 'color': '#3B82F6'},
            {'name': 'Customer Service', 'color': '#10B981'},
            {'name': 'Billing', 'color': '#F59E0B'},
            {'name': 'Emergency Response', 'color': '#EF4444'},
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults={'color': dept_data['color']}
            )
            if created:
                self.stdout.write(f'Created department: {dept.name}')

        # Create users
        users_data = [
            {'username': 'john_tech', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'role': 'technician', 'email': '<EMAIL>'},
            {'username': 'sarah_sup', 'first_name': 'Sarah', 'last_name': 'Johnson', 'role': 'supervisor', 'email': '<EMAIL>'},
            {'username': 'mike_tech', 'first_name': 'Mike', 'last_name': 'Davis', 'role': 'technician', 'email': '<EMAIL>'},
            {'username': 'lisa_admin', 'first_name': 'Lisa', 'last_name': 'Wilson', 'role': 'admin', 'email': '<EMAIL>'},
        ]
        
        created_users = []
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'role': user_data['role'],
                    'email': user_data['email'],
                    'department': 'Technical Support'
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                created_users.append(user)
                self.stdout.write(f'Created user: {user.username}')

        # Create message templates
        templates_data = [
            {'name': 'Power Outage Response', 'category': 'power-outage', 'content': 'Thank you for reporting the power outage. Our team is investigating and will restore power as soon as possible.'},
            {'name': 'Wire Cut Response', 'category': 'wire-cut', 'content': 'We have received your report about the wire cut. A technician has been dispatched to assess and repair the damage.'},
            {'name': 'Billing Inquiry', 'category': 'billing', 'content': 'Thank you for your billing inquiry. We will review your account and respond within 24 hours.'},
            {'name': 'General Response', 'category': 'general', 'content': 'Thank you for contacting us. Your message has been received and will be addressed shortly.'},
        ]
        
        for template_data in templates_data:
            template, created = MessageTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'category': template_data['category'],
                    'content': template_data['content'],
                    'variables': []
                }
            )
            if created:
                self.stdout.write(f'Created template: {template.name}')

        # Create sample SMS messages
        sample_messages = [
            {'phone': '+************', 'content': 'Power is out in our area since 2 hours. When will it be restored?', 'category': 'power-outage', 'priority': 'high'},
            {'phone': '+************', 'content': 'There is a fallen wire on the main road. Very dangerous!', 'category': 'wire-cut', 'priority': 'critical'},
            {'phone': '+251933345678', 'content': 'My electricity bill seems too high this month. Can you check?', 'category': 'billing', 'priority': 'medium'},
            {'phone': '+251944456789', 'content': 'The street light has been flickering for days.', 'category': 'general', 'priority': 'low'},
            {'phone': '+251955567890', 'content': 'Transformer is making loud noise and sparking.', 'category': 'power-outage', 'priority': 'critical'},
            {'phone': '+251966678901', 'content': 'When will the scheduled maintenance be completed?', 'category': 'general', 'priority': 'medium'},
        ]
        
        technicians = User.objects.filter(role='technician')
        
        for i, msg_data in enumerate(sample_messages):
            # Create message with timestamp spread over last few days
            timestamp = timezone.now() - timedelta(days=random.randint(0, 7), hours=random.randint(0, 23))
            
            message = SMSMessage.objects.create(
                phone_number=msg_data['phone'],
                content=msg_data['content'],
                category=msg_data['category'],
                priority=msg_data['priority'],
                status=random.choice(['new', 'in-progress', 'replied', 'closed']),
                timestamp=timestamp,
                assigned_to=random.choice(technicians) if technicians and random.choice([True, False]) else None,
                tags=['customer-report', msg_data['category']]
            )
            
            # Create some replies for some messages
            if random.choice([True, False]) and technicians:
                Reply.objects.create(
                    message=message,
                    content=f"Thank you for reporting this issue. We are looking into it.",
                    sender=random.choice(technicians),
                    timestamp=timestamp + timedelta(hours=random.randint(1, 4))
                )
            
            # Create timeline events
            TimelineEvent.objects.create(
                message=message,
                type='message',
                user=message.assigned_to or random.choice(technicians) if technicians else None,
                description=f"Message received from {message.phone_number}",
                timestamp=timestamp
            )
            
            self.stdout.write(f'Created message: {message.case_id}')

        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
