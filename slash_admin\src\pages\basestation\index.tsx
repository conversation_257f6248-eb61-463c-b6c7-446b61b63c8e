import React, { useCallback, useEffect, useState, useMemo } from "react";
import { Table, Button, Card, Col, Popconfirm, Row, Space, Form, Select, DatePicker, Input, Checkbox, message, Modal } from "antd";
import type { ColumnsType } from "antd/es/table";
import type { TablePaginationConfig } from "antd/es/table";
import type { FilterValue, SorterResult } from "antd/es/table/interface";
import { FilterOutlined } from '@ant-design/icons';
import { debounce } from "lodash";
import moment from 'moment';

import transformerService from "@/api/services/transformerService";
import { BasestationModal, type BasestationModalProps } from "./basestation-modal";
import { IconButton, Iconify } from "@/components/icon";
import type { Basestation } from "#/entity";
import { toast } from "sonner";
import { usePathname, useRouter } from "@/router/hooks";
import { cleanParams } from "@/lib/utils";
import orgService from "@/api/services/orgService";
import { useUserInfo } from "@/store/userStore";
import * as XLSX from "xlsx";
import dayjs from 'dayjs';

const STATION_TYPES = [
	{ value: 'Single Wooden Pole', label: 'Single Wooden Pole' },
	{ value: 'Single Concrete Pole', label: 'Single Concrete Pole' },
	{ value: 'Single Steel Pole', label: 'Single Steel Pole' },
	{ value: 'Double Wooden Pole', label: 'Double Wooden Pole' },
	{ value: 'Double Concrete Pole', label: 'Double Concrete Pole' },
	{ value: 'Double Steel Pole', label: 'Double Steel Pole' },
	{ value: 'Triple Wooden Pole', label: 'Triple Wooden Pole' },
	{ value: 'Triple Concrete Pole', label: 'Triple Concrete Pole' },
	{ value: 'Triple Steel Pole', label: 'Triple Steel Pole' },
	{ value: 'Quadruple Wooden Pole', label: 'Quadruple Wooden Pole' },
	{ value: 'Quadruple Concrete Pole', label: 'Quadruple Concrete Pole' },
	{ value: 'Quadruple Steel Pole', label: 'Quadruple Steel Pole' },
	{ value: 'Ground Seat Foundation Elevated', label: 'Ground Seat Foundation Elevated' },
	{ value: 'Ground Seat Foundation Ground Level', label: 'Ground Seat Foundation Ground Level' },
	{ value: 'Net Station', label: 'Net Station' },
];

const DEFAULT_PAGINATION: TablePaginationConfig = {
  current: 1,
  pageSize: 10,
};

const DEFAULT_FILTERS = {
  region: '',
  csc: '',
  substation: '',
  feeder: '',
  station_type: '',
  station_code: '',
  address: '',
  gps_location: '',
  dateCreated: null,
  dateUpdated: null,
  without_transformer: false,
  created_by: null,
  updated_by: null,
};

const DEFAULT_BASESTATION_VALUE: Basestation = {
  station_code: "",
  region: "",
  csc: "",
  substation: "",
  feeder: "",
  address: "",
  gps_location: "",
  accuracy: "",
  station_type: "",
  created_at: new Date(),
  updated_at: new Date(),
  created_by: null,
  updated_by: null,
};

// Types
interface TableParams {
  pagination: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue>;
}

interface FilterState {
  region: string;
  csc: string;
  substation: string;
  feeder: string;
  station_type: string;
  station_code: string;
  address: string;
  gps_location: string;
  dateCreated: [moment.Moment, moment.Moment] | null;
  dateUpdated: [moment.Moment, moment.Moment] | null;
  created_date_range: [string | null, string | null];
  without_transformer: boolean;
  created_by: string;
  updated_by: string;
}

// Components
const FilterForm: React.FC<{
  filters: FilterState;
  regions: any[];
  selectedCSCs: any[];
  selectedSubstations: any[];
  selectedFeeders: any[];
  onRegionChange: (value: string) => void;
  onCSCChange: (value: string) => void;
  onSubstationChange: (value: string) => void;
  onFeederChange: (value: string) => void;
  onStationCodeChange: (value: string) => void;
  onStationTypeChange: (value: string) => void;
  onAddressChange: (value: string) => void;
  onGPSLocationChange: (value: string) => void;
  onCreatedDateRangeChange: (value: [string | null, string | null]) => void;
  onwithout_transformerChange: (value: boolean) => void;
  onCreatedByChange: (value: string) => void;
  onUpdatedByChange: (value: string) => void;
  onReset: () => void;
  onApply: () => void;
}> = ({
  filters,
  regions,
  selectedCSCs,
  selectedSubstations,
  selectedFeeders,
  onRegionChange,
  onCSCChange,
  onSubstationChange,
  onFeederChange,
  onStationCodeChange,
  onStationTypeChange,
  onAddressChange,
  onGPSLocationChange,
  onCreatedDateRangeChange,
  onwithout_transformerChange,
  onCreatedByChange,
  onUpdatedByChange,
  onReset,
  onApply,
}) => (
  <Form layout="vertical">
    <Row gutter={[16, 16]}>
      

      <Col xs={24} sm={12} md={6}>
        <Form.Item label="Region">
          <Select
            value={filters.region}
            onChange={onRegionChange}
            placeholder="Select Region"
            allowClear
            showSearch
            optionFilterProp="children"
          >
            {regions.map(region => (
              <Select.Option key={region.csc_code} value={region.csc_code}>
                {region.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col xs={24} sm={12} md={6}>
        <Form.Item label="CSC">
          <Select
            value={filters.csc}
            onChange={onCSCChange}
            placeholder="Select CSC"
            allowClear
            showSearch
            optionFilterProp="children"
            disabled={!filters.region}
          >
            {selectedCSCs.map(csc => (
              <Select.Option key={csc.csc_code} value={csc.name}>
                {csc.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col xs={24} sm={12} md={6}>
        <Form.Item label="Substation">
          <Select
            value={filters.substation}
            onChange={onSubstationChange}
            placeholder="Select Substation"
            allowClear
            showSearch
            optionFilterProp="children"
            disabled={!filters.region}
          >
            {selectedSubstations.map((sub: any) => (
              <Select.Option key={sub.id} value={sub.name}>
                {sub.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col xs={24} sm={12} md={6}>
        <Form.Item label="Feeder">
          <Select
            value={filters.feeder}
            onChange={onFeederChange}
            placeholder="Select Feeder"
            allowClear
            showSearch
            optionFilterProp="children"
            disabled={!filters.substation}
          >
            {selectedFeeders.map((feeder: any) => (
              <Select.Option key={feeder.id} value={feeder.feeder_name}>
                {feeder.feeder_name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col xs={24} sm={12} md={6}>
          <Form.Item label="Station Code">
            <Input
              value={filters.station_code || ''}
              onChange={(e) => onStationCodeChange(e.target.value)}
              placeholder="Station Code"
            />
          </Form.Item>
        </Col>

      <Col xs={24} sm={12} md={6}>
        <Form.Item label="Station Type">
          <Select
            value={filters.station_type || undefined}  // Add undefined as fallback
            onChange={onStationTypeChange}
            placeholder="Select Station Type"
            allowClear
            showSearch
          >
            {STATION_TYPES.map(type => (
              <Select.Option key={type.value} value={type.value}>
                {type.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </Col>

      <Col xs={24} sm={12} md={6}>
          <Form.Item label="Address">
            <Input
              value={filters.address || ''}
              onChange={(e) => onAddressChange(e.target.value)}
              placeholder="Address"
            />
          </Form.Item>
        </Col>


        <Col xs={24} sm={12} md={6}>
          <Form.Item label="GPS Location">
            <Input
              value={filters.gps_location || ''}
              onChange={(e) => onGPSLocationChange(e.target.value)}
              placeholder="GPS Location "
            />
          </Form.Item>
        </Col>

      <Col xs={24} sm={12} md={8}>
                <Form.Item label="Created Date Range">
                  <DatePicker.RangePicker
                    value={filters.created_date_range?.[0] 
                      ? [
                          moment(filters.created_date_range[0]),
                          moment(filters.created_date_range[1])
                        ] 
                      : null
                    }
                    onChange={(dates) => {
                      const formattedDates = dates 
                        ? [
                            dates[0]?.format('YYYY-MM-DD'),
                            dates[1]?.format('YYYY-MM-DD')
                          ] 
                        : [null, null];
                      onCreatedDateRangeChange(formattedDates);
                    }}
                    style={{ width: '100%' }}
                    allowClear
                  />
                </Form.Item>
              </Col>

            <Col xs={24} sm={12} md={6}>
                      <Form.Item label="Created By">
                        <Input
                          value={filters.created_by || ''}
                          onChange={(e) => onCreatedByChange(e.target.value)}
                          placeholder="Enter creator"
                        />
                      </Form.Item>
                    </Col>
                    
                    <Col xs={24} sm={12} md={6}>
                      <Form.Item label="Updated By">
                        <Input
                          value={filters.updated_by || ''}
                          onChange={(e) => onUpdatedByChange(e.target.value)}
                          placeholder="Enter updater"
                        />
                      </Form.Item>
                    </Col>


        <Col xs={24} sm={12} md={6}>
          <Form.Item>
            <Checkbox
              checked={!!filters.without_transformer}
              onChange={(e) => onwithout_transformerChange(e.target.checked)}
            >
              BaseStation without Transformer
            </Checkbox>
          </Form.Item>
        </Col>

              
    </Row>

    <Row justify="end" gutter={[8, 8]} className="mt-4">
      <Col>
        <Space>
          <Button onClick={onReset}>Reset</Button>
          <Button type="primary" onClick={onApply}>Apply</Button>
        </Space>
      </Col>
    </Row>
  </Form>
);

// Main Component
export default function BaseStationPage() {
  const { push } = useRouter();
  const pathname = usePathname();
  const { role } = useUserInfo();

  // State management
  const [data, setData] = useState<Basestation[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>(DEFAULT_FILTERS);
  const [activeFilters, setActiveFilters] = useState<FilterState>(DEFAULT_FILTERS);
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: DEFAULT_PAGINATION,
  });
  const [regions, setRegions] = useState<any[]>([]);
  const [selectedCSCs, setSelectedCSCs] = useState<any[]>([]);
  const [selectedSubstations, setSelectedSubstations] = useState<any[]>([]);
  const [selectedFeeders, setSelectedFeeders] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);


  // Modal state with proper typing
  const [modalProps, setModalProps] = useState<BasestationModalProps>(() => ({
    formValue: DEFAULT_BASESTATION_VALUE,
    title: "New",
    show: false,
    onOk: () => setModalProps(prev => ({ ...prev, show: false })),
    onCancel: () => setModalProps(prev => ({ ...prev, show: false })),
    onDataChange: () => {},
  }));

  // Memoized functions
  const fetchData = useCallback(async (params = tableParams) => {
    setLoading(true);
    try {
      const response = await transformerService.getBasestations({
        page: params.pagination?.current || 1,
        pageSize: params.pagination?.pageSize || 10,
        sortField: params.sortField,
        sortOrder: params.sortOrder,
        filters: params.filters,
      });

      if (response.results) {
        setData(response.results);
        setTableParams(prev => ({
          ...prev,
          pagination: {
            ...prev.pagination,
            total: response.count || 0,
          },
        }));
      }
    } catch (error) {
      console.error("Error fetching basestations:", error);
      toast.error("Failed to load basestations");
    } finally {
      setLoading(false);
    }
  }, []);

  const handleFiltersUpdate = useCallback(
    debounce(async (filterParams: Partial<FilterState>, currentPage?: number, pageSize?: number) => {
      const formattedParams = {
        page: currentPage || 1,
        pageSize: pageSize || tableParams.pagination.pageSize,
        searchType: "BaseStation",
        ...filterParams,
        ...(filterParams.dateCreated && {
          created_after: filterParams.dateCreated[0].format('YYYY-MM-DD'),
          created_before: filterParams.dateCreated[1].format('YYYY-MM-DD'),
        }),
        ...(filterParams.dateUpdated && {
          updated_after: filterParams.dateUpdated[0].format('YYYY-MM-DD'),
          updated_before: filterParams.dateUpdated[1].format('YYYY-MM-DD'),
        }),
      };

      const cleanFilters = cleanParams(formattedParams);
    
      try {
        setLoading(true);
        const response = await transformerService.getBasestationsFiltered(cleanFilters);
        if (response.results) {
          setData(response.results);
          setTableParams(prev => ({
            ...prev,
            pagination: {
              ...prev.pagination,
              current: currentPage || 1,
              total: response.count || 0,
            },
          }));
        }
      } catch (error) {
        console.error("Error fetching filtered basestations:", error);
        toast.error("Failed to apply filters");
      } finally {
        setLoading(false);
      }
    }, 300),
    [tableParams.pagination.pageSize]
  );

  const handleDelete = useCallback(async (station_code: string) => {
    try {
      await transformerService.deleteBasestation(station_code);
      toast.success("Basestation deleted successfully!");
      fetchData();
    } catch (error) {
      toast.error("Failed to delete basestation");
    }
  }, [fetchData]);

  const handleDataChange = useCallback((newData: Basestation, isEdit: boolean) => {
    setData(prevData => 
      isEdit 
        ? prevData.map(item => item.station_code === newData.station_code ? newData : item)
        : [newData, ...prevData]
    );
  }, []);

  // Memoized columns definition
  const columns = useMemo<ColumnsType<Basestation>>(() => [
    {
      title: "Station Code",
      dataIndex: "station_code",
      width: 150,
    },
    // {
    //   title: "Station Type",
    //   dataIndex: "station_type",
    //   width: 150,
    // },
    {
      title: "Region",
      dataIndex: "region",
      width: 150,
    },
    {
      title: "CSC",
      dataIndex: "csc",
      width: 100,
    },
    {
      title: "Substation",
      dataIndex: "substation",
      width: 150,
    },
    {
      title: "Feeder",
      dataIndex: "feeder",
      width: 150,
    },
    {
      title: "Address",
      dataIndex: "address",
      width: 200,
    },
    {
      title: "GPS Location",
      dataIndex: "gps_location",
      width: 150,
    },
    {
          title: "Created By",
          dataIndex: "created_by",
          width: 150,
          render: (created_by) => created_by?.username || "-",
        },
        {
          title: "Created At",
          dataIndex: "created_at",
          width: 150,
          render: (date) => date ? dayjs(date).format("M/D/YYYY HH:mm") : "-",
        },
    {
      title: "Action",
      key: "operation",
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="flex w-full justify-center text-gray gap-2">
          <IconButton onClick={() => push(`${pathname}/${record.station_code}`)}>
            <Iconify icon="ix:view" size={18} />
          </IconButton>
          <IconButton onClick={() => onEdit(record)}>
            <Iconify icon="ix:edit" size={18} />
          </IconButton>
          {(role?.name === "SuperAdmin" || role?.name === "Admin") && (
            <Popconfirm
              title="Delete the Basestation?"
              okText="Yes"
              cancelText="No"
              placement="left"
              onConfirm={() => handleDelete(record.station_code)}
              getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
            >
              <IconButton>
                <Iconify icon="ix:delete" size={18} className="text-error" />
              </IconButton>
            </Popconfirm>
          )}
        </div>
      ),
    },
  ], [handleDelete, pathname, push]);

  // Effects
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    setModalProps(prev => ({
      ...prev,
      onDataChange: handleDataChange
    }));
  }, [handleDataChange]);

  useEffect(() => {
    if (Object.keys(activeFilters).length > 0 && 
        Object.values(activeFilters).some(value => value !== '')) {
      handleFiltersUpdate(
        activeFilters,
        tableParams.pagination?.current,
        tableParams.pagination?.pageSize
      );
    } else {
      fetchData(tableParams);
    }
  }, [
    tableParams.pagination?.current,
    tableParams.pagination?.pageSize,
    activeFilters,
    fetchData,
    handleFiltersUpdate
  ]);

  useEffect(() => {
    const loadRegions = async () => {
      try {
        const data = await orgService.getOrgList();
        setRegions(data);
      } catch (error) {
        console.error("Error fetching regions:", error);
        toast.error("Failed to load regions");
      }
    };
    loadRegions();
  }, []);

  // Event handlers
  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue>,
    sorter: SorterResult<Basestation>
  ) => {
    setTableParams({
      pagination,
      filters,
      sortField: sorter.field as string,
      sortOrder: sorter.order,
    });
  };

  const handleRegionChange = (regionCode: string) => {
    const selectedRegion = regions.find(region => region.csc_code === regionCode);
    if (selectedRegion) {
      setSelectedCSCs(selectedRegion.csc_centers);
      setSelectedSubstations(selectedRegion.substations);
      setFilters(prev => ({ 
        ...prev, 
        region: regionCode,
        csc: undefined,
        substation: undefined,
        feeder: undefined
      }));
    }
  };

  const handleResetFilters = () => {
    setFilters(DEFAULT_FILTERS);
    setActiveFilters(DEFAULT_FILTERS);
    setSelectedCSCs([]);
    setSelectedSubstations([]);
    setSelectedFeeders([]);
    setTableParams(prev => ({
      ...prev,
      pagination: DEFAULT_PAGINATION,
    }));
    fetchData({
      pagination: DEFAULT_PAGINATION,
      filters: {},
    });
  };

  const handleApplyFilters = () => {
    setActiveFilters(filters);
    setTableParams(prev => ({
      ...prev,
      pagination: {
        ...prev.pagination,
        current: 1,
        pageSize: 10
      }
    }));
    handleFiltersUpdate(filters, 1, 10);
  };

  const onEdit = (record: Basestation) => {
    setModalProps(prev => ({
      ...prev,
      show: true,
      title: "Edit Basestation",
      formValue: {
        region: record.region,
        csc: record.csc,
        substation: record.substation,
        feeder: record.feeder,
        station_type: record.station_type,
        address: record.address,
        gps_location: record.gps_location,
        station_code: record.station_code,
        // Include any other fields that are part of your Basestation type
      },
      onOk: () => setModalProps(prev => ({ ...prev, show: false })),
      onCancel: () => setModalProps(prev => ({ ...prev, show: false })),
      onDataChange: handleDataChange,
    }));
  };

  const handleExportExcel = () => {
  // Prepare data for export (flatten nested objects if needed)
  const exportData = data.map(item => ({
    "Station Code": item.station_code,
    "Region": item.region,
    "CSC": item.csc,
    "Substation": item.substation,
    "Feeder": item.feeder,
    "Station Type": item.station_type,
    "Address": item.address,
    "GPS Location": item.gps_location,
    "Created At": item.created_at ? new Date(item.created_at).toLocaleString() : "",
    "Updated At": item.updated_at ? new Date(item.updated_at).toLocaleString() : "",
    // Add more fields if needed
  }));

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Basestations");
  XLSX.writeFile(workbook, "basestations.xlsx");
  };


  // Row selection config
const rowSelection = {
  selectedRowKeys,
  onChange: (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  },
};

// Bulk delete handler
// const handleBulkDelete = async () => {
//   if (selectedRowKeys.length === 0) {
//     message.warning("No basestations selected.");
//     return;
//   }
//   try {
//     // Call your API for bulk delete (implement this in transformerService)
//     await transformerService.deleteBasestationsBulk(selectedRowKeys);
//     message.success("Selected basestations deleted!");
//     setSelectedRowKeys([]);
//     fetchData();
//   } catch (error) {
//     message.error("Bulk delete failed.");
//   }
// };

const handleBulkDelete = async () => {
  if (selectedRowKeys.length === 0) {
    message.warning("No basestations selected.");
    return;
  }

  Modal.confirm({
    title: "Confirm Deletion",
    content: `Are you sure you want to delete ${selectedRowKeys.length} selected basestation(s)? This action cannot be undone.`,
    okText: "Yes, Delete",
    okType: "danger",
    cancelText: "Cancel",
    onOk: async () => {
      try {
        await transformerService.deleteBasestationsBulk(selectedRowKeys);
        message.success("Selected basestations deleted!");
        setSelectedRowKeys([]);
        fetchData();
      } catch (error) {
        message.error("Bulk delete failed.");
      }
    },
  });
};

  return (
    <Space direction="vertical" size="large" className="w-full">
      <Card>
        {/* <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
          <h2 className="text-xl font-bold">Basestation List</h2>
          <Space>
            <Button
              icon={<FilterOutlined />}
              onClick={() => setShowFilters(!showFilters)}
              type={showFilters ? "primary" : "default"}
            >
              Filters
            </Button>
            <Button
              danger
              disabled={selectedRowKeys.length === 0}
              onClick={handleBulkDelete}
            >
              Delete Selected
            </Button>
            <Button onClick={handleExportExcel}>
                Export to Excel
              </Button>
            <Button 
              type="primary" 
              onClick={() => setModalProps(prev => ({
                ...prev,
                show: true,
                title: "Create New Basestation",
                formValue: DEFAULT_BASESTATION_VALUE,
              }))}
            >
              New Basestation
            </Button>
          </Space>
        </div> */}

        <Row gutter={[16, 16]} align="middle" className="mb-4">
  <Col xs={24} sm={12}>
    <h2 className="text-xl font-bold text-center sm:text-left mb-2 sm:mb-0">
      Basestation List
    </h2>
  </Col>
  <Col xs={24} sm={12}>
    <Space
      direction="vertical"
      size="middle"
      className="w-full sm:w-auto"
      style={{ width: "100%" }}
    >
      <Space wrap>
        <Button
          icon={<FilterOutlined />}
          onClick={() => setShowFilters(!showFilters)}
          type={showFilters ? "primary" : "default"}
        >
          Filters
        </Button>
        {(role?.name === "SuperAdmin" || role?.name === "Admin") && (
          <Button
            danger
            disabled={selectedRowKeys.length === 0}
            onClick={handleBulkDelete}
          >
            Delete Selected
          </Button>
        )}
        <Button onClick={handleExportExcel}>
          Export to Excel
        </Button>
        <Button
          type="primary"
          onClick={() =>
            setModalProps(prev => ({
              ...prev,
              show: true,
              title: "Create New Basestation",
              formValue: DEFAULT_BASESTATION_VALUE,
            }))
          }
        >
          New Basestation
        </Button>
      </Space>
    </Space>
  </Col>
</Row>

        {showFilters && (
          <div className="mt-4 bg-gray-50 p-4 rounded-lg">
            <FilterForm
              filters={filters}
              regions={regions}
              selectedCSCs={selectedCSCs}
              selectedSubstations={selectedSubstations}
              selectedFeeders={selectedFeeders}
              onRegionChange={handleRegionChange}
              onCSCChange={(value) => setFilters(prev => ({ ...prev, csc: value }))}
              onSubstationChange={(value) => {
                const selectedRegion = regions.find(region => 
                  region.substations.some((sub: any) => sub.name === value)
                );
                if (selectedRegion) {
                  const substation = selectedRegion.substations.find(
                    (sub: any) => sub.name === value
                  );
                  if (substation) {
                    setSelectedFeeders(substation.feeders || []);
                    setFilters(prev => ({ 
                      ...prev, 
                      substation: value,
                      feeder: undefined 
                    }));
                  }
                }
              }}
              onFeederChange={(value) => setFilters(prev => ({ ...prev, feeder: value }))}
              onStationCodeChange={(value) => setFilters(prev => ({ ...prev, station_code: value }))}
              onStationTypeChange={(value) => setFilters(prev => ({ ...prev, station_type: value }))}
              onAddressChange={(value) => setFilters(prev => ({ ...prev, address: value }))}
              onGPSLocationChange={(value) => setFilters(prev => ({ ...prev, gps_location: value }))}
              onCreatedDateRangeChange={(value) => setFilters(prev => ({ ...prev, created_date_range: value }))}
              onwithout_transformerChange={(value) => setFilters(prev => ({ ...prev, without_transformer: value }))}
              onCreatedByChange={(value) => setFilters(prev => ({ ...prev, created_by: value }))}
              onUpdatedByChange={(value) => setFilters(prev => ({ ...prev, updated_by: value }))}
              onReset={handleResetFilters}
              onApply={handleApplyFilters}
            />
          </div>
        )}
      </Card>

      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          rowKey="station_code"
          dataSource={data}
          pagination={{
            ...tableParams.pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} items`,
          }}
          loading={loading}
          onChange={handleTableChange}
        />
        <BasestationModal {...modalProps} />
      </Card>
    </Space>
  );
}





