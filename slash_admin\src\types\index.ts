export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'supervisor' | 'technician' | 'viewer';
  department: string;
  avatar?: string;
  isActive: boolean;
}

export interface SMSMessage {
  id: string;
  phone_number: string;
  content: string;
  timestamp: Date;
  status: 'new' | 'in-progress' | 'replied' | 'closed';
  category: 'power-outage' | 'wire-cut' | 'fallen-pole' | 'corruption' | 'billing' | 'general' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical';
  tags: string[];
  assigned_to?: User;
  caseId: string;
  replies: Reply[];
  attachments: Attachment[];
  isArchived: boolean;
  archivedAt?: Date;
  archivedBy?: User;
}

export interface Reply {
  id: string;
  content: string;
  timestamp: Date;
  sender: User;
  is_from_customer: boolean;
  attachments: Attachment[];
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  type: 'image' | 'document' | 'other';
  size: number;
  uploadedAt: Date;
  uploadedBy: User;
}

export interface Case {
  id: string;
  phone_number: string;
  category: string;
  status: 'received' | 'assigned' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assigned_to?: User;
  createdAt: Date;
  updatedAt: Date;
  messages: SMSMessage[];
  notes: string;
  timeline: TimelineEvent[];
  assignmentHistory: AssignmentHistory[];
  dueDate?: Date;
  taskComment?: string;
  checklist: ChecklistItem[];
  reports: ProgressReport[];
}

export interface TimelineEvent {
  id: string;
  type: 'message' | 'assignment' | 'status_change' | 'reply' | 'report' | 'note';
  timestamp: Date;
  user: User;
  description: string;
  metadata?: any;
}

export interface AssignmentHistory {
  id: string;
  fromUser?: User;
  toUser: User;
  timestamp: Date;
  reason?: string;
  assignedBy: User;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  completedAt?: Date;
  completedBy?: User;
}

export interface ProgressReport {
  id: string;
  content: string;
  timestamp: Date;
  reportedBy: User;
  attachments: Attachment[];
  checklist: ChecklistItem[];
}

export interface Notification {
  id: string;
  type: 'assignment' | 'message' | 'report' | 'status_change';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  userId: string;
  caseId?: string;
  messageId?: string;
}

export interface Department {
  id: string;
  name: string;
  color: string;
}

export interface SystemSettings {
  categories: CategoryConfig[];
  workingHours: WorkingHours;
  autoReplies: AutoReplyConfig[];
  notifications: NotificationSettings;
}

export interface CategoryConfig {
  id: string;
  name: string;
  color: string;
  autoAssignDepartment?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  keywords: string[];
}

export interface WorkingHours {
  monday: { start: string; end: string; enabled: boolean };
  tuesday: { start: string; end: string; enabled: boolean };
  wednesday: { start: string; end: string; enabled: boolean };
  thursday: { start: string; end: string; enabled: boolean };
  friday: { start: string; end: string; enabled: boolean };
  saturday: { start: string; end: string; enabled: boolean };
  sunday: { start: string; end: string; enabled: boolean };
}

export interface AutoReplyConfig {
  id: string;
  category: string;
  template: string;
  enabled: boolean;
  conditions: string[];
}

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  assignmentNotifications: boolean;
  reportNotifications: boolean;
}
export interface Analytics {
  totalCases: number;
  resolvedCases: number;
  pendingCases: number;
  averageResponseTime: number;
  categoryCounts: Record<string, number>;
  statusCounts: Record<string, number>;
  dailyTrends: Array<{
    date: string;
    cases: number;
    resolved: number;
  }>;
}

export interface MessageTemplate {
  id: string;
  name: string;
  category: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DetailedReport {
  id: string;
  messageId: string;
  caseId: string;
  title: string;
  summary: string;
  detailedDescription: string;
  actionsTaken: string;
  outcome: string;
  recommendations: string;
  followUpRequired: boolean;
  followUpDate: string;
  followUpNotes: string;
  checklist: ChecklistItem[];
  attachments: Attachment[];
  createdAt: Date;
  createdBy: User;
}

export interface ApprovalRequest {
  id: string;
  messageId: string;
  type: 'budget_approval' | 'escalation' | 'resource_request' | 'policy_exception';
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  requestedBy: User;
  requestedAt: Date;
  approvers: User[];
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: User;
  approvedAt?: Date;
  comments?: string;
  approvalHistory: ApprovalHistory[];
}

export interface ApprovalHistory {
  id: string;
  decision: 'approved' | 'rejected';
  comments: string;
  approvedBy: User;
  approvedAt: Date;
}

export interface AssignmentStatus {
  id: string;
  messageId: string;
  assigned_to: User;
  assignedBy: User;
  assignedAt: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dueDate: Date;
  estimatedHours: number;
  skillsRequired: string[];
  status: 'assigned' | 'accepted' | 'in_progress' | 'completed' | 'rejected';
  acceptedAt?: Date;
  completedAt?: Date;
  comments: string;
  progressUpdates: ProgressUpdate[];
}

export interface ProgressUpdate {
  id: string;
  content: string;
  timestamp: Date;
  updatedBy: User;
  percentComplete: number;
}