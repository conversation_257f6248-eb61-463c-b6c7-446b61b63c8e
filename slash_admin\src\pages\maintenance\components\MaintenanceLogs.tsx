import React from "react";
import { Card, Timeline, Spin, Typography, Tag } from "antd";
import { ClockCircleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";

interface MaintenanceLog {
	id: number;
	change_type: string;
	old_value: string | null;
	new_value: string | null;
	timestamp: string;
	description: string;
	user_name: string;
}

interface MaintenanceLogsProps {
	logs?: MaintenanceLog[];
	loading: boolean;
}

const getLogColor = (changeType: string): string => {
	switch (changeType) {
		case "STATUS":
			return "blue";
		case "ASSIGNMENT":
			return "green";
		case "PRIORITY":
			return "red";
		case "SCHEDULE":
			return "orange";
		case "DETAILS":
			return "purple";
		default:
			return "gray";
	}
};

const getChangeTypeLabel = (changeType: string): string => {
	switch (changeType) {
		case "STATUS":
			return "Status Change";
		case "ASSIGNMENT":
			return "Assignment Change";
		case "PRIORITY":
			return "Priority Change";
		case "SCHEDULE":
			return "Schedule Change";
		case "DETAILS":
			return "Details Change";
		default:
			return "Other Change";
	}
};

const MaintenanceLogs: React.FC<MaintenanceLogsProps> = ({ logs = [], loading }) => {
	return (
		<Card title={<div className="font-bold">ACTIVITY LOG</div>} className="h-full">
			{loading ? (
				<div className="text-center py-4">
					<Spin />
				</div>
			) : logs && logs.length > 0 ? (
				<Timeline>
					{logs.map((log) => (
						<Timeline.Item
							key={log.id}
							dot={<ClockCircleOutlined style={{ fontSize: "16px" }} />}
							color={getLogColor(log.change_type)}
						>
							<div className="flex flex-col gap-2">
								<div className="flex items-center gap-2">
									<Tag color={getLogColor(log.change_type)}>{getChangeTypeLabel(log.change_type)}</Tag>
									<Typography.Text type="secondary" className="text-sm">
										{dayjs(log.timestamp).format("MMM DD, YYYY h:mm A")}
									</Typography.Text>
								</div>

								<Typography.Text strong>{log.description}</Typography.Text>

								{(log.old_value || log.new_value) && (
									<div className="text-sm">
										{log.old_value && (
											<div>
												<Typography.Text type="secondary">Old value: </Typography.Text>
												<Typography.Text delete>{log.old_value}</Typography.Text>
											</div>
										)}
										{log.new_value && (
											<div>
												<Typography.Text type="secondary">New value: </Typography.Text>
												<Typography.Text>{log.new_value}</Typography.Text>
											</div>
										)}
									</div>
								)}

								<Typography.Text type="secondary" className="text-sm">
									By {log.user_name}
								</Typography.Text>
							</div>
						</Timeline.Item>
					))}
				</Timeline>
			) : (
				<div className="text-center py-4">
					<Typography.Text type="secondary">No activity logs found</Typography.Text>
				</div>
			)}
		</Card>
	);
};

export default MaintenanceLogs;
