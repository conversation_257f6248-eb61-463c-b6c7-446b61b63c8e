import { useState, useEffect } from 'react';
import { MessageTemplate } from '../types';

const mockTemplates: MessageTemplate[] = [
  {
    id: '1',
    name: 'Power Outage Response',
    category: 'power-outage',
    content: 'Thank you for reporting the power outage at {{address}}. Our crew has been dispatched and we estimate restoration within {{hours}} hours. We will keep you updated on progress.',
    variables: ['address', 'hours'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '2',
    name: 'Billing Inquiry Response',
    category: 'billing',
    content: 'Thank you for your billing inquiry regarding account {{account_number}}. I will review your account and respond within {{timeframe}} with detailed information.',
    variables: ['account_number', 'timeframe'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '3',
    name: 'Emergency Wire Down',
    category: 'wire-cut',
    content: 'URGENT: Thank you for reporting the downed wire at {{location}}. Please stay away from the area for safety. A crew is en route and will secure the area within {{eta}} minutes.',
    variables: ['location', 'eta'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: '4',
    name: 'General Acknowledgment',
    category: 'general',
    content: 'Thank you for contacting ElectriCare. Your message has been received and assigned case number {{case_id}}. We will respond within {{response_time}}.',
    variables: ['case_id', 'response_time'],
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];

export function useTemplates() {
  const [templates, setTemplates] = useState<MessageTemplate[]>(mockTemplates);
  const [loading, setLoading] = useState(false);

  const saveTemplate = (template: MessageTemplate) => {
    setTemplates(prev => {
      const existing = prev.find(t => t.id === template.id);
      if (existing) {
        return prev.map(t => t.id === template.id ? template : t);
      } else {
        return [...prev, template];
      }
    });
  };

  const deleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId));
  };

  const getTemplatesByCategory = (category: string) => {
    return templates.filter(t => t.isActive && (t.category === category || t.category === 'general'));
  };

  return {
    templates,
    loading,
    saveTemplate,
    deleteTemplate,
    getTemplatesByCategory
  };
}