import { useState, useEffect } from 'react';
import { MessageTemplate } from '../types';
import apiClient from '../api/apiClient';

export function useTemplates() {
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get({ url: '/api/templates/' });
        const normalizedTemplates = (response.results || response).map((template: any) => ({
          id: template.id,
          name: template.name,
          category: template.category,
          content: template.content,
          variables: template.variables || [],
          isActive: template.is_active,
          createdAt: new Date(template.created_at),
          updatedAt: new Date(template.updated_at)
        }));
        setTemplates(normalizedTemplates);
      } catch (error) {
        console.error('Error fetching templates:', error);
        setTemplates([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  const saveTemplate = (template: MessageTemplate) => {
    setTemplates(prev => {
      const existing = prev.find(t => t.id === template.id);
      if (existing) {
        return prev.map(t => t.id === template.id ? template : t);
      } else {
        return [...prev, template];
      }
    });
  };

  const deleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId));
  };

  const getTemplatesByCategory = (category: string) => {
    return templates.filter(t => t.isActive && (t.category === category || t.category === 'general'));
  };

  return {
    templates,
    loading,
    saveTemplate,
    deleteTemplate,
    getTemplatesByCategory
  };
}