// import { <PERSON><PERSON>, Col, Form, Input, Row } from "antd";
// import Card from "@/components/card";
// import { UploadAvatar } from "@/components/upload";
// import { toast } from "sonner";
// import userService from "@/api/services/userService";
// import type { UserInfo } from "#/entity";

// type FieldType = {
// 	username: string;
// 	email: string;
// 	phone: string;
// 	full_name?: string;
// 	region?: string;
// 	csc?: string;
// 	title?: string;
// 	address: string;
// 	city: string;
// 	about: string;
// };

// interface GeneralTabProps {
// 	user: UserInfo;
// 	onUpdate: () => void;
// }

// export default function GeneralTab({ user, onUpdate }: GeneralTabProps) {
// 	const initFormValues = {
// 		username: user.username || "",
// 		email: user.email || "",
// 		phone: user.phone || "",
// 		full_name: user.full_name || "",
// 		region: user.region || "",
// 		csc: user.csc || "",
// 		title: user.title || "",
// 		address: user.address || "",
// 		city: user.city || "",
// 		about: user.about || "",
// 	};

// 	const [form] = Form.useForm<FieldType>();

// 	const onFinish = async (values: FieldType) => {
// 		try {
// 			const userData = {
// 				username: values.username,
// 				email: values.email,
// 				phone: values.phone,
// 				full_name: values.full_name,
// 				region: values.region,
// 				csc: values.csc,
// 				title: values.title,
// 				address: values.address,
// 				city: values.city,
// 				about: values.about,
// 			};

// 			await userService.adminUpdateUserProfile(user.id, userData);
// 			toast.success("User updated successfully");
// 			onUpdate();
// 		} catch (error) {
// 			toast.error("Failed to update user");
// 			console.error("Update failed:", error);
// 		}
// 	};

// 	return (
// 		<Row gutter={[16, 16]}>
// 			<Col span={24} lg={8}>
// 				<Card className="flex-col !px-6 !pb-10 !pt-20">
// 					<UploadAvatar defaultAvatar={user.avatar} />
// 				</Card>
// 			</Col>
// 			<Col span={24} lg={16}>
// 				<Card>
// 					<Form
// 						form={form}
// 						layout="vertical"
// 						initialValues={initFormValues}
// 						labelCol={{ span: 8 }}
// 						className="w-full"
// 						onFinish={onFinish}
// 					>
// 						<Row gutter={16}>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="Username"
// 									name="username"
// 									rules={[{ required: true, message: "Please input username!" }]}
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="Email"
// 									name="email"
// 									rules={[
// 										{ required: true, message: "Please input email!" },
// 										{ type: "email", message: "Please enter a valid email!" },
// 									]}
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 						</Row>

// 						<Row gutter={16}>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="Full Name"
// 									name="full_name"
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="Title"
// 									name="title"
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 						</Row>

// 						<Row gutter={16}>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="Region"
// 									name="region"
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="CSC"
// 									name="csc"
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 						</Row>



// 						<Row gutter={16}>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="Phone Number"
// 									name="phone"
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 							<Col span={12}>
// 								<Form.Item<FieldType>
// 									label="City"
// 									name="city"
// 								>
// 									<Input />
// 								</Form.Item>
// 							</Col>
// 						</Row>

// 						<Form.Item<FieldType>
// 							label="Address"
// 							name="address"
// 						>
// 							<Input />
// 						</Form.Item>

// 						<Form.Item<FieldType>
// 							label="About"
// 							name="about"
// 						>
// 							<Input.TextArea rows={4} />
// 						</Form.Item>

// 						<div className="flex w-full justify-end">
// 							<Button type="primary" htmlType="submit">
// 								Save Changes
// 							</Button>
// 						</div>
// 					</Form>
// 				</Card>
// 			</Col>
// 		</Row>
// 	);
// }


import React, { useState, useEffect } from "react";
import {
  Button,
  Col,
  Form,
  Input,
  Row,
  Select,
} from "antd";
import Card from "@/components/card";
import { UploadAvatar } from "@/components/upload";
import { toast } from "sonner";
import userService from "@/api/services/userService";
import type { UserInfo } from "#/entity";
import orgService from "@/api/services/orgService";

type FieldType = {
  username: string;
  email: string;
  phone: string;
  full_name?: string;
  region?: string;
  csc?: string;
  title?: string;
  address: string;
  city: string;
  about: string;
};

interface GeneralTabProps {
  user: UserInfo;
  onUpdate: () => void;
}

export default function GeneralTab({ user, onUpdate }: GeneralTabProps) {
  const [form] = Form.useForm<FieldType>();

  const initFormValues = {
    username: user.username || "",
    email: user.email || "",
    phone: user.phone || "",
    full_name: user.full_name || "",
    region: user.region || "",
    csc: user.csc || "",
    title: user.title || "",
    address: user.address || "",
    city: user.city || "",
    about: user.about || "",
  };

  const [regions, setRegions] = useState<any[]>([]);
  const [selectedCSCs, setSelectedCSCs] = useState<any[]>([]);
  const [filters, setFilters] = useState({
    region: user.region || undefined,
    csc: user.csc || undefined,
  });

  useEffect(() => {
    const loadRegions = async () => {
      try {
        const data = await orgService.getOrgList();
        setRegions(data);
      } catch (error) {
        console.error("Error fetching regions:", error);
        toast.error("Failed to load regions");
      }
    };
    loadRegions();
  }, []);

  const handleRegionChange = (regionCode: string) => {
    const selectedRegion = regions.find((region) => region.csc_code === regionCode);
    if (selectedRegion) {
      setSelectedCSCs(selectedRegion.csc_centers || []);
      setFilters((prev) => ({
        ...prev,
        region: regionCode,
        csc: undefined,
      }));
      form.setFieldsValue({ csc: undefined });
    }
  };

  const onCSCChange = (value: string) => {
    setFilters((prev) => ({
      ...prev,
      csc: value,
    }));
  };

  const onFinish = async (values: FieldType) => {
    try {
      const userData = {
        username: values.username,
        email: values.email,
        phone: values.phone,
        full_name: values.full_name,
        region: values.region,
        csc: values.csc,
        title: values.title,
        address: values.address,
        city: values.city,
        about: values.about,
      };

      await userService.adminUpdateUserProfile(user.id, userData);
      toast.success("User updated successfully");
      onUpdate();
    } catch (error) {
      toast.error("Failed to update user");
      console.error("Update failed:", error);
    }
  };

  return (
    <Row gutter={[16, 16]}>
      <Col span={24} lg={8}>
        <Card className="flex-col !px-6 !pb-10 !pt-20">
          <UploadAvatar defaultAvatar={user.avatar} />
        </Card>
      </Col>
      <Col span={24} lg={16}>
        <Card>
          <Form
            form={form}
            layout="vertical"
            initialValues={initFormValues}
            labelCol={{ span: 8 }}
            className="w-full"
            onFinish={onFinish}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item<FieldType>
                  label="Username"
                  name="username"
                  rules={[{ required: true, message: "Please input username!" }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item<FieldType>
                  label="Email"
                  name="email"
                  rules={[
                    { required: true, message: "Please input email!" },
                    { type: "email", message: "Please enter a valid email!" },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item<FieldType>
                  label="Full Name"
                  name="full_name"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item<FieldType>
                  label="Title"
                  name="title"
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item label="Region" name="region">
                  <Select
                    value={filters.region}
                    onChange={handleRegionChange}
                    placeholder="Select Region"
                    allowClear
                    showSearch
                    optionFilterProp="children"
                  >
                    {regions.map((region) => (
                      <Select.Option key={region.csc_code} value={region.csc_code}>
                        {region.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item label="CSC" name="csc">
                  <Select
                    value={filters.csc}
                    onChange={onCSCChange}
                    placeholder="Select CSC"
                    allowClear
                    showSearch
                    optionFilterProp="children"
                    disabled={!filters.region}
                  >
                    {selectedCSCs.map((csc) => (
                      <Select.Option key={csc.csc_code} value={csc.name}>
                        {csc.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item<FieldType>
                  label="Phone Number"
                  name="phone"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item<FieldType>
                  label="City"
                  name="city"
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item<FieldType>
              label="Address"
              name="address"
            >
              <Input />
            </Form.Item>

            <Form.Item<FieldType>
              label="About"
              name="about"
            >
              <Input.TextArea rows={4} />
            </Form.Item>

            <div className="flex w-full justify-end">
              <Button type="primary" htmlType="submit">
                Save Changes
              </Button>
            </div>
          </Form>
        </Card>
      </Col>
    </Row>
  );
}

