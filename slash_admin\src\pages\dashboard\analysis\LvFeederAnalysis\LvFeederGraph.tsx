import React from 'react';
import { Bar } from '@ant-design/plots';

interface LvFeederGraphProps {
  tabKey: string;
  data: any[];
}

const LvFeederGraph: React.FC<LvFeederGraphProps> = ({ tabKey, data }) => {
  // Transform data based on tabKey
  const getGraphData = () => {
    // Implement data transformation logic based on tabKey
    // Return data in format required by Bar chart
  };

  const config = {
    data: getGraphData(),
    // Configure other Bar chart properties
  };

  return <Bar {...config} />;
};

export default LvFeederGraph;