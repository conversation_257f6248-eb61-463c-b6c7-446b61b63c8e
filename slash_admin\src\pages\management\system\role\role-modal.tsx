import { Form, Input, InputNumber, Modal, Radio, Tree } from "antd";
import { useEffect, useState } from "react";

import { PERMISSION_LIST } from "@/_mock/assets";
import { flattenTrees } from "@/utils/tree";

import type { Permission, Role } from "#/entity";
import { BasicStatus } from "#/enum";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import roleService from "@/api/services/roleService";

export type RoleModalProps = {
	formValue: Role;
	title: string;
	show: boolean;
	onOk: VoidFunction;
	onCancel: VoidFunction;
	permission: any;
};
// const PERMISSIONS: Permission[] = PERMISSION_LIST as Permission[];
export function RoleModal({ title, show, formValue, onOk, onCancel, permission }: RoleModalProps) {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [checkedKeys, setCheckedKeys] = useState([]);

	console.log("checkedKeys", checkedKeys);

	useEffect(() => {
		const flattenedPermissions = flattenTrees(formValue.permission || []);
		setCheckedKeys(flattenedPermissions);
		// setCheckedKeys(flattenedPermissions.map((item) => item.id));
		form.setFieldsValue({ ...formValue, permission: checkedKeys });
	}, [formValue]);

	// const flattenedPermissions = flattenTrees(formValue.permission);
	// const checkedKeys = flattenedPermissions.map((item) => item.id);

	console.log("formValueRole", formValue);
	// useEffect(() => {
	// 	form.setFieldsValue({ ...formValue });
	// }, [formValue, form]);

	// 	useEffect(() => {
	// 		// Initialize the form with the provided
	// 		console.log("formValue UseEffect", formValue)
	// 		form.setFieldsValue({ ...formValue, permission: checkedKeys });
	// }, [formValue, form]);

	const handleSubmit = () => {
		form
			.validateFields() // Validate all fields
			.then(async (values: any) => {
				console.log("values", values);
				setLoading(true);

				if (title === "Create New") {
					await permissionMutation.mutateAsync(values);
					toast.success("Permission created successfully!");
				} else if (title === "Edit") {
					// Extract the updated form values
					const updatedValues = form.getFieldsValue(); // Get the latest form values

					// Call the updateBasestation function with the updated values
					await roleService.updateRole(formValue.id, updatedValues as Partial<any>);
					toast.success("Base Station updated successfully");
				}

				onOk(); // Call the parent's onOk handler
				setLoading(false); // Reset loading state
			})
			.catch((errorInfo) => {
				console.log("Validation failed:", errorInfo);
				// Handle validation errors if needed
			});
	};

	const permissionMutation = useMutation({
		mutationFn: roleService.createRole,
	});

	const onCheck = (checkedKeysValue) => {
		console.log("onCheck", checkedKeysValue);

		// Update the form's value for the 'permission' field
		form.setFieldsValue({ permission: checkedKeysValue });

		// Update the local state for `checkedKeys`
		setCheckedKeys(checkedKeysValue);
	};

	return (
		<Modal title={title} open={show} onOk={handleSubmit} onCancel={onCancel}>
			<Form initialValues={formValue} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }} layout="horizontal">
				<Form.Item<Role> label="Name" name="name" required>
					<Input />
				</Form.Item>

				<Form.Item<Role> label="Label" name="label" required>
					<Input />
				</Form.Item>

				<Form.Item<Role> label="Order" name="order">
					<InputNumber style={{ width: "100%" }} />
				</Form.Item>

				<Form.Item<Role> label="Status" name="status" required>
					<Radio.Group optionType="button" buttonStyle="solid">
						<Radio value={BasicStatus.ENABLE}> Enable </Radio>
						<Radio value={BasicStatus.DISABLE}> Disable </Radio>
					</Radio.Group>
				</Form.Item>

				<Form.Item<Role> label="Desc" name="desc">
					<Input.TextArea />
				</Form.Item>

				<Form.Item<Role> label="Permission" name="permission">
					<Tree
						checkable
						checkedKeys={checkedKeys}
						onCheck={onCheck}
						treeData={permission}
						fieldNames={{
							key: "id",
							children: "children",
							title: "name",
						}}
					/>
				</Form.Item>
			</Form>
		</Modal>
	);
}
