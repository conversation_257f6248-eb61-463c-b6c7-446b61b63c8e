import { Button, Form, Input } from "antd";
import { useTranslation } from "react-i18next";

import { toast } from "sonner";
import { SvgIcon } from "@/components/icon";

import { ReturnButton } from "./components/ReturnButton";
import { LoginStateEnum, useLoginStateContext } from "./providers/LoginStateProvider";
import userService from "@/api/services/userService";

function ResetForm() {
	const onFinish = async (values: any) => {
		try {
			const response = await userService.resetPassword(values.email);
			toast.success("Password reset email sent successfully. Please check your inbox.");
			backToLogin(); // Return to login page after successful submission
		} catch (error) {
			console.error("Password reset error:", error);
			toast.error(error?.response?.data?.message || "Failed to send reset email. Please try again.");
		}
	};

	const { t } = useTranslation();
	const { loginState, backToLogin } = useLoginStateContext();

	if (loginState !== LoginStateEnum.RESET_PASSWORD) return null;

	return (
		<>
			<div className="mb-8 text-center">
				<SvgIcon icon="ic-reset-password" size="100" />
			</div>
			<div className="mb-4 text-center text-2xl font-bold xl:text-3xl">{t("sys.login.forgetFormTitle")}</div>
			<Form name="normal_login" size="large" initialValues={{ remember: true }} onFinish={onFinish}>
				<p className="mb-4 h-12 text-center text-gray">{t("sys.login.forgetFormSecondTitle")}</p>
				<Form.Item name="email" rules={[{ required: true, message: t("sys.login.emaildPlaceholder") }]}>
					<Input placeholder={t("sys.login.email")} />
				</Form.Item>
				<Form.Item>
					<Button type="primary" htmlType="submit" className="w-full !bg-black">
						{t("sys.login.sendEmailButton")}
					</Button>
				</Form.Item>

				<ReturnButton onClick={backToLogin} />
			</Form>
		</>
	);
}

export default ResetForm;
