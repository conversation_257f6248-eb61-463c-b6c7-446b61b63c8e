from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.authtoken.views import obtain_auth_token
from . import api_views

router = DefaultRouter()
router.register(r'messages', api_views.SMSMessageViewSet, basename='message')
router.register(r'users', api_views.UserViewSet, basename='user')
router.register(r'replies', api_views.ReplyViewSet, basename='reply')
router.register(r'notifications', api_views.NotificationViewSet, basename='notification')
router.register(r'approval-requests', api_views.ApprovalRequestViewSet, basename='approval-request')
router.register(r'progress-reports', api_views.ProgressReportViewSet, basename='progress-report')
router.register(r'detailed-reports', api_views.DetailedReportViewSet, basename='detailed-report')
router.register(r'templates', api_views.MessageTemplateViewSet, basename='template')
router.register(r'departments', api_views.DepartmentViewSet, basename='department')

urlpatterns = [
    # Include router URLs first
    path('', include(router.urls)),

    # Authentication endpoints
    path('auth/login/', obtain_auth_token, name='api_token_auth'),
    path('auth/logout/', api_views.LogoutAPIView.as_view(), name='api_logout'),
    path('users/me/', api_views.CurrentUserAPIView.as_view(), name='current_user'),

    # Message specific endpoints
    path('messages/<uuid:pk>/assign/', api_views.AssignMessageAPIView.as_view(), name='assign_message'),
    path('messages/<uuid:pk>/update_status/', api_views.UpdateMessageStatusAPIView.as_view(), name='update_message_status'),
    path('messages/<uuid:pk>/archive/', api_views.ArchiveMessageAPIView.as_view(), name='archive_message'),
    # path('messages/<uuid:pk>/add_reply/', api_views.AddReplyAPIView.as_view(), name='add_reply'),
    path('bulk-assign/', api_views.BulkAssignAPIView.as_view(), name='bulk_assign'),

    # Notification endpoints
    path('notifications/<uuid:pk>/mark_read/', api_views.MarkNotificationReadAPIView.as_view(), name='mark_notification_read'),
    path('notifications/mark_all_read/', api_views.MarkAllNotificationsReadAPIView.as_view(), name='mark_all_notifications_read'),

    # Approval endpoints
    path('approval-requests/<uuid:pk>/approve/', api_views.ApproveRequestAPIView.as_view(), name='approve_request'),
    path('approval-requests/<uuid:pk>/reject/', api_views.RejectRequestAPIView.as_view(), name='reject_request'),

    # Dashboard endpoints
    path('dashboard/stats/', api_views.DashboardStatsAPIView.as_view(), name='dashboard_stats'),
    path('analytics/', api_views.AnalyticsAPIView.as_view(), name='analytics'),

    # WebSocket test endpoint
    path('websocket/test/', api_views.websocket_test_view, name='websocket-test'),
]