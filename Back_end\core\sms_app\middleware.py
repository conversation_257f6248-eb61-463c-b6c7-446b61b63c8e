import logging
from urllib.parse import parse_qs
from django.contrib.auth.models import AnonymousUser
from django.contrib.auth import get_user_model
from django.db import close_old_connections
from channels.middleware import BaseMiddleware
from channels.auth import AuthMiddlewareStack
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from jwt import decode as jwt_decode
from django.conf import settings

User = get_user_model()
logger = logging.getLogger(__name__)


class JWTAuthMiddleware(BaseMiddleware):
    """
    Custom middleware to authenticate WebSocket connections using JWT tokens
    """

    def __init__(self, inner):
        super().__init__(inner)

    async def __call__(self, scope, receive, send):
        # Close old database connections to prevent usage of timed out connections
        close_old_connections()

        # Get the token from query parameters or headers
        token = None
        
        # Try to get token from query parameters first
        query_string = scope.get('query_string', b'').decode()
        if query_string:
            query_params = parse_qs(query_string)
            token = query_params.get('token', [None])[0]
        
        # If no token in query params, try headers
        if not token:
            headers = dict(scope.get('headers', []))
            auth_header = headers.get(b'authorization', b'').decode()
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        # Set default user as anonymous
        scope['user'] = AnonymousUser()

        if token:
            try:
                # Validate the token
                UntypedToken(token)
                
                # Decode the token to get user information
                decoded_data = jwt_decode(
                    token, 
                    settings.SECRET_KEY, 
                    algorithms=["HS256"]
                )
                
                # Get user from token
                user_id = decoded_data.get('user_id')
                if user_id:
                    try:
                        user = await self.get_user(user_id)
                        if user:
                            scope['user'] = user
                            logger.info(f"WebSocket authenticated user: {user.username}")
                        else:
                            logger.warning(f"User with ID {user_id} not found")
                    except Exception as e:
                        logger.error(f"Error getting user: {e}")
                        
            except (InvalidToken, TokenError, Exception) as e:
                logger.warning(f"WebSocket authentication failed: {e}")
                # Continue with anonymous user
                pass

        return await super().__call__(scope, receive, send)

    async def get_user(self, user_id):
        """Get user from database asynchronously"""
        try:
            from channels.db import database_sync_to_async
            get_user_sync = database_sync_to_async(User.objects.get)
            return await get_user_sync(pk=user_id)
        except User.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Error fetching user: {e}")
            return None


def JWTAuthMiddlewareStack(inner):
    """
    Custom middleware stack that includes JWT authentication
    """
    return JWTAuthMiddleware(AuthMiddlewareStack(inner))


class WebSocketAuthMiddleware:
    """
    Minimal WebSocket middleware that just sets anonymous user
    """

    def __init__(self, inner):
        self.inner = inner

    async def __call__(self, scope, receive, send):
        logger.info(f"🔧 WebSocketAuthMiddleware called for {scope.get('type')} connection")
        logger.info(f"   Path: {scope.get('path')}")

        # For WebSocket connections, just set anonymous user
        if scope['type'] == 'websocket':
            scope['user'] = AnonymousUser()
            logger.info("✅ WebSocket connection with anonymous user set")

        return await self.inner(scope, receive, send)


def WebSocketAuthMiddlewareStack(inner):
    """
    Simplified middleware stack for WebSocket authentication
    """
    return WebSocketAuthMiddleware(inner)
