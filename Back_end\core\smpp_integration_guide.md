# SMPP Integration Guide for SMS App

## ✅ Complete Python SMPP Implementation

The Node.js SMPP code has been successfully converted to Python and integrated with the Django SMS app.

## 🔧 Configuration

### SMPP Settings (in `core/settings.py`)
```python
SMPP_CONFIG = {
    'HOST': '*************',
    'PORT': 5019,
    'SYSTEM_ID': '908',
    'PASSWORD': 'Eth@908e',
    'SYSTEM_TYPE': '',
    'INTERFACE_VERSION': 0x34,
    'ADDR_TON': 1,
    'ADDR_NPI': 1,
    'ADDRESS_RANGE': '908',
    'AUTO_ENQUIRE_LINK_PERIOD': 30,
    'SOCKET_TIMEOUT': 30,
}

# Auto-start SMPP client when Django starts
AUTO_START_SMPP = True
```

## 🚀 Starting the SMPP Client

### Method 1: Automatic Startup (Recommended)
The SMPP client starts automatically when Django starts if `AUTO_START_SMPP = True`.

### Method 2: Manual Management Command
```bash
# Start SMPP client
python manage.py start_smpp

# Start in daemon mode (keeps running)
python manage.py start_smpp --daemon

# Check status
python manage.py start_smpp --status

# Stop client
python manage.py start_smpp --stop
```

### Method 3: Programmatic Control
```python
from sms_app.sms_handler import start_smpp_client, stop_smpp_client, get_smpp_status

# Start client
start_smpp_client()

# Check status
status = get_smpp_status()

# Stop client
stop_smpp_client()
```

## 📡 SMPP Features Implemented

### ✅ Connection Management
- **Bind as Transceiver**: Full send/receive capability
- **Auto-reconnection**: Handles connection drops
- **Heartbeat**: Automatic enquire_link for connection health
- **Graceful shutdown**: Proper unbind and disconnect

### ✅ Message Sending
- **SMS Delivery**: Send SMS to any number
- **Long Message Support**: Automatic message splitting
- **Delivery Reports**: Track message delivery status
- **Error Handling**: Comprehensive error management

### ✅ Message Receiving
- **Real-time Reception**: Instant SMS reception
- **Message Decoding**: UTF-8 and UCS2 support
- **Auto-reply**: Automatic acknowledgment messages
- **Database Integration**: Save messages to Django models

### ✅ Integration Features
- **Django Models**: Full integration with SMS models
- **Celery Tasks**: Async message processing
- **WebSocket Broadcasting**: Real-time updates
- **Admin Interface**: Manage messages via Django admin

## 🧪 Testing

### Send SMS Test
```bash
python test_sms_send.py
```
Options:
1. Direct SMPP Send Test
2. Integrated Handler Send Test
3. Multiple SMS Send Test
4. Run All Tests

### Receive SMS Test
```bash
python test_sms_receive.py
```
Options:
1. Direct SMPP Receive Test
2. Integrated Handler Receive Test
3. Message Processing Test
4. Show Recent Messages
5. SMPP Status

## 📱 Usage Examples

### Sending SMS
```python
from sms_app.sms_handler import send_sms_via_smpp

# Send SMS
success = send_sms_via_smpp("251912345678", "Hello from Python SMPP!")
```

### Receiving SMS
SMS messages are automatically received and processed when the SMPP client is running:
1. **Received**: SMS arrives at SMPP server
2. **Decoded**: Message content extracted and decoded
3. **Saved**: Message saved to database via Django models
4. **Processed**: Auto-categorization and priority assignment
5. **Broadcast**: Real-time WebSocket notification
6. **Auto-reply**: Automatic acknowledgment sent

## 🔄 Message Flow

### Incoming SMS Flow
```
📱 Customer Phone
    ↓
📡 SMSC (*************:5019)
    ↓
🐍 Python SMPP Client
    ↓
💾 Django Database (SMSMessage model)
    ↓
🔄 Celery Task Processing
    ↓
🌐 WebSocket Broadcast
    ↓
📧 Auto-reply SMS
```

### Outgoing SMS Flow
```
🖥️ Django Admin/API
    ↓
🐍 Python SMPP Client
    ↓
📡 SMSC (*************:5019)
    ↓
📱 Customer Phone
    ↓
📬 Delivery Receipt
    ↓
💾 Status Update in Database
```

## 🛠️ Node.js vs Python Comparison

| Feature | Node.js | Python | Status |
|---------|---------|---------|---------|
| SMPP Connection | ✅ | ✅ | **Converted** |
| Bind Transceiver | ✅ | ✅ | **Converted** |
| Send SMS | ✅ | ✅ | **Converted** |
| Receive SMS | ✅ | ✅ | **Converted** |
| Message Decoding | ✅ | ✅ | **Enhanced** |
| Auto-reply | ✅ | ✅ | **Converted** |
| Error Handling | ✅ | ✅ | **Enhanced** |
| Logging | ✅ | ✅ | **Enhanced** |
| Database Integration | ❌ | ✅ | **Added** |
| WebSocket Broadcasting | ❌ | ✅ | **Added** |
| Django Integration | ❌ | ✅ | **Added** |

## 🔧 Troubleshooting

### Connection Issues
```bash
# Check SMPP status
python manage.py start_smpp --status

# Test connection manually
python test_sms_receive.py
# Choose option 5 for status check
```

### Message Not Received
1. Check SMPP client is running
2. Verify network connectivity to SMSC
3. Check system_id and password
4. Verify address_range configuration

### Message Not Sent
1. Check SMPP connection status
2. Verify destination number format
3. Check message encoding
4. Review SMPP logs

## 📊 Monitoring

### SMPP Client Status
```python
from sms_app.sms_handler import get_smpp_status

status = get_smpp_status()
print(f"Running: {status['running']}")
print(f"Connected: {status['connected']}")
print(f"Thread Alive: {status['thread_alive']}")
```

### Message Statistics
```python
from sms_app.models import SMSMessage

# Total messages
total = SMSMessage.objects.count()

# Recent messages
recent = SMSMessage.objects.filter(
    created_at__gte=timezone.now() - timedelta(hours=24)
).count()

# By status
pending = SMSMessage.objects.filter(status='pending').count()
```

## 🎯 Production Deployment

### Requirements
1. **Python Dependencies**: `smpplib` installed
2. **Network Access**: Connection to SMSC server
3. **Process Management**: Use supervisor or systemd
4. **Monitoring**: Log monitoring and alerting

### Supervisor Configuration
```ini
[program:smpp_client]
command=python manage.py start_smpp --daemon
directory=/path/to/sms_app
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/smpp_client.log
```

## ✅ Status Summary

- ✅ **Node.js Code Converted**: Complete Python implementation
- ✅ **SMPP Client**: Working with full send/receive capability
- ✅ **Auto-start**: Starts automatically with Django
- ✅ **Database Integration**: Messages saved to Django models
- ✅ **WebSocket Integration**: Real-time message broadcasting
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Testing Tools**: Separate send/receive test scripts
- ✅ **Management Commands**: Easy start/stop/status commands
- ✅ **Production Ready**: Suitable for production deployment

The SMS app now has perfect SMPP integration with Python! 🎉
