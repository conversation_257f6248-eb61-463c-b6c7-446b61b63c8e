import { useState } from "react";
import { Space, Table, <PERSON><PERSON>, Card, Ta<PERSON>, Popconfirm } from "antd";
import type { ColumnsType } from "antd/es/table";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Organization, Substation, Feeder } from "@/types/entity";
import orgService from "@/api/services/orgService";
import { OrganizationModal } from "./organization-modal";
import { SubstationModal } from "./substation-modal";
import { FeederModal } from "./feeder-modal";
import { toast } from "sonner";
import { IconButton, Iconify } from "@/components/icon";

export default function OrganizationPage() {
	const queryClient = useQueryClient();
	const [organizationModalProps, setOrganizationModalProps] = useState<OrganizationModalProps>({
		formValue: { csc_code: "", name: "" },
		title: "New",
		show: false,
		onOk: () => setOrganizationModalProps((prev) => ({ ...prev, show: false })),
		onCancel: () => setOrganizationModalProps((prev) => ({ ...prev, show: false })),
	});

	const [substationModalProps, setSubstationModalProps] = useState<SubstationModalProps>({
		formValue: { name: "" },
		title: "Create Substation",
		show: false,
		onOk: () => {
			setSubstationModalProps((prev) => ({ ...prev, show: false }));
			queryClient.invalidateQueries({ queryKey: ['orgs'] });
		},
		onCancel: () => setSubstationModalProps((prev) => ({ ...prev, show: false })),
	});

	const [feederModalProps, setFeederModalProps] = useState({
		show: false,
		feeder: null as Feeder | null,
		selectedSubstation: 0, // Initialize with a number value
		onOk: () => {
			queryClient.invalidateQueries({ queryKey: ['orgs'] });
			setFeederModalProps(prev => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setFeederModalProps(prev => ({ ...prev, show: false }));
		}
	});

	const [expandedRegionKeys, setExpandedRegionKeys] = useState<React.Key[]>([]);
	const [expandedSubstationKeys, setExpandedSubstationKeys] = useState<React.Key[]>([]);
	const [expandedFeederKeys, setExpandedFeederKeys] = useState<React.Key[]>([]);

	const { data } = useQuery({
		queryKey: ["orgs"],
		queryFn: orgService.getOrgList,
	});

	const handleAddFeeder = (substation: Substation) => {
		console.log("Adding feeder to substation:", substation);
		setFeederModalProps({
			...feederModalProps,
			show: true,
			feeder: null,
			selectedSubstation: Number(substation.id)
		});
	};

	const handleDelete = async (csc_code: string, type: string) => {
		try {
			if (type === "region") {
				await orgService.deleteRegion(csc_code);
				toast.success("Region deleted successfully!");
			} else if (type === "csc") {
				await orgService.deleteCSC(csc_code);
				toast.success("CSC deleted successfully!");
			} else if (type === "substation") {
				await orgService.deleteSubstation(csc_code);
				toast.success("Substation deleted successfully!");
			}
			queryClient.invalidateQueries({ queryKey: ['orgs'] });
		} catch (error) {
			toast.error(`Failed to delete ${type}`);
		}
	};

	// Organization View Columns
	const organizationColumns: ColumnsType<Organization> = [
		{ title: "Name", dataIndex: "name", width: 300 },
		{
			title: "Action",
			key: "operation",
			width: 100,
			// render: (_, record) => (
			// 	<Space>
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete the Organization"
			// 			onConfirm={() => handleDelete(record.csc_code, record.type || "")}
			// 			okText="Yes"
			// 			cancelText="No"
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</Space>
			// ),
			render: (_, record) => (
							<Space>
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
									<Popconfirm
										title="Delete the Organization?"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDelete(record.csc_code, record.type || "")}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</Space>
						),
		},
	];

	const cscColumns: ColumnsType<any> = [
		{ title: "CSC Name", dataIndex: "name" },
		{
			title: "Action",
			key: "operation",
			// render: (_, record) => (
			// 	<Space>
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete this CSC?"
			// 			onConfirm={() => handleDelete(record.csc_code, "csc")}
			// 			okText="Yes"
			// 			cancelText="No"
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</Space>
			// ),
			render: (_, record) => (
							<Space>
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
									<Popconfirm
										title="Delete this CSC?"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDelete(record.csc_code, "csc")}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</Space>
						),
		},
	];

	// Substation View Columns
	const substationColumns: ColumnsType<Substation> = [
		{ title: "Name", dataIndex: "name" },
		{
			title: "Action",
			key: "operation",
			// render: (_, record) => (
			// 	<Space>
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<IconButton 
			// 			onClick={() => handleAddFeeder(record)}
			// 			className="bg-secondary-lighter"
			// 		>
			// 			<Iconify icon="material-symbols:add" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete this Substation?"
			// 			onConfirm={() => handleDelete(record.id, "substation")}
			// 			okText="Yes"
			// 			cancelText="No"
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</Space>
			// ),
			render: (_, record) => (
							<Space>
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
								<IconButton 
					      	onClick={() => handleAddFeeder(record)}
					      	className="bg-secondary-lighter"
					      >
					      	<Iconify icon="ix:add" size={18} />
					      </IconButton>
									<Popconfirm
										title="Delete this Substation?"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDelete(record.id, "substation")}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</Space>
						),
		},
	];

	const feederColumns: ColumnsType<Feeder> = [
		{ title: "Name", dataIndex: "feeder_name" },
		{ title: "Voltage Level", dataIndex: "voltage_level" },
		{ title: "Peak Load", dataIndex: "peak_load" },
		{ title: "Length", dataIndex: "length" },
		{ title: "Transformers", dataIndex: "number_of_transformer" },
		{
			title: "Action",
			key: "operation",
			width: 100,
			// render: (_, record) => (
			// 	<Space>
			// 		<IconButton onClick={() => onEditFeeder(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete this Feeder?"
			// 			onConfirm={() => handleDeleteFeeder(record.id)}
			// 			okText="Yes"
			// 			cancelText="No"
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</Space>
			// ),
			render: (_, record) => (
							<Space>
								<IconButton onClick={() => onEditFeeder(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
									<Popconfirm
										title="Delete this Feeder?"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDeleteFeeder(record.id)}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</Space>
						),
		},
	];

	const onCreate = (type: string) => {
		if (type === "Region") {
			setOrganizationModalProps({
				...organizationModalProps,
				title: "Create Region",
				show: true,
				formValue: { csc_code: "", name: "" },
			});
		} else if (type === "CSC") {
			setOrganizationModalProps({
				...organizationModalProps,
				title: "Create CSC",
				show: true,
				formValue: { csc_code: "", name: "" },
			});
		} else if (type === "Substation") {
			setSubstationModalProps({
				...substationModalProps,
				title: "Create Substation",
				show: true,
				formValue: { name: "" },
			});
		}
	};

	const onEdit = (formValue: Organization | Substation) => {
		console.log("Editing:", formValue);
		
		// Check if it's a substation
		if ('id' in formValue) {  // Substations have 'id', Organizations have 'csc_code'
			setSubstationModalProps({
				show: true,
				title: "Edit Substation",
				formValue: {
					id: formValue.id,  // Include the id
					name: formValue.name,
					region: formValue.region
				},
				onOk: () => {
					setSubstationModalProps((prev) => ({ ...prev, show: false }));
					queryClient.invalidateQueries({ queryKey: ['orgs'] });
				},
				onCancel: () => setSubstationModalProps((prev) => ({ ...prev, show: false })),
			});
		} else {
			// Handle Organization edit
			setOrganizationModalProps({
				show: true,
				title: `Edit ${formValue.type}`,
				formValue,
				onOk: () => setOrganizationModalProps((prev) => ({ ...prev, show: false })),
				onCancel: () => setOrganizationModalProps((prev) => ({ ...prev, show: false })),
			});
		}
	};

	const onEditFeeder = (feeder: Feeder) => {
		setFeederModalProps({
			...feederModalProps,
			show: true,
			feeder: feeder,
			selectedSubstation: feeder.substation_id
		});
	};

	const handleDeleteFeeder = async (feederId: number) => {
		try {
			await orgService.deleteFeeder(feederId);
			toast.success("Feeder deleted successfully");
			queryClient.invalidateQueries({ queryKey: ['orgs'] });
		} catch (error: any) {
			toast.error(error.message || "Failed to delete feeder");
		}
	};

	function OrganizationView() {
		return (
			<Card
				title="Organization Structure"
				extra={
					<Space>
						<Button type="primary" onClick={() => onCreate("Region")}>
							Create Region
						</Button>
						<Button type="primary" onClick={() => onCreate("CSC")}>
							Create CSC
						</Button>
					</Space>
				}
			>
				<Table
					rowKey="csc_code"
					size="small"
					scroll={{ x: "max-content" }}
					pagination={false}
					columns={organizationColumns}
					dataSource={data}
					expandable={{
						expandedRowKeys: expandedRegionKeys,
						onExpand: (expanded, record) => {
							setExpandedRegionKeys(expanded ? [record.csc_code] : []);
						},
						expandedRowRender: (record) => (
							<Table
								rowKey="csc_code"
								columns={cscColumns}
								dataSource={record.csc_centers}
								pagination={false}
								size="small"
							/>
						),
					}}
				/>
			</Card>
		);
	}

	// Region columns for Substation view
	const regionSubstationColumns: ColumnsType<Organization> = [
		{ title: "Region Name", dataIndex: "name", width: 300 },
		{
			title: "Action",
			key: "operation",
			width: 100,
			// render: (_, record) => (
			// 	<Space>
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 	</Space>
			// ),
			render: (_, record) => (
							<Space>
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
							</Space>
						),
		},
	];

	function SubstationView() {
		return (
			<Card
				title="Substation Structure"
				extra={
					<Button type="primary" onClick={() => onCreate("Substation")}>
						Create Substation
					</Button>
				}
			>
				<Table
					rowKey="csc_code"
					size="small"
					scroll={{ x: "max-content" }}
					pagination={false}
					columns={regionSubstationColumns}
					dataSource={data}
					expandable={{
						expandedRowKeys: expandedRegionKeys,
						onExpand: (expanded, record) => {
							setExpandedRegionKeys(expanded ? [record.csc_code] : []);
						},
						expandedRowRender: (record) => (
							<Table
								rowKey="id"
								columns={substationColumns}
								dataSource={record.substations}
								pagination={false}
								size="small"
								expandable={{
									expandedRowKeys: expandedSubstationKeys,
									onExpand: (expanded, record) => {
										setExpandedSubstationKeys(expanded ? [record.id] : []);
									},
									expandedRowRender: (substationRecord) => (
										<Table
											rowKey="id"
											columns={feederColumns}
											dataSource={substationRecord.feeders}
											pagination={false}
											size="small"
										/>
									),
								}}
							/>
						),
					}}
				/>
			</Card>
		);
	}

	return (
		<Space direction="vertical" size="large" className="w-full">
			<Tabs
				items={[
					{
						key: "1",
						label: "Organization",
						children: <OrganizationView />,
					},
					{
						key: "2",
						label: "Substation",
						children: <SubstationView />,
					},
				]}
			/>
			<OrganizationModal {...organizationModalProps} />
			<SubstationModal {...substationModalProps} />
			<FeederModal {...feederModalProps} />
		</Space>
	);
}












