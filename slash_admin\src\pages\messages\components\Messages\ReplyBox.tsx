import React, { useState } from 'react';
import { PaperAirplaneIcon } from '@heroicons/react/24/solid';
import { MessageTemplate } from '../../../../types';
import { useTemplates } from '../../../../hooks/useTemplates';

interface ReplyBoxProps {
  onSend: (content: string) => void;
  category: string;
}

export default function ReplyBox({ onSend, category }: ReplyBoxProps) {
  const [message, setMessage] = useState('');
  const [showTemplates, setShowTemplates] = useState(false);
  const { templates } = useTemplates();

  const categoryTemplates = templates.filter(t => 
    t.isActive && (t.category === category || t.category === 'general')
  );

  const handleSend = () => {
    if (message.trim()) {
      onSend(message.trim());
      setMessage('');
    }
  };

  const handleTemplateSelect = (template: MessageTemplate) => {
    // Replace variables with placeholders for user to fill
    let content = template.content;
    template.variables.forEach(variable => {
      content = content.replace(new RegExp(`{{${variable}}}`, 'g'), `[${variable.toUpperCase()}]`);
    });
    setMessage(content);
    setShowTemplates(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="p-4">
      {/* Template Suggestions */}
      {showTemplates && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium text-gray-700 mb-2">Quick Templates:</div>
          <div className="space-y-2">
            {categoryTemplates.map((template) => (
              <button
                key={template.id}
                onClick={() => handleTemplateSelect(template)}
                className="w-full text-left p-2 text-sm text-gray-600 hover:bg-white hover:shadow-sm rounded border transition-all"
              >
                <div className="font-medium">{template.name}</div>
                <div className="text-xs text-gray-500 truncate">{template.content}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="flex gap-3">
        <div className="flex-1">
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your reply..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-sky-500 focus:border-sky-500 resize-none"
          />
        </div>
        <div className="flex flex-col gap-2">
          <button
            onClick={() => setShowTemplates(!showTemplates)}
            className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Templates
          </button>
          <button
            onClick={handleSend}
            disabled={!message.trim()}
            className="px-4 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
            Send
          </button>
        </div>
      </div>
    </div>
  );
}