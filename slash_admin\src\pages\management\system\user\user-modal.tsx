import { Form, Input, Modal, Select } from "antd";
import { useEffect, useState } from "react";
import type { User } from "#/entity"; // Import the User type
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import roleService from "@/api/services/roleService";

export type UserModalProps = {
	formValue: User; // Type representing the User entity
	title: string; // Title of the modal (e.g., "Create New User" or "Edit User")
	show: boolean; // Whether the modal is visible
	onOk: VoidFunction; // Callback when OK is clicked
	onCancel: VoidFunction; // Callback when Cancel is clicked
};

// Define the UserModal component
export function UserModal({ title, show, formValue, onOk, onCancel }: UserModalProps) {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	const [roles, setRoles] = useState<{ key: string; name: string }[]>([]); // List of roles
	const [loadingRoles, setLoadingRoles] = useState(true); // Loading state for roles

	// Fetch roles when the component mounts
	useEffect(() => {
		const loadRoles = async () => {
			try {
				const data = await roleService.getRole(); // Replace with your actual API call
				setRoles(data.map((role: any) => ({ key: role.id, name: role.name })));
			} catch (error) {
				console.error("Error fetching roles:", error);
			} finally {
				setLoadingRoles(false);
			}
		};
		loadRoles();
	}, []);

	// Update form values when formValue changes
	useEffect(() => {
		form.setFieldsValue({ ...formValue });
	}, [formValue, form]);

	// Handle OK button click
	const handleOk = () => {
		form
			.validateFields() // Validate all fields
			.then(async (values: any) => {
				setLoading(true);
				if (title === "Create User") {
					const data = {
						...values,
						role_id: values.role,
					};
					console.log("Create User  after", data);
					await userMutation.mutateAsync(data);
					toast.success("User created successfully!");
				} else if (title === "Edit") {
					// Extract the updated form values
					const updatedValues = form.getFieldsValue(); // Get the latest form values
					// Call the update function with the updated values
					console.log("Edit User", updatedValues);
					const data = {
						...updatedValues,
						role_id: updatedValues.role.id ? updatedValues.role.id : updatedValues.role,
					};
					console.log("Edit User  after", data);
					await roleService.updateUser(formValue.id, data as Partial<User>);
					toast.success("User updated successfully");
				}
				onOk(); // Call the parent's onOk handler
				setLoading(false); // Reset loading state
			})
			.catch((errorInfo) => {
				console.log("Validation failed:", errorInfo);
				// Handle validation errors if needed
			});
	};

	// Mutation for creating a new user
	const userMutation = useMutation({
		mutationFn: roleService.createUser,
	});

	return (
		<Modal title={title} open={show} onOk={handleOk} onCancel={onCancel} confirmLoading={loading}>
			<Form initialValues={formValue} form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout="horizontal">
				{/* Username */}
				<Form.Item<User>
					label="Username"
					name="username"
					rules={[{ required: true, message: "Please enter a username!" }]}
				>
					<Input />
				</Form.Item>

				{/* Email */}
				<Form.Item<User>
					label="Email"
					name="email"
					rules={[{ required: true, type: "email", message: "Please enter a valid email!" }]}
				>
					<Input />
				</Form.Item>

				{/* Role */}
				<Form.Item<User> name="role" label="Select Role" rules={[{ required: true, message: "Please select a role!" }]}>
					<Select
						showSearch
						placeholder="Select a role"
						optionFilterProp="children"
						loading={loadingRoles}
						filterOption={(input, option) =>
							String(option?.children ?? "")
								.toLowerCase()
								.includes(input.toLowerCase())
						}
					>
						{roles.map((role) => (
							<Select.Option key={role.key} value={role.key}>
								{role.name}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* Avatar URL */}
				{/* <Form.Item<User> label="Avatar URL" name="avatar" required={false}>
                    <Input />
                </Form.Item> */}

				{/* Is Staff */}
				{/* <Form.Item<User> label="Is Staff" name="is_staff" valuePropName="checked" initialValue={true}>
                    <Input type="checkbox" />
                </Form.Item> */}

				{/* Is Active */}
				<Form.Item<User> label="Status" name="is_active" valuePropName="checked" initialValue={true}>
					<Input type="checkbox" />
				</Form.Item>
			</Form>
		</Modal>
	);
}
