import React, { useCallback, useEffect, useState } from "react";
import L from "leaflet";
import "./BaseStationcluster.css";
import useSupercluster from "use-supercluster";
import { Marker, Popup, useMap } from "react-leaflet";
import { Link } from "react-router";

// Create icons for clusters dynamically
const icons = {};
const fetchIcon = (count, size) => {
	if (!icons[count]) {
		icons[count] = L.divIcon({
			html: `<div class="cluster-marker" style="width: ${size}px; height: ${size}px;">
        ${count}
      </div>`,
		});
	}
	return icons[count];
};

// Custom icon for single crime markers
const cuffs = new L.Icon({
	iconUrl: "/energy.svg",
	iconSize: [25, 25],
});

function Inspectioncluster({ data }) {
	const maxZoom = 22;
	const [bounds, setBounds] = useState(null);
	const [zoom, setZoom] = useState(12);
	const map = useMap();

	// Function to update map bounds and zoom
	function updateMap() {
		const b = map.getBounds();
		setBounds([b.getSouthWest().lng, b.getSouthWest().lat, b.getNorthEast().lng, b.getNorthEast().lat]);
		setZoom(map.getZoom());
	}

	const onMove = useCallback(() => {
		updateMap();
	}, [map]);

	useEffect(() => {
		updateMap();
	}, [map]);

	useEffect(() => {
		map.on("move", onMove);
		return () => {
			map.off("move", onMove);
		};
	}, [map, onMove]);

	// Convert crime data into GeoJSON format
	const points = data.map((crime) => {
		const [longitude, latitude] = crime.gps_location
			?.split(",") // Split by comma
			.map((coord) => parseFloat(coord.trim())) // Parse and trim whitespace
			.reverse(); // Reverse to [latitude, longitude]

		return {
			type: "Feature",
			properties: {
				cluster: false,

				station_code: crime?.transformer_data?.basestation?.station_code,
				transformer_data: crime?.transformer_data.id,
				arrester: crime.arrester,
				drop_out: crime.drop_out,
				// capacity: crime.capacity,
				// colling_type: crime.colling_type,
				// primary_voltage: crime.primary_voltage,
				// trafo_type: crime.trafo_type,
			},
			geometry: {
				type: "Point",
				coordinates: [longitude, latitude], // Store as [longitude, latitude]
			},
		};
	});

	// console.log("Parsed points:", points);

	// Use Supercluster to generate clusters
	const { clusters, supercluster } = useSupercluster({
		points: points,
		bounds: bounds,
		zoom: zoom,
		options: { radius: 75, maxZoom: 17 },
	});

	return (
		<>
			{clusters.map((cluster) => {
				const [longitude, latitude] = cluster.geometry.coordinates;
				const { cluster: isCluster, point_count: pointCount } = cluster.properties;

				// Render a cluster marker
				if (isCluster) {
					return (
						<Marker
							key={`cluster-${cluster.id}`}
							position={[latitude, longitude]} // Leaflet expects [latitude, longitude]
							icon={fetchIcon(pointCount, 10 + (pointCount / points.length) * 40)}
							eventHandlers={{
								click: () => {
									const expansionZoom = Math.min(supercluster.getClusterExpansionZoom(cluster.id), maxZoom);
									map.setView([latitude, longitude], expansionZoom, {
										animate: true,
									});
								},
							}}
						></Marker>
					);
				} else {
					// Render a single crime marker
					return (
						<Marker
							key={`crime-${cluster.properties.station_code}`}
							position={[latitude, longitude]} // Leaflet expects [latitude, longitude]
							icon={cuffs}
						>
							{/* Popup for individual crimes */}
							<Popup>
								<div>
									<h3>Latest Inspection</h3>
									<p>
										<strong>Station Code:</strong>
										<Link to={`/basestation/${cluster.properties.station_code}`}>
											{cluster.properties.station_code}
										</Link>
									</p>
									<p>
										<strong>Transformer id:</strong>
										<Link to={`/transformer/${cluster.properties.transformer_data}`}>
											{cluster.properties.transformer_data}
										</Link>
									</p>
									<p>
										<strong>station_code:</strong> {cluster.properties.station_code}
									</p>
									<p>
										<strong>transformer_data:</strong> {cluster.properties.transformer_data}
									</p>
									<p>
										<strong>arrester:</strong> {cluster.properties.arrester}
									</p>
									<p>
										<strong>drop_out:</strong> {cluster.properties.drop_out}
									</p>
								</div>
							</Popup>
						</Marker>
					);
				}
			})}
		</>
	);
}

export default Inspectioncluster;
