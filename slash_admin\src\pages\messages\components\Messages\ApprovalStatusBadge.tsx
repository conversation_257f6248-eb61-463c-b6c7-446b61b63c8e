import React from 'react';
import { ClockIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { ApprovalRequest } from '../../../../types';

interface ApprovalStatusBadgeProps {
  status: ApprovalRequest['status'];
  size?: 'sm' | 'md' | 'lg';
}

export default function ApprovalStatusBadge({ status, size = 'md' }: ApprovalStatusBadgeProps) {
  const getStatusConfig = (status: ApprovalRequest['status']) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending Approval',
          icon: ClockIcon,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case 'approved':
        return {
          label: 'Approved',
          icon: CheckCircleIcon,
          className: 'bg-green-100 text-green-800 border-green-200'
        };
      case 'rejected':
        return {
          label: 'Rejected',
          icon: XCircleIcon,
          className: 'bg-red-100 text-red-800 border-red-200'
        };
      default:
        return {
          label: 'Unknown',
          icon: ClockIcon,
          className: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <span className={`inline-flex items-center gap-1 rounded-full font-medium border ${config.className} ${sizeClasses[size]}`}>
      <Icon className={iconSizes[size]} />
      {config.label}
    </span>
  );
}