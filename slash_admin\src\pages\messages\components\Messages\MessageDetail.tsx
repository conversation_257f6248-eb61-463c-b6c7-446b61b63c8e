import React, { useEffect, useRef, useState } from 'react';
import { format, isToday } from 'date-fns';
import {
  UserIcon,
  ClockIcon,
  TagIcon,
  PaperAirplaneIcon,
  DocumentArrowDownIcon,
  UserPlusIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import { SMSMessage, User, TimelineEvent, ProgressReport } from '../../../../types';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMessages } from '../../../../hooks/useMessages';
import { useReplies } from '../../../../hooks/useReplies';
import StatusBadge from './StatusBadge';
import CategoryBadge from './CategoryBadge';
import PriorityBadge from './PriorityBadge';
import ReplyBox from './ReplyBox';
import AssignmentModal from './AssignmentModal';
import EnhancedAssignmentModal from './EnhancedAssignmentModal';
import ApprovalModal from './ApprovalModal';
import DetailedReportModal from '../Reports/DetailedReportModal';
import ProgressReportModal from './ProgressReportModal';
import CaseTimeline from './CaseTimeline';
import AttachmentViewer from './AttachmentViewer';
import MessageActions from './MessageActions';
import { mockUsers } from '@/data/mockData';

interface MessageDetailProps {
  message: SMSMessage;
}

export default function MessageDetail({ message }: MessageDetailProps) {
  const { currentUser } = useAuth();
  const {
    assignMessage,
    updateMessageStatus,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF
  } = useMessages();

  // Reply filtering states
  const [replyFilters, setReplyFilters] = useState({
    phoneNumber: '',
    search: '',
    senderType: '' as '' | 'customer' | 'agent'
  });
  const [showReplyFilters, setShowReplyFilters] = useState(false);

  

  // Use the new useReplies hook
  const {
    replies,
    loading: repliesLoading,
    loadingMore,
    error: repliesError,
    hasMore,
    totalCount,
    loadMoreReplies,
    addReply: addReplyToMessage
  } = useReplies({
    messageId: message.id,
    phoneNumberFilter: replyFilters.phoneNumber || undefined,
    searchFilter: replyFilters.search || undefined,
    senderTypeFilter: replyFilters.senderType || undefined
  });

  console.log('replies in MessageDetail:', replies);

  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showEnhancedAssignmentModal, setShowEnhancedAssignmentModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showDetailedReportModal, setShowDetailedReportModal] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'conversation' | 'timeline' | 'attachments'>('conversation');

  // cont function ConversationHistory({ message }) {
  const repliesEndRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  // Scroll to bottom on initial load and when replies change
    // useEffect(() => {
    //   if (replies.length > 0) {
    //     scrollToBottom(true);
    //   }
    // }, [replies.length]);

    useEffect(() => {
      if (replies.length > 0) {
        if (loadingMore) {
          // Preserve position when loading older replies
          preserveScrollPosition();
        } else {
          // Scroll to bottom only when a NEW reply is added
          scrollToBottom(true);
        }
      }
    }, [replies.length]);
  
    // const scrollToBottom = (instant = false) => {
    //   if (scrollContainerRef.current) {
    //     scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    //   }
    //   if (messagesEndRef.current && instant) {
    //     messagesEndRef.current.scrollIntoView({ behavior: 'auto' });
    //   }
    // };\
    const scrollToBottom = (instant = false) => {
  if (scrollContainerRef.current) {
    if (instant) {
      scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
    } else {
      scrollContainerRef.current.scrollTo({
        top: scrollContainerRef.current.scrollHeight,
        behavior: "smooth"
      });
    }
  }
};

const preserveScrollPosition = () => {
  if (!scrollContainerRef.current) return;
  const container = scrollContainerRef.current;
  // Distance from bottom before new replies
  const prevHeight = container.scrollHeight;
  
  requestAnimationFrame(() => {
    const newHeight = container.scrollHeight;
    container.scrollTop = newHeight - prevHeight + container.scrollTop;
  });
};

  function formattedTime(timestamp: string | Date) {
    const date = new Date(timestamp);
    return isToday(date) ? format(date, 'h:mm a') : format(date, 'MMM d');
  }

  // Mock timeline events for demonstration
  const timelineEvents: TimelineEvent[] = [
    {
      id: '1',
      type: 'message',
      timestamp: message.timestamp,
      user: { id: 'customer', name: 'Customer', email: '', role: 'viewer', department: '', isActive: true },
      description: 'Initial message received',
      metadata: message.content
    },
    ...(message.assigned_to ? [{
      id: '2',
      type: 'assignment' as const,
      timestamp: new Date(message.timestamp.getTime() + 5 * 60 * 1000),
      user: currentUser!,
      description: `Assigned to ${message.assigned_to.name}`,
      metadata: `Case assigned to ${message.assigned_to.department}`
    }] : []),
    ...replies.map((reply, index) => ({
      id: `reply-${index}`,
      type: 'reply' as const,
      timestamp: reply.timestamp,
      user: reply.sender,
      description: reply.is_from_customer ? 'Customer replied' : 'Reply sent to customer',
      metadata: reply.content
    }))
  ];

  const handleSendReply = async (content: string) => {
    try {
      await addReplyToMessage(content);

      // Trigger notification
      window.dispatchEvent(new CustomEvent('newNotification', {
        detail: {
          type: 'message',
          title: 'Reply Sent',
          message: `Reply sent to ${message.phone_number}`,
          caseId: message.caseId
        }
      }));

      // Scroll to bottom to show new reply
      scrollToBottom(true);
    } catch (error) {
      console.error('Error sending reply:', error);
      // You might want to show an error notification here
    }
  };

  const handleAssignment = (assignment: {
    userId: string;
    comment: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    dueDate: string;
    estimatedHours?: number;
    skillsRequired?: string[];
  }) => {
    // In a real app, you'd find the user by ID
    const mockUser = {
      id: assignment.userId,
      name: 'Assigned User',
      email: '',
      role: 'technician' as const,
      department: 'Field Operations',
      isActive: true
    };

    assignMessage(message.id, mockUser);
    setShowEnhancedAssignmentModal(false);

    // Trigger notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: {
        type: 'assignment',
        title: 'Case Assigned',
        message: `Case ${message.caseId} has been assigned`,
        caseId: message.caseId
      }
    }));
  };

  const handleProgressReport = (report: {
    content: string;
    attachments: File[];
    checklist: any[];
  }) => {
    // In a real app, this would save the progress report
    console.log('Progress report submitted:', report);
    setShowProgressModal(false);

    // Trigger notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: {
        type: 'report',
        title: 'Progress Report',
        message: `Progress report submitted for ${message.caseId}`,
        caseId: message.caseId
      }
    }));
  };

  const handleStatusChange = (newStatus: SMSMessage['status']) => {
    updateMessageStatus(message.id, newStatus);
  };

  const handleApproval = (decision: 'approved' | 'rejected', comments: string) => {
    // In a real app, this would update the approval status
    console.log('Approval decision:', decision, comments);
    setShowApprovalModal(false);
  };

  const handleDetailedReport = (report: any) => {
    // In a real app, this would save the detailed report
    console.log('Detailed report submitted:', report);
    setShowDetailedReportModal(false);
  };

  // Mock approval request for demo
  const mockApprovalRequest = {
    id: '1',
    messageId: message.id,
    type: 'escalation' as const,
    description: 'Request approval for emergency response escalation',
    priority: 'high' as const,
    requestedBy: currentUser!,
    requestedAt: new Date(),
    approvers: [mockUsers[0]],
    status: 'pending' as const,
    approvalHistory: []
  };

  const canAssign = currentUser?.role === 'admin' || currentUser?.role === 'supervisor';
  const canReply = currentUser?.role !== 'viewer';
  const canReport = currentUser?.role === 'technician' || currentUser?.role === 'supervisor';

  return (
    <div className="flex-1 flex flex-col bg-white h-full">
      {/* Modern Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-white to-gray-50">
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{message.phone_number}</h2>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-500">Case #{message.caseId}</span>
                <span className="text-gray-300">•</span>
                <span className="text-sm text-gray-500">{format(message.timestamp, 'PPpp')}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <MessageActions
              message={message}
              onArchive={archiveMessage}
              onDelete={deleteMessage}
              onRestore={restoreMessage}
              onExportPDF={exportToPDF}
              onAddTag={addTag}
            />
          </div>
        </div>

        {/* <div className="flex flex-wrap gap-2 mb-4">
          <StatusBadge status={message.status} />
          <CategoryBadge category={message.category} />
          <PriorityBadge priority={message.priority} />
        </div> */}

        {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center text-gray-600">
            <ClockIcon className="h-4 w-4 mr-2" />
            {format(message.timestamp, 'PPpp')}
          </div>
          {message.assigned_to && (
            <div className="flex items-center text-gray-600">
              <UserIcon className="h-4 w-4 mr-2" />
              {message.assigned_to.name}
            </div>
          )}
          <div className="flex items-center text-gray-600">
            <TagIcon className="h-4 w-4 mr-2" />
            {message.tags.length} tags
          </div>
        </div> */}

        {/* Tags */}
        {/* {message.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {message.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {tag}
              </span>
            ))}
          </div>
        )} */}

        {/* Action Buttons */}
        {/* <div className="mt-4 flex flex-wrap gap-2">
          {canAssign && (
            <button
              onClick={() => setShowEnhancedAssignmentModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <UserPlusIcon className="h-4 w-4 mr-2" />
              {message.assigned_to ? 'Reassign' : 'Assign'}
            </button>
          )}
          
          {canReport && (
            <button
              onClick={() => setShowDetailedReportModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <ClipboardDocumentListIcon className="h-4 w-4 mr-2" />
              Detailed Report
            </button>
          )}

          {(currentUser?.role === 'admin' || currentUser?.role === 'supervisor') && (
            <button
              onClick={() => setShowApprovalModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
              Approval Request
            </button>
          )}

          {canReport && message.assigned_to?.id === currentUser?.id && (
            <button
              onClick={() => setShowProgressModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <ClipboardDocumentListIcon className="h-4 w-4 mr-2" />
              Progress Report
            </button>
          )}

          {message.status === 'new' && canReply && (
            <button
              onClick={() => handleStatusChange('in-progress')}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
            >
              Start Working
            </button>
          )}
          
          {(message.status === 'in-progress' || message.status === 'replied') && canReply && (
            <button
              onClick={() => handleStatusChange('closed')}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Mark Resolved
            </button>
          )}
        </div> */}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'conversation', name: 'Conversation', icon: ChatBubbleLeftRightIcon },
            { id: 'timeline', name: 'Timeline', icon: ClockIcon },
            { id: 'attachments', name: 'Attachments', icon: DocumentArrowDownIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === tab.id
                  ? 'border-sky-500 text-sky-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.name}
              {tab.id === 'attachments' && message.attachments.length > 0 && (
                <span className="bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {message.attachments.length}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 flex flex-col overflow-y-auto">
        {/* Original Message */}
            <div className="mb-6">
              <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium text-blue-900">Original Message</div>
                  <div className="text-xs text-blue-600">
                    {format(message.timestamp, 'PPpp')}
                  </div>
                </div>
                <p className="text-blue-800">{message.content}</p>
              </div>
            </div>

        {activeTab === 'conversation' && (
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Reply Filters */}
            <div className="border-b border-gray-200 bg-gray-50">
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-700">
                    Replies ({totalCount})
                  </h4>
                  <button
                    onClick={() => setShowReplyFilters(!showReplyFilters)}
                    className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
                  >
                    <FunnelIcon className="h-4 w-4" />
                    Filter
                  </button>
                </div>

                {showReplyFilters && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Phone Number
                        </label>
                        <input
                          type="text"
                          placeholder="Filter by phone number"
                          value={replyFilters.phoneNumber}
                          onChange={(e) => setReplyFilters(prev => ({ ...prev, phoneNumber: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Search Content
                        </label>
                        <input
                          type="text"
                          placeholder="Search in replies"
                          value={replyFilters.search}
                          onChange={(e) => setReplyFilters(prev => ({ ...prev, search: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Sender Type
                        </label>
                        <select
                          value={replyFilters.senderType}
                          onChange={(e) => setReplyFilters(prev => ({ ...prev, senderType: e.target.value as '' | 'customer' | 'agent' }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">All</option>
                          <option value="customer">Customer</option>
                          <option value="agent">Agent</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Replies List with Infinite Scroll */}
            <div
              className="flex-1 overflow-y-auto p-4"
              ref={scrollContainerRef}
              onScroll={(e) => {
                const { scrollTop } = e.currentTarget;
                // Load more when scrolling near the top (for chat-like experience)
                if (scrollTop < 100 && hasMore && !loadingMore) {
                  loadMoreReplies();
                }
              }}
            >
              {/* Load More Button/Indicator at Top */}
              {hasMore && (
                <div className="text-center mb-4">
                  {loadingMore ? (
                    <div className="flex items-center justify-center gap-2 text-gray-500">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      Loading older replies...
                    </div>
                  ) : (
                    <button
                      onClick={loadMoreReplies}
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      Load older replies
                    </button>
                  )}
                </div>
              )}

              {/* Loading State */}
              {repliesLoading && replies.length === 0 && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-500">Loading replies...</p>
                </div>
              )}

              {/* Error State */}
              {repliesError && (
                <div className="text-center py-8">
                  <div className="text-red-600 mb-2">Error loading replies</div>
                  <p className="text-gray-500 text-sm">{repliesError}</p>
                </div>
              )}

              {/* Replies */}
              {replies.map((reply) => (
                <div
                  key={reply.id}
                  className={`flex ${
                    reply.is_from_customer ? "justify-start" : "justify-end"
                  } mb-3`}
                >
                  <div className={`max-w-[80%]`}>
                    <div
                      className={`rounded-lg p-3 ${reply.is_from_customer
                        ? "bg-gray-50 border-l-4 border-gray-400"
                        : "bg-green-50 border-l-4 border-green-400"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-1">
                        <div className="font-medium text-gray-700">
                          {reply.is_from_customer ? "Customer" : reply.sender?.name || "Agent"}
                        </div>
                        <div className="text-xs text-gray-500 ml-2">
                          {formattedTime(reply.timestamp)}
                        </div>
                      </div>
                      <p className="text-gray-800">{reply.content}</p>
                      {reply.attachments && reply.attachments.length > 0 && (
                        <AttachmentViewer attachments={reply.attachments} />
                      )}
                      {!reply.is_from_customer && (
                        <div className="text-right mt-1">
                          <span className="text-xs text-gray-500">
                            {reply.status === "sent" && "✓"}
                            {reply.status === "delivered" && "✓✓"}
                            {(reply.status !== "sent" && reply.status !== "delivered") && (
                              <span>{reply.status}</span>
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Empty State */}
              {!repliesLoading && replies.length === 0 && !repliesError && (
                <div className="text-center py-8">
                  <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No replies yet</p>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>


            {/* Reply Box */}
            {canReply && message.status !== 'closed' && (
              <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Send Reply</h3>
                <ReplyBox onSend={handleSendReply} category={message.category} />
              </div>
            )}
          </div>
        )}

        {activeTab === 'timeline' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Case Timeline</h3>
            <CaseTimeline events={timelineEvents} />
          </div>
        )}

        {activeTab === 'attachments' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Attachments</h3>
            {message.attachments.length > 0 ? (
              <AttachmentViewer attachments={message.attachments} />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DocumentArrowDownIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No attachments</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <EnhancedAssignmentModal
        isOpen={showEnhancedAssignmentModal}
        onClose={() => setShowEnhancedAssignmentModal(false)}
        onAssign={handleAssignment}
        currentAssignee={message.assigned_to}
        messageId={message.id}
        caseId={message.caseId}
      />

      <ApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        onApprove={handleApproval}
        message={message}
        approvalRequest={mockApprovalRequest}
      />

      <DetailedReportModal
        isOpen={showDetailedReportModal}
        onClose={() => setShowDetailedReportModal(false)}
        onSubmit={handleDetailedReport}
        message={message}
      />

      <ProgressReportModal
        isOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
        onSubmit={handleProgressReport}
        caseId={message.caseId}
      />
    </div>
  );
}