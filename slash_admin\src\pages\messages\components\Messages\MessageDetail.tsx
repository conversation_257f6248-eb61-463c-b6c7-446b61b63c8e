import React, { useEffect, useRef, useState } from 'react';
import { format, isToday } from 'date-fns';
import {
  UserIcon,
  ClockIcon,
  TagIcon,
  PaperAirplaneIcon,
  DocumentArrowDownIcon,
  UserPlusIcon,
  ClipboardDocumentListIcon,
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { SMSMessage, User, TimelineEvent, ProgressReport } from '../../../../types';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMessages } from '../../../../hooks/useMessages';
import { mockUsers } from '../../../../data/mockData';
import StatusBadge from './StatusBadge';
import CategoryBadge from './CategoryBadge';
import PriorityBadge from './PriorityBadge';
import ReplyBox from './ReplyBox';
import AssignmentModal from './AssignmentModal';
import EnhancedAssignmentModal from './EnhancedAssignmentModal';
import ApprovalModal from './ApprovalModal';
import DetailedReportModal from '../Reports/DetailedReportModal';
import ProgressReportModal from './ProgressReportModal';
import CaseTimeline from './CaseTimeline';
import AttachmentViewer from './AttachmentViewer';
import MessageActions from './MessageActions';

interface MessageDetailProps {
  message: SMSMessage;
}

export default function MessageDetail({ message }: MessageDetailProps) {
  const { currentUser } = useAuth();
  const {
    addReply,
    assignMessage,
    updateMessageStatus,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF
  } = useMessages();

  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showEnhancedAssignmentModal, setShowEnhancedAssignmentModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showDetailedReportModal, setShowDetailedReportModal] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'conversation' | 'timeline' | 'attachments'>('conversation');

  // cont function ConversationHistory({ message }) {
  const repliesEndRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  // Scroll to bottom on initial load and when replies change
    useEffect(() => {
      scrollToBottom(true);
    }, [message.replies]);
  
    const scrollToBottom = (instant = false) => {
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTop = scrollContainerRef.current.scrollHeight;
      }
      if (messagesEndRef.current && instant) {
        messagesEndRef.current.scrollIntoView({ behavior: 'auto' });
      }
    };

  function formattedTime(timestamp: string | Date) {
    const date = new Date(timestamp);
    return isToday(date) ? format(date, 'h:mm a') : format(date, 'MMM d');
  }

  // Mock timeline events for demonstration
  const timelineEvents: TimelineEvent[] = [
    {
      id: '1',
      type: 'message',
      timestamp: message.timestamp,
      user: { id: 'customer', name: 'Customer', email: '', role: 'viewer', department: '', isActive: true },
      description: 'Initial message received',
      metadata: message.content
    },
    ...(message.assigned_to ? [{
      id: '2',
      type: 'assignment' as const,
      timestamp: new Date(message.timestamp.getTime() + 5 * 60 * 1000),
      user: currentUser!,
      description: `Assigned to ${message.assigned_to.name}`,
      metadata: `Case assigned to ${message.assigned_to.department}`
    }] : []),
    ...message.replies.map((reply, index) => ({
      id: `reply-${index}`,
      type: 'reply' as const,
      timestamp: reply.timestamp,
      user: reply.sender,
      description: 'Reply sent to customer',
      metadata: reply.content
    }))
  ];

  const handleSendReply = (content: string) => {
    if (currentUser) {
      addReply(message.id, content, currentUser);

      // Trigger notification
      window.dispatchEvent(new CustomEvent('newNotification', {
        detail: {
          type: 'message',
          title: 'Reply Sent',
          message: `Reply sent to ${message.phone_number}`,
          caseId: message.caseId
        }
      }));
    }
  };

  const handleAssignment = (assignment: {
    userId: string;
    comment: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    dueDate: string;
    estimatedHours?: number;
    skillsRequired?: string[];
  }) => {
    // In a real app, you'd find the user by ID
    const mockUser = {
      id: assignment.userId,
      name: 'Assigned User',
      email: '',
      role: 'technician' as const,
      department: 'Field Operations',
      isActive: true
    };

    assignMessage(message.id, mockUser);
    setShowEnhancedAssignmentModal(false);

    // Trigger notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: {
        type: 'assignment',
        title: 'Case Assigned',
        message: `Case ${message.caseId} has been assigned`,
        caseId: message.caseId
      }
    }));
  };

  const handleProgressReport = (report: {
    content: string;
    attachments: File[];
    checklist: any[];
  }) => {
    // In a real app, this would save the progress report
    console.log('Progress report submitted:', report);
    setShowProgressModal(false);

    // Trigger notification
    window.dispatchEvent(new CustomEvent('newNotification', {
      detail: {
        type: 'report',
        title: 'Progress Report',
        message: `Progress report submitted for ${message.caseId}`,
        caseId: message.caseId
      }
    }));
  };

  const handleStatusChange = (newStatus: SMSMessage['status']) => {
    updateMessageStatus(message.id, newStatus);
  };

  const handleApproval = (decision: 'approved' | 'rejected', comments: string) => {
    // In a real app, this would update the approval status
    console.log('Approval decision:', decision, comments);
    setShowApprovalModal(false);
  };

  const handleDetailedReport = (report: any) => {
    // In a real app, this would save the detailed report
    console.log('Detailed report submitted:', report);
    setShowDetailedReportModal(false);
  };

  // Mock approval request for demo
  const mockApprovalRequest = {
    id: '1',
    messageId: message.id,
    type: 'escalation' as const,
    description: 'Request approval for emergency response escalation',
    priority: 'high' as const,
    requestedBy: currentUser!,
    requestedAt: new Date(),
    approvers: [mockUsers[0]],
    status: 'pending' as const,
    approvalHistory: []
  };

  const canAssign = currentUser?.role === 'admin' || currentUser?.role === 'supervisor';
  const canReply = currentUser?.role !== 'viewer';
  const canReport = currentUser?.role === 'technician' || currentUser?.role === 'supervisor';

  return (
    <div className="flex-1 flex flex-col bg-white h-full">
      {/* Modern Header */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-white to-gray-50">
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-blue-100 rounded-xl">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{message.phone_number}</h2>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-500">Case #{message.caseId}</span>
                <span className="text-gray-300">•</span>
                <span className="text-sm text-gray-500">{format(message.timestamp, 'PPpp')}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <MessageActions
              message={message}
              onArchive={archiveMessage}
              onDelete={deleteMessage}
              onRestore={restoreMessage}
              onExportPDF={exportToPDF}
              onAddTag={addTag}
            />
          </div>
        </div>

        {/* <div className="flex flex-wrap gap-2 mb-4">
          <StatusBadge status={message.status} />
          <CategoryBadge category={message.category} />
          <PriorityBadge priority={message.priority} />
        </div> */}

        {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center text-gray-600">
            <ClockIcon className="h-4 w-4 mr-2" />
            {format(message.timestamp, 'PPpp')}
          </div>
          {message.assigned_to && (
            <div className="flex items-center text-gray-600">
              <UserIcon className="h-4 w-4 mr-2" />
              {message.assigned_to.name}
            </div>
          )}
          <div className="flex items-center text-gray-600">
            <TagIcon className="h-4 w-4 mr-2" />
            {message.tags.length} tags
          </div>
        </div> */}

        {/* Tags */}
        {/* {message.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {message.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {tag}
              </span>
            ))}
          </div>
        )} */}

        {/* Action Buttons */}
        {/* <div className="mt-4 flex flex-wrap gap-2">
          {canAssign && (
            <button
              onClick={() => setShowEnhancedAssignmentModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <UserPlusIcon className="h-4 w-4 mr-2" />
              {message.assigned_to ? 'Reassign' : 'Assign'}
            </button>
          )}
          
          {canReport && (
            <button
              onClick={() => setShowDetailedReportModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <ClipboardDocumentListIcon className="h-4 w-4 mr-2" />
              Detailed Report
            </button>
          )}

          {(currentUser?.role === 'admin' || currentUser?.role === 'supervisor') && (
            <button
              onClick={() => setShowApprovalModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
              Approval Request
            </button>
          )}

          {canReport && message.assigned_to?.id === currentUser?.id && (
            <button
              onClick={() => setShowProgressModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500"
            >
              <ClipboardDocumentListIcon className="h-4 w-4 mr-2" />
              Progress Report
            </button>
          )}

          {message.status === 'new' && canReply && (
            <button
              onClick={() => handleStatusChange('in-progress')}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
            >
              Start Working
            </button>
          )}
          
          {(message.status === 'in-progress' || message.status === 'replied') && canReply && (
            <button
              onClick={() => handleStatusChange('closed')}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Mark Resolved
            </button>
          )}
        </div> */}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'conversation', name: 'Conversation', icon: ChatBubbleLeftRightIcon },
            { id: 'timeline', name: 'Timeline', icon: ClockIcon },
            { id: 'attachments', name: 'Attachments', icon: DocumentArrowDownIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === tab.id
                  ? 'border-sky-500 text-sky-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.name}
              {tab.id === 'attachments' && message.attachments.length > 0 && (
                <span className="bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {message.attachments.length}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 flex flex-col overflow-y-auto">
        {/* Original Message */}
            <div className="mb-6">
              <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-400">
                <div className="flex justify-between items-start mb-2">
                  <div className="font-medium text-blue-900">Original Message</div>
                  <div className="text-xs text-blue-600">
                    {format(message.timestamp, 'PPpp')}
                  </div>
                </div>
                <p className="text-blue-800">{message.content}</p>
              </div>
            </div>

        {activeTab === 'conversation' && (
          <div className="flex-1 flex flex-col overflow-hidden">
            <div 
              className="flex-1 overflow-y-auto p-4 flex flex-col-reverse"
              ref={scrollContainerRef}
              style={{ minHeight: 0 }} // Needed for proper scrolling in flex container
            >
              {/* This empty div helps with initial scroll position */}
              <div ref={messagesEndRef} />
            

               {[...message.replies].reverse().map((reply) => (
                <div
                  key={reply.id}
                  className={`flex ${
                    reply.is_from_customer ? "justify-start" : "justify-end"
                  } mb-3`}
                >
                  <div className={`max-w-[80%]`}>
                                      <div
                                        className={`rounded-lg p-3 ${reply.is_from_customer
                        ? "bg-gray-50 border-l-4 border-gray-400"
                        : "bg-green-50 border-l-4 border-green-400"
                      }`}
                                      >
                                        <div className="flex justify-between items-start mb-1">
                                          <div className="font-medium text-gray-700">
                                            {reply.is_from_customer ? "Customer" : reply.sender.name}
                                          </div>
                                          <div className="text-xs text-gray-500 ml-2">
                                            {formattedTime(reply.timestamp)}
                                          </div>
                                        </div>
                                        <p className="text-gray-800">{reply.content}</p>
                                        {reply.attachments.length > 0 && (
                                          <AttachmentViewer attachments={reply.attachments} />
                                        )}
                                        {!reply.is_from_customer && (
                                          <div className="text-right mt-1">
                                            <span className="text-xs text-gray-500">
                                              {reply.status === "sent" && "✓"}
                                              {reply.status === "delivered" && "✓✓"}
                                              {(reply.status !== "sent" && reply.status !== "delivered") && (
                                                <span>{reply.status}</span>
                                              )}
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                </div>
              ))}

            </div>


            {/* Reply Box */}
            {canReply && message.status !== 'closed' && (
              <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Send Reply</h3>
                <ReplyBox onSend={handleSendReply} category={message.category} />
              </div>
            )}
          </div>
        )}

        {activeTab === 'timeline' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Case Timeline</h3>
            <CaseTimeline events={timelineEvents} />
          </div>
        )}

        {activeTab === 'attachments' && (
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Attachments</h3>
            {message.attachments.length > 0 ? (
              <AttachmentViewer attachments={message.attachments} />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <DocumentArrowDownIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No attachments</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <EnhancedAssignmentModal
        isOpen={showEnhancedAssignmentModal}
        onClose={() => setShowEnhancedAssignmentModal(false)}
        onAssign={handleAssignment}
        currentAssignee={message.assigned_to}
        messageId={message.id}
        caseId={message.caseId}
      />

      <ApprovalModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        onApprove={handleApproval}
        message={message}
        approvalRequest={mockApprovalRequest}
      />

      <DetailedReportModal
        isOpen={showDetailedReportModal}
        onClose={() => setShowDetailedReportModal(false)}
        onSubmit={handleDetailedReport}
        message={message}
      />

      <ProgressReportModal
        isOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
        onSubmit={handleProgressReport}
        caseId={message.caseId}
      />
    </div>
  );
}