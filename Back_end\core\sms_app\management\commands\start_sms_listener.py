"""
Django management command to start the SMS listener task.
Usage: python manage.py start_sms_listener
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from sms_app.tasks import listen_for_sms


class Command(BaseCommand):
    help = 'Start the SMS listener task for receiving SMS messages via SMPP'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--async',
            action='store_true',
            help='Run as async Celery task (requires Celery worker)',
        )
        parser.add_argument(
            '--sync',
            action='store_true',
            help='Run synchronously in current process',
        )
    
    def handle(self, *args, **options):
        if options['async']:
            self.start_async_listener()
        else:
            self.start_sync_listener()
    
    def start_async_listener(self):
        """Start SMS listener as async Celery task"""
        self.stdout.write("🚀 Starting SMS listener as async Celery task...")
        
        try:
            # Start the listener task
            result = listen_for_sms.delay()
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ SMS listener task started with ID: {result.id}")
            )
            self.stdout.write("ℹ️ Task is running in background via Celery worker")
            self.stdout.write("ℹ️ Make sure Celery worker is running: celery -A core worker -l info")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to start SMS listener task: {e}")
            )
    
    def start_sync_listener(self):
        """Start SMS listener synchronously"""
        self.stdout.write("🚀 Starting SMS listener synchronously...")
        
        # Check configuration
        smpp_config = getattr(settings, 'SMPP_CONFIG', {})
        if not smpp_config:
            self.stdout.write(
                self.style.ERROR("❌ SMPP_CONFIG not found in settings")
            )
            return
        
        self.stdout.write(f"📡 Connecting to {smpp_config.get('HOST')}:{smpp_config.get('PORT')}")
        self.stdout.write(f"🆔 System ID: {smpp_config.get('SYSTEM_ID')}")
        self.stdout.write("📥 Listening for incoming SMS messages...")
        self.stdout.write("⏹️ Press Ctrl+C to stop")
        
        try:
            # Run the listener task synchronously
            result = listen_for_sms()
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ SMS listener completed: {result}")
            )
            
        except KeyboardInterrupt:
            self.stdout.write("\n🛑 SMS listener stopped by user")
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ SMS listener failed: {e}")
            )
