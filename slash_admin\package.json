{"name": "slash-admin", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://github.com/d3george/slash-admin", "scripts": {"dev": "vite", "build": "cross-env SKIP_TYPE_CHECK=true vite build", "build:strict": "tsc && vite build", "preview": "vite preview", "preinstall": "lefthook install"}, "dependencies": {"@ant-design/cssinjs": "^1.17.2", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^2.6.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource-variable/inter": "^5.1.0", "@fontsource-variable/open-sans": "^5.1.0", "@fullcalendar/common": "^5.11.5", "@fullcalendar/core": "^6.1.9", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@fullcalendar/timeline": "^6.1.9", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@iconify/react": "^4.1.1", "@maptiler/leaflet-maptilersdk": "^4.0.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.50.1", "@tanstack/react-query-devtools": "^5.50.1", "@types/supercluster": "^7.1.3", "@vanilla-extract/css": "^1.17.0", "@vanilla-extract/vite-plugin": "^4.0.19", "@vercel/analytics": "^1.2.2", "@vitejs/plugin-react": "^4.1.0", "antd": "^5.9.3", "apexcharts": "^4.0.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.5.1", "class-variance-authority": "^0.7.1", "classnames": "^2.3.2", "clsx": "^2.1.1", "color": "^4.2.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^10.16.4", "highlight.js": "^11.9.0", "i18next": "^23.5.1", "i18next-browser-languagedetector": "^7.1.0", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "qs": "^6.14.0", "ramda": "^0.29.1", "react": "18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "18.2.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^4.0.13", "react-helmet-async": "^2.0.5", "react-i18next": "^13.2.2", "react-icons": "^4.11.0", "react-leaflet": "^4.2.1", "react-markdown": "^8.0.7", "react-organizational-chart": "^2.2.1", "react-quill": "^2.0.0", "react-router": "^7.0.2", "react-use": "^17.4.0", "rehype-highlight": "^6.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "reset-css": "^5.0.2", "screenfull": "^6.0.2", "simplebar-react": "^3.2.4", "sonner": "^1.7.0", "styled-components": "^6.0.9", "supercluster": "^8.0.1", "swr": "^2.3.2", "tailwind-merge": "^2.5.4", "use-supercluster": "^1.2.0", "xlsx": "^0.18.5", "zustand": "^4.4.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^17.7.2", "@commitlint/config-conventional": "^17.7.0", "@faker-js/faker": "^8.1.0", "@types/autosuggest-highlight": "^3.2.0", "@types/color": "^3.0.4", "@types/leaflet": "^1.9.16", "@types/lodash": "^4.17.16", "@types/nprogress": "^0.2.1", "@types/numeral": "^2.0.3", "@types/ramda": "^0.29.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@types/styled-components": "^5.1.28", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "lefthook": "^1.8.2", "msw": "^2.4.9", "postcss": "^8.4.31", "postcss-import": "^15.1.0", "postcss-nesting": "^11.3.0", "rollup-plugin-visualizer": "^5.9.2", "tailwindcss": "^3.3.3", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite": "^5.4.9", "vite-plugin-svg-icons": "^2.0.1", "vite-tsconfig-paths": "^5.0.1"}, "engines": {"node": "20.*"}, "packageManager": "pnpm@9.1.0", "msw": {"workerDirectory": "public"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}