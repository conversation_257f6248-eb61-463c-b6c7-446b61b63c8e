import apiClient from "../apiClient";

import type { UserInfo, UserToken } from "#/entity";

export interface SignInReq {
	username: string;
	password: string;
}

export interface SignUpReq extends SignInReq {
	email: string;
}
export type SignInRes = UserToken & { user: UserInfo };

export interface UpdatePasswordReq {
	old_password: string;
	new_password: string;
}

export enum UserApi {
	SignIn = "/auth/signin",
	SignUp = "/auth/signup",
	Logout = "/auth/logout",
	Refresh = "/auth/refresh",
	User = "/auth/users",
	updateUserProfile = "/auth/users/:id/",
	adminUpdateUserProfile = "/auth/users/:id/profile/",
	UpdatePassword = "/auth/users/password/",
	resetPassword = "/auth/reset-password/",
	confirmResetPassword = "/auth/confirm-reset-password/",
	adminResetPassword = "/auth/users/:id/admin-reset-password/",
}

interface AdminUpdateUserProfileData {
	username?: string;
	email?: string;
	phone?: string;
	full_name?: string;
	region?: string;
	csc?: string;
	title?: string;
	address?: string;
	city?: string;
	about?: string;
}

const signin = (data: SignInReq) => apiClient.post<SignInRes>({ url: UserApi.SignIn, data });
const signup = (data: SignUpReq) => apiClient.post<SignInRes>({ url: UserApi.SignUp, data });

const findById = (id: string) => apiClient.get<UserInfo[]>({ url: `${UserApi.User}/${id}` });

const updateUserProfile = async (userId: string, data: Partial<UserInfo>): Promise<void> => {
	try {
		await apiClient.patch<any>({
			url: UserApi.updateUserProfile.replace(":id", userId.toString()),
			data,
		});
	} catch (error) {
		throw new Error("Failed to update user profile");
	}
};

const updateAvatar = async (userId: string, file: File): Promise<string> => {
	const formData = new FormData();
	formData.append("avatar", file);

	const response = await apiClient.patch<{ avatar_url: string }>({
		url: `${UserApi.User}/${userId}/avatar/`,
		data: formData,
		headers: {
			"Content-Type": "multipart/form-data",
		},
	});

	return response.avatar_url;
};

const updatePassword = (data: UpdatePasswordReq) =>
	apiClient.post<void>({
		url: UserApi.UpdatePassword,
		data,
	});

const resetPassword = async (email: string) => {
	const response = await apiClient.post<void>({
		url: UserApi.resetPassword,
		data: { email },
	});
	return response.data;
};

const confirmResetPassword = (data: { uid: string; token: string; new_password: string }) => {
	return apiClient.post<void>({
		url: UserApi.confirmResetPassword,
		data,
	});
};

const getUser = async (id: string): Promise<UserInfo> => {
	try {
		const response = await apiClient.get<UserInfo>({ 
			url: `${UserApi.User}/${id}/`
		});
		return response;
	} catch (error) {
		throw new Error("Failed to fetch user details");
	}
};

const adminResetPassword = async (userId: string, data: { new_password: string }): Promise<void> => {
	try {
		await apiClient.post<void>({
			url: UserApi.adminResetPassword.replace(":id", userId),
			data,
		});
	} catch (error) {
		throw new Error("Failed to reset user password");
	}
};

const adminUpdateUserProfile = async (userId: string, data: AdminUpdateUserProfileData): Promise<void> => {
	try {
		await apiClient.patch<void>({
			url: UserApi.adminUpdateUserProfile.replace(":id", userId),
			data,
		});
	} catch (error) {
		throw new Error("Failed to update user profile");
	}
};

const logout = async () => {
	const refresh_token = "localStorage.getItem('refresh_token')"

	const userStoreData = localStorage.getItem("userStore");

		if (userStoreData) {
			try {
				// Step 2: Parse the data into a JavaScript object
				const userStoreObject = JSON.parse(userStoreData);

				// Step 3: Access the accessToken
				const  refreshToken = userStoreObject?.state?.userToken?.refreshToken;

				if (refreshToken) {
					const response = await apiClient.post<AnalyserOptions>({
						url: UserApi.Logout,
						data:  { refreshToken },
					});
				} else {
					console.error("Access token not found in userStore");
				}
			} catch (error) {
				console.error("Error parsing userStore data:", error);
			}
		} else {
			console.error("userStore not found in localStorage");
		}
};

export default {
	signin,
	signup,
	findById,
	updateUserProfile,
	updateAvatar,
	updatePassword,
	resetPassword,
	confirmResetPassword,
	getUser,
	adminResetPassword,
	adminUpdateUserProfile,
	logout,
};



