
import React, { useEffect, useState, useCallback } from "react";
import Table, { type ColumnsType } from "antd/es/table";
import type { TablePaginationConfig } from "antd/es/table";
import type { FilterValue, SorterResult } from "antd/es/table/interface";
import transformerService from "@/api/services/transformerService";
import { <PERSON>ton, Card, Popconfirm } from "antd";
import { IconButton, Iconify } from "@/components/icon";
import type { Inspection } from "#/entity";
import { toast } from "sonner";
import { usePathname, useRouter } from "@/router/hooks";
import { formatDate } from "@fullcalendar/core/index.js";
import { FilterOutlined } from '@ant-design/icons';
import FilterForm from './FilterForm';
import { debounce } from 'lodash';
import * as XLSX from "xlsx";
import dayjs from 'dayjs';

// Helper function to clean params
const cleanParams = (params: any) => {
  return Object.entries(params).reduce((acc: any, [key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      acc[key] = value;
    }
    return acc;
  }, {});
};

// Define the table parameters interface
interface TableParams {
  pagination?: TablePaginationConfig;
  sortField?: string;
  sortOrder?: string;
  filters?: Record<string, FilterValue>;
}

// Default Inspection Value
const DEFAULT_INSPECTION_VALUE: Inspection = {
  id: 0,
  body_condition: "",
  arrester: "",
  drop_out: "",
  fuse_link: "",
  bushing: "",
  cable_lugs: "",
  horn_gap: "",
  tap_changer_position: "",
  oil_level: "",
  oil_leakage: "",
  silica_gel: "",
  cable_size: "",
  neutral_ground: "",
  arrester_body_ground: "",
  N_load_current: "",
  R_S_Voltage: "",
  R_T_Voltage: "",
  T_S_Voltage: "",
  created_at: new Date(),
  updated_at: new Date(),
};

export default function LatestInspection() {
  const { push } = useRouter();
  const pathname = usePathname();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Inspection[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  
  const [activeFilters, setActiveFilters] = useState({});
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    filters: {},
  });

  console.log("activeFilters", activeFilters)

  const handleFiltersUpdate = useCallback(
    debounce(async (activeFilterss: any, currentPage?: number, pageSize?: number) => {
      const formattedParams = {
        page: currentPage || tableParams.pagination?.current || 1,
        pageSize: pageSize || tableParams.pagination?.pageSize || 10,
        searchType: "LatestInspection",
        ...activeFilterss,
      };

      const cleanFilters = cleanParams(formattedParams);
    
      try {
        setLoading(true);
        const response = await transformerService.getBasestationsFiltered(cleanFilters);
        if (response) {
          setData(response.results);
          setTableParams(prev => ({
            ...prev,
            pagination: {
              ...prev.pagination,
              current: currentPage || prev.pagination?.current || 1,
              pageSize: pageSize || prev.pagination?.pageSize || 10,
              total: response.count || 0,
            },
          }));
          setActiveFilters(activeFilterss);
        }
      } catch (error) {
        console.error("Error fetching filtered data:", error);
        toast.error("Failed to apply filters");
      } finally {
        setLoading(false);
      }
    }, 300),
    [tableParams.pagination]
  );

  useEffect(() => {
    handleFiltersUpdate({});
  }, []);

  const handleFilterChange = (values: any) => {
    // Update activeFilters with all form values
    console.log("Form values 333333333333333333:", values);
    setActiveFilters(values);
  };


  const handleResetFilters = () => {
    // Clear all filters
    setActiveFilters({});
    
    // Reset table to first page with default page size
    handleFiltersUpdate({}, 1, tableParams.pagination?.pageSize);
  };

  const handleApplyFilters = () => {
    // Apply the current activeFilters
    handleFiltersUpdate(activeFilters, 1, tableParams.pagination?.pageSize);
  };

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue>,
    sorter: SorterResult<Inspection>
  ) => {
    const newParams = {
      ...activeFilters,
      sortField: sorter.field,
      sortOrder: sorter.order,
    };
    
    handleFiltersUpdate(
      newParams,
      pagination.current,
      pagination.pageSize
    );
  };

  // Define the columns for the table
  const columns: ColumnsType<Inspection> = [
    {
      title: "Transformer ID",
      dataIndex: "transformer_data",
      width: 150,
      render: (transformerData) => transformerData?.id,
    },
    {
      title: "Body Condition",
      dataIndex: "body_condition",
      width: 150,
    },
    {
      title: "Arrester",
      dataIndex: "arrester",
      width: 100,
    },
    // {
    //   title: "Drop Out",
    //   dataIndex: "drop_out",
    //   width: 150,
    // },
    // {
    //   title: "Fuse Link",
    //   dataIndex: "fuse_link",
    //   width: 150,
    // },

    {
      title: "Voltage Phase Unbalance",
      dataIndex: "voltage_phase_unbalance",
      width: 150,
    },
    {
      title: "Average Voltage",
      dataIndex: "average_voltage",
      width: 150,
    },
    {
      title: "region",
      dataIndex: "transformer_data",
      width: 150,
      render: (transformerData) => transformerData?.basestation?.region,
    },
    {
      title: "csc",
      dataIndex: "transformer_data", 
      width: 150,
      render: (transformerData) => transformerData?.basestation?.csc,
    }, 
    {
      title: "Created At",
      dataIndex: "created_at",
      width: 150,
      render: (date) => dayjs(date).format("M/D/YYYY HH:mm"),
    },
    {
      title: "Action",
      key: "operation",
      align: "center",
      width: 120,
      render: (_, record) => (
        <div className="flex w-full justify-center text-gray">
          <IconButton
            onClick={() => {
              console.log(`${pathname}/${record.id}`);
              push(`/transformer/inspection/${record.id}`);
            }}
          >
            <Iconify icon="carbon:view" size={18} />
          </IconButton>
        </div>
      ),
    },
  ];

  const handleExportExcel = () => {
  // Prepare data for export (flatten nested objects if needed)
  const exportData = data.map(item => ({
    "Transformer ID": item.transformer_data?.id || "",
    "Body Condition": item.body_condition || "",
    "Arrester": item.arrester || "",
    "Drop Out": item.drop_out || "",
    "Fuse Link": item.fuse_link || "",
    "Bushing": item.bushing || "",
    "Cable Lugs": item.cable_lugs || "",
    "Horn Gap": item.horn_gap || "",
    "Tap Changer Position": item.tap_changer_position || "",
    "Oil Level": item.oil_level || "",
    "Oil Leakage": item.oil_leakage || "",
    "Silica Gel": item.silica_gel || "",
    "Cable Size": item.cable_size || "",
    "Neutral Ground": item.neutral_ground || "",
    "Arrester Body Ground": item.arrester_body_ground || "",
    "N Load Current": item.N_load_current || "",
    "R-S Voltage": item.R_S_Voltage || "",
    "R-T Voltage": item.R_T_Voltage || "",
    "T-S Voltage": item.T_S_Voltage || "",
    "Voltage Phase Unbalance": item.voltage_phase_unbalance || "",
    "Average Voltage": item.average_voltage || "",
    "Created At": item.created_at ? new Date(item.created_at).toLocaleString() : "",
    "Updated At": item.updated_at ? new Date(item.updated_at).toLocaleString() : "",
  }));

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Latest Inspections");
  XLSX.writeFile(workbook, "latest-inspections.xlsx");
};

  return (
    <Card
      title="Latest Inspections"
      extra={
        <>
        <Button
          icon={<FilterOutlined />}
          onClick={() => setShowFilters(!showFilters)}
        >
          Filters
        </Button>
        <Button onClick={handleExportExcel} className="ml-2">
            Export to Excel
          </Button>
          </>
      }
    >
      {showFilters && (
        <div className="mt-4 bg-gray-50 p-4 rounded-lg">
          <FilterForm
            filters={activeFilters}
            onFilterChange={handleFilterChange}
            onReset={handleResetFilters}
            onApply={handleApplyFilters}
          />
        </div>
      )}
      <Table
        columns={columns}
        rowKey="id"
        dataSource={data}
        pagination={{
          ...tableParams.pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} items`,
        }}
        loading={loading}
        onChange={handleTableChange}
      />
    </Card>
  );
}


