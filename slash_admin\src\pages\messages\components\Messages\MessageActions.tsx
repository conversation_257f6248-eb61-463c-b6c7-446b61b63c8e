import React, { useState } from 'react';
import { 
  ArchiveBoxIcon, 
  TrashIcon, 
  ArrowUturnLeftIcon,
  DocumentArrowDownIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { SMSMessage } from '../../../../types';

interface MessageActionsProps {
  message: SMSMessage;
  onArchive: (messageId: string) => void;
  onDelete: (messageId: string) => void;
  onRestore: (messageId: string) => void;
  onExportPDF: (messageId: string) => void;
  onAddTag: (messageId: string, tag: string) => void;
}

export default function MessageActions({
  message,
  onArchive,
  onDelete,
  onRestore,
  onExportPDF,
  onAddTag
}: MessageActionsProps) {
  const [showTagInput, setShowTagInput] = useState(false);
  const [newTag, setNewTag] = useState('');

  const handleAddTag = (e: React.FormEvent) => {
    e.preventDefault();
    if (newTag.trim()) {
      onAddTag(message.id, newTag.trim());
      setNewTag('');
      setShowTagInput(false);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      {/* Add Tag */}
      <div className="relative">
        <button
          onClick={() => setShowTagInput(!showTagInput)}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          title="Add Tag"
        >
          <TagIcon className="h-4 w-4" />
        </button>
        
        {showTagInput && (
          <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10 min-w-48">
            <form onSubmit={handleAddTag} className="flex gap-2">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Enter tag..."
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-sky-500 focus:border-sky-500"
                autoFocus
              />
              <button
                type="submit"
                className="px-3 py-1 text-sm bg-sky-600 text-white rounded hover:bg-sky-700"
              >
                Add
              </button>
            </form>
          </div>
        )}
      </div>

      {/* Export PDF */}
      <button
        onClick={() => onExportPDF(message.id)}
        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        title="Export PDF"
      >
        <DocumentArrowDownIcon className="h-4 w-4" />
      </button>

      {/* Archive/Restore */}
      {message.isArchived ? (
        <button
          onClick={() => onRestore(message.id)}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          title="Restore"
        >
          <ArrowUturnLeftIcon className="h-4 w-4" />
        </button>
      ) : (
        <button
          onClick={() => onArchive(message.id)}
          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          title="Archive"
        >
          <ArchiveBoxIcon className="h-4 w-4" />
        </button>
      )}

      {/* Delete */}
      <button
          onClick={() => {
            if (window.confirm('Are you sure you want to delete this message?')) {
              onDelete(message.id);
            }
          }}
          className="p-2 text-red-400 hover:text-red-600 transition-colors"
          title="Delete"
        >
          <TrashIcon className="h-4 w-4" />
        </button>
    </div>
  );
}