import { Button, DatePicker, Form, Input, Modal, Select, Radio } from "antd";
import { useEffect, useState } from "react";
import type { MaintenanceModalProps } from "./index";
import dayjs from "dayjs";
import { useMutation, useQuery } from "@tanstack/react-query";
import maintenanceService from "@/api/services/maintenanceService";
import roleService from "@/api/services/roleService";
import { toast } from "sonner";
import type { UserInfo } from "#/entity";

const { Option } = Select;

export default function MaintenanceModal({ formValue, title, show, onOk, onCancel }: MaintenanceModalProps) {
	const [form] = Form.useForm();

	// Use React Query to fetch users
	const { data: users = [], isLoading } = useQuery<UserInfo[]>({
		queryKey: ["users"],
		queryFn: () => roleService.getUser(),
		staleTime: 5 * 60 * 1000, // Cache for 5 minutes
	});

	const maintenanceMutation = useMutation({
		mutationFn: maintenanceService.createMaintenance,
	});

	useEffect(() => {
		if (show) {
			form.setFieldsValue({
				...formValue,
				date_scheduled: formValue.date_scheduled ? dayjs(formValue.date_scheduled) : null,
				date_due: formValue.date_due ? dayjs(formValue.date_due) : null,
				date_completed: formValue.date_completed ? dayjs(formValue.date_completed) : null,
			});
		}
	}, [show, form, formValue]);

	const handleOk = () => {
		form
			.validateFields()
			.then(async (values) => {
				const formData = {
					...values,
					date_scheduled: values.date_scheduled?.format("YYYY-MM-DD"),
					date_due: values.date_due?.format("YYYY-MM-DD"),
					date_completed: values.date_completed?.format("YYYY-MM-DD"),
				};

				try {
					if (title === "New") {
						await maintenanceService.createMaintenance(formData);
						toast.success("Work order created successfully!");
					} else {
						await maintenanceService.updateMaintenance(formValue.id, formData);
						toast.success("Work order updated successfully");
					}
					onOk();
					form.resetFields();
				} catch (error) {
					toast.error(error.message || "Operation failed");
					console.error("Operation failed:", error);
				}
			})
			.catch((info) => {
				console.log("Validate Failed:", info);
				toast.error("Please check all required fields");
			});
	};

	const handleCancel = () => {
		form.resetFields();
		onCancel();
	};

	return (
		<Modal
			title={`${title} Work Order`}
			open={show}
			onCancel={handleCancel}
			footer={[
				<Button key="cancel" onClick={handleCancel}>
					Cancel
				</Button>,
				<Button key="save" type="primary" onClick={handleOk}>
					Save
				</Button>,
			]}
			width={600}
			maskClosable={false}
			destroyOnClose
		>
			<Form form={form} layout="vertical" initialValues={formValue}>
				{/* Code */}
				<Form.Item name="code" label="Code:" rules={[{ required: true, message: "Please input the code!" }]}>
					<Input placeholder="Enter code" />
				</Form.Item>

				{/* Asset */}
				<Form.Item name="asset" label="Asset:" rules={[{ required: true, message: "Please select an asset!" }]}>
					<Select placeholder="Select Asset">
						<Option value="Asset 1">Asset 1</Option>
						<Option value="Asset 2">Asset 2</Option>
					</Select>
				</Form.Item>

				{/* Details */}
				<Form.Item name="details" label="Details:" rules={[{ required: true, message: "Please input the details!" }]}>
					<Input.TextArea rows={4} placeholder="Type here..." />
				</Form.Item>

				{/* Type */}
				<Form.Item name="type" label="Type:" rules={[{ required: true, message: "Please select the type!" }]}>
					<Select placeholder="Select Type">
						<Option value="Breakdown">Breakdown</Option>
						<Option value="Corrective">Corrective</Option>
						<Option value="Inspection">Inspection</Option>
						<Option value="Preventive">Preventive</Option>
						<Option value="Movement">Movement</Option>
					</Select>
				</Form.Item>

				{/* Priority */}
				<Form.Item
					name="priority"
					label="Priority:"
					rules={[{ required: true, message: "Please select the priority!" }]}
				>
					<Radio.Group optionType="button" buttonStyle="solid">
						<Radio value="Low">Low</Radio>
						<Radio value="Medium">Medium</Radio>
						<Radio value="High">High</Radio>
						<Radio value="Critical">Critical</Radio>
					</Radio.Group>
				</Form.Item>

				{/* Assigned To */}
				<Form.Item name="assigned_to" label="Assigned To:">
					<Select
						mode="multiple"
						placeholder="Select Users"
						optionFilterProp="children"
						loading={isLoading}
						style={{ width: "100%" }}
					>
						{users.map((user) => (
							<Option key={user.id} value={user.id}>
								{user.username}
							</Option>
						))}
					</Select>
				</Form.Item>

				{/* Date Scheduled */}
				<Form.Item
					name="date_scheduled"
					label="Date Scheduled:"
					rules={[{ required: true, message: "Please select the scheduled date!" }]}
				>
					<DatePicker className="w-full" />
				</Form.Item>

				{/* Date Due */}
				<Form.Item
					name="date_due"
					label="Date Due:"
					rules={[{ required: true, message: "Please select the due date!" }]}
				>
					<DatePicker className="w-full" />
				</Form.Item>

				{/* Status */}
				<Form.Item name="status" label="Status:" rules={[{ required: true, message: "Please select the status!" }]}>
					<Select placeholder="Select Status">
						<Option value="New">New</Option>
						<Option value="Assigned">Assigned</Option>
						<Option value="In Progress">In Progress</Option>
						<Option value="Waiting For Parts">Waiting For Parts</Option>
						<Option value="Complete">Complete</Option>
					</Select>
				</Form.Item>
			</Form>
		</Modal>
	);
}
