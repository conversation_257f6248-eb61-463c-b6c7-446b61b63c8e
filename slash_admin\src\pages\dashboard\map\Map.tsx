
import React, { useEffect, useMemo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, LayersControl } from "react-leaflet";
import "@/assets/offline/unpkg.com/leaflet@1.7.1/dist/leaflet.css"; 
import { Icon } from "leaflet";
import BaseStationcluster from "./data/BaseStationcluster";
import { useFilteredStore } from "@/store/filteredStore";
import Transformercluster from "./data/Transformercluster";
import Inspectioncluster from "./data/Inspectioncluster";
import transformerService from "@/api/services/transformerService";
import { CircleLoading } from "@/components/loading";

// Define types for better type safety
type SearchType = "Transformer" | "LatestInspection" | "Basestation";

// Constants
const DEFAULT_CENTER: [number, number] = [9.03, 38.74];
const DEFAULT_ZOOM = 6;
const MAP_STYLE = { height: "100%", width: "100%" };

// Tile layer URLs
const SATELLITE_URL = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}";
const STREET_URL = "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png";

// Create icon once outside component to prevent recreation on each render
export const icon = new Icon({
  iconUrl: "/skateboarding.svg",
  iconSize: [25, 25],
});

// Import custom CSS to fix layer control icons
import "./map-styles.css";

/**
 * Map component that displays different types of data based on searchType
 */
const MyMap: React.FC = () => {
  const { 
    filteredbaseStation, 
    searchType, 
    setFilteredbaseStation, 
    setSearchType,
    isLoading 
  } = useFilteredStore();
  

  // Load all base stations when component mounts
  useEffect(() => {
    const fetchAllBaseStations = async () => {
      try {
        const response = await transformerService.getBasestationsFiltered({});
        
        if (response) {
          // Filter out entries without GPS coordinates
          const validStations = response.filter((station: any) => 
            station.gps_location && station.gps_location.includes(',')
          );
          
          // Only set the filtered stations if there's no existing filtered data
          if (filteredbaseStation.length === 0) {
            setFilteredbaseStation(validStations);
            setSearchType("Basestation");
          }
        }
      } catch (error) {
        console.error("Error fetching base stations:", error);
      }
    };

    // Only fetch if no data is loaded and no filtered data exists
    if (filteredbaseStation.length === 0) {
      fetchAllBaseStations();
    }
  }, []); // Empty dependency array ensures it only runs once on mount

  // Memoize the cluster component to prevent unnecessary re-renders
  const ClusterComponent = useMemo(() => {
    switch (searchType as SearchType) {
      case "Transformer":
        return <Transformercluster data={filteredbaseStation} />;
      case "LatestInspection":
        return <Inspectioncluster data={filteredbaseStation} />;
      default:
        return <BaseStationcluster data={filteredbaseStation} />;
    }
  }, [filteredbaseStation, searchType]);
  


  return (
    <div className="basis-5/12 grow relative rounded-xl z-0">
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 z-[9999] flex items-center justify-center">
          <CircleLoading />
        </div>
      )}
      <MapContainer
        center={DEFAULT_CENTER}
        zoom={DEFAULT_ZOOM}
        scrollWheelZoom={true}
        style={MAP_STYLE}
        className="map-container rounded-xl"
      >
        <LayersControl position="topright">
          <LayersControl.BaseLayer checked name="Street View">
            <TileLayer
              attribution=''
              url={STREET_URL}
            />
          </LayersControl.BaseLayer>
          
          <LayersControl.BaseLayer name="Satellite View">
            <TileLayer
              attribution=''
              url={SATELLITE_URL}
            />
          </LayersControl.BaseLayer>
        </LayersControl>
        
        {ClusterComponent}
      </MapContainer>
    </div>
  );
};

export default React.memo(MyMap);











