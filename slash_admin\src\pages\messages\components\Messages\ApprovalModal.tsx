import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon, CheckCircleIcon, XCircleIcon, ClockIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { SMSMessage, ApprovalRequest } from '../../../../types';
import { useAuth } from '../../../../contexts/AuthContext';

interface ApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApprove: (decision: 'approved' | 'rejected', comments: string) => void;
  message: SMSMessage;
  approvalRequest: ApprovalRequest;
}

export default function ApprovalModal({
  isOpen,
  onClose,
  onApprove,
  message,
  approvalRequest
}: ApprovalModalProps) {
  const { currentUser } = useAuth();
  const [decision, setDecision] = useState<'approved' | 'rejected' | null>(null);
  const [comments, setComments] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!decision) return;

    setLoading(true);
    try {
      await onApprove(decision, comments);
      onClose();
    } catch (error) {
      console.error('Approval failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <Dialog.Title className="text-lg font-medium text-gray-900">
                  Approval Request
                </Dialog.Title>
                <p className="text-sm text-gray-500">Case: {message.caseId}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Request Details */}
          <div className="p-6 border-b border-gray-200 bg-gray-50">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">Request Details</h3>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(approvalRequest.status)}`}>
                  {approvalRequest.status.charAt(0).toUpperCase() + approvalRequest.status.slice(1)}
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Requested by:</span>
                  <p className="text-gray-600">{approvalRequest.requestedBy.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Request Type:</span>
                  <p className="text-gray-600 capitalize">{approvalRequest.type.replace('_', ' ')}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Priority:</span>
                  <p className="text-gray-600 capitalize">{approvalRequest.priority}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Requested Date:</span>
                  <p className="text-gray-600">{approvalRequest.requestedAt.toLocaleDateString()}</p>
                </div>
              </div>
              
              <div>
                <span className="font-medium text-gray-700">Description:</span>
                <p className="text-gray-600 mt-1">{approvalRequest.description}</p>
              </div>
            </div>
          </div>

          {/* Case Information */}
          <div className="p-6 border-b border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-4">Case Information</h3>
            <div className="space-y-3">
              <div>
                <span className="font-medium text-gray-700">Customer:</span>
                <p className="text-gray-600">{message.phone_number}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Issue:</span>
                <p className="text-gray-600">{message.content}</p>
              </div>
              <div className="flex gap-4">
                <div>
                  <span className="font-medium text-gray-700">Category:</span>
                  <p className="text-gray-600 capitalize">{message.category.replace('-', ' ')}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Priority:</span>
                  <p className="text-gray-600 capitalize">{message.priority}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Status:</span>
                  <p className="text-gray-600 capitalize">{message.status.replace('-', ' ')}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Approval Form */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-6">
              {/* Decision */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Approval Decision *
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div
                    className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                      decision === 'approved'
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setDecision('approved')}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="decision"
                        value="approved"
                        checked={decision === 'approved'}
                        onChange={() => setDecision('approved')}
                        className="text-green-600 focus:ring-green-500"
                      />
                      <CheckCircleIcon className="h-6 w-6 text-green-600" />
                      <div>
                        <h4 className="font-medium text-gray-900">Approve</h4>
                        <p className="text-sm text-gray-600">Grant the requested approval</p>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                      decision === 'rejected'
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setDecision('rejected')}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="decision"
                        value="rejected"
                        checked={decision === 'rejected'}
                        onChange={() => setDecision('rejected')}
                        className="text-red-600 focus:ring-red-500"
                      />
                      <XCircleIcon className="h-6 w-6 text-red-600" />
                      <div>
                        <h4 className="font-medium text-gray-900">Reject</h4>
                        <p className="text-sm text-gray-600">Deny the requested approval</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Comments */}
              <div>
                <label htmlFor="comments" className="block text-sm font-medium text-gray-700 mb-2">
                  Comments {decision === 'rejected' && <span className="text-red-500">*</span>}
                </label>
                <div className="relative">
                  <textarea
                    id="comments"
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    placeholder={
                      decision === 'approved' 
                        ? "Add any conditions or notes for approval..."
                        : decision === 'rejected'
                        ? "Please provide reasons for rejection..."
                        : "Add your comments here..."
                    }
                    rows={4}
                    required={decision === 'rejected'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent resize-none"
                  />
                  <DocumentTextIcon className="absolute right-3 top-3 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Approval History */}
              {approvalRequest.approvalHistory && approvalRequest.approvalHistory.length > 0 && (
                <div className="border-t border-gray-200 pt-6">
                  <h4 className="font-medium text-gray-900 mb-3">Approval History</h4>
                  <div className="space-y-3">
                    {approvalRequest.approvalHistory.map((history, index) => (
                      <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div className={`p-1 rounded-full ${
                          history.decision === 'approved' ? 'bg-green-100' : 'bg-red-100'
                        }`}>
                          {history.decision === 'approved' ? (
                            <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircleIcon className="h-4 w-4 text-red-600" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-900">{history.approvedBy.name}</span>
                            <span className="text-xs text-gray-500">
                              {history.approvedAt.toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 capitalize">
                            {history.decision} - {history.comments}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-6 border-t border-gray-200 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!decision || loading || (decision === 'rejected' && !comments.trim())}
                className={`flex-1 px-4 py-2 rounded-md text-white transition-colors ${
                  decision === 'approved'
                    ? 'bg-green-600 hover:bg-green-700'
                    : decision === 'rejected'
                    ? 'bg-red-600 hover:bg-red-700'
                    : 'bg-gray-400'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {loading ? 'Processing...' : `Submit ${decision ? decision.charAt(0).toUpperCase() + decision.slice(1) : 'Decision'}`}
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}