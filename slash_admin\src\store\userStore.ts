import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import userService, { type SignInReq } from "@/api/services/userService";

import { toast } from "sonner";
import type { Role, UserInfo, UserToken } from "#/entity";
import { PermissionType, StorageEnum } from "#/enum";
import { ADMIN_ROLE } from "@/_mock/assets";

// First cast to unknown, then to Role
const typedAdminRole = ADMIN_ROLE as unknown as Role;

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

type UserStore = {
	userInfo: Partial<UserInfo>;
	userToken: UserToken;
	// 使用 actions 命名空间来存放所有的 action
	actions: {
		setUserInfo: (userInfo: UserInfo) => void;
		setUserToken: (token: UserToken) => void;
		clearUserInfoAndToken: () => void;
	};
};

const useUserStore = create<UserStore>()(
	persist(
		(set) => ({
			userInfo: {},
			userToken: {},
			actions: {
				setUserInfo: (userInfo) => {
					set({ userInfo });
				},
				setUserToken: (userToken) => {
					set({ userToken });
				},
				clearUserInfoAndToken() {
					set({ userInfo: {}, userToken: {} });
				},
			},
		}),
		{
			name: "userStore", // name of the item in the storage (must be unique)
			storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
			partialize: (state) => ({
				[StorageEnum.UserInfo]: state.userInfo,
				[StorageEnum.UserToken]: state.userToken,
			}),
		},
	),
);

console.log(useUserStore.getState());

export const useUserInfo = () => useUserStore((state) => state.userInfo);
export const useUserToken = () => useUserStore((state) => state.userToken);
// export const useUserPermission = () =>
// 	useUserStore((state) => state.userInfo.permissions);
export const useUserPermission = () => useUserStore((state) => state.userInfo.role?.permission);
export const useUserActions = () => useUserStore((state) => state.actions);

export const useSignIn = () => {
	const navigatge = useNavigate();
	const { setUserToken, setUserInfo } = useUserActions();

	const signInMutation = useMutation({
		mutationFn: userService.signin,
	});

	const signIn = async (data: SignInReq) => {
		try {
			const res = await signInMutation.mutateAsync(data);
			const { user, accessToken, refreshToken } = res;
			setUserToken({ accessToken, refreshToken });
			// const user1: UserInfo = {
			// 	...user,
			// 	role: typedAdminRole,
			// permissions: typedAdminRole.permission,
			// };
			// setUserInfo(user1);
			setUserInfo(user);
			navigatge(HOMEPAGE, { replace: true });
			toast.success("Sign in success!");
		} catch (err) {
			if (err.response?.data?.non_field_errors?.[0]?.includes("Invalid credentials")) {
			    toast.error("UserName(EEU ID) or Password Incorrect");
		  }
			// toast.error(err.message, {
			// 	position: "top-center",
			// });
		}
	};

	return signIn;
};

export default useUserStore;



