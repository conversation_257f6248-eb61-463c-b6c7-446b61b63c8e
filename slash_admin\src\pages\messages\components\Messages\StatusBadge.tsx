import React from 'react';
import clsx from 'clsx';

interface StatusBadgeProps {
  status: 'new' | 'in-progress' | 'replied' | 'closed';
}

const statusConfig = {
  'new': {
    label: 'New',
    className: 'bg-red-100 text-red-800'
  },
  'in-progress': {
    label: 'In Progress',
    className: 'bg-yellow-100 text-yellow-800'
  },
  'replied': {
    label: 'Replied',
    className: 'bg-blue-100 text-blue-800'
  },
  'closed': {
    label: 'Closed',
    className: 'bg-green-100 text-green-800'
  }
};

export default function StatusBadge({ status }: StatusBadgeProps) {
  const config = statusConfig[status];
  
  return (
    <span className={clsx(
      'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
      config.className
    )}>
      {config.label}
    </span>
  );
}