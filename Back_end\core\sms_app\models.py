from django.db import models
# from django.contrib.auth.models import AbstractUser
from django.utils import timezone
import uuid
from account.models import User


# class User(AbstractUser):
#     """Extended User model to match frontend requirements"""
#     ROLE_CHOICES = [
#         ('admin', 'Admin'),
#         ('supervisor', 'Supervisor'),
#         ('technician', 'Technician'),
#         ('viewer', 'Viewer'),
#     ]

#     id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
#     role = models.Char<PERSON>ield(max_length=20, choices=ROLE_CHOICES, default='viewer')
#     department = models.CharField(max_length=100, blank=True)
#     avatar = models.URLField(blank=True, null=True)
#     is_active = models.BooleanField(default=True)

#     def __str__(self):
#         return f"{self.first_name} {self.last_name} ({self.role})"


class Department(models.Model):
    """Department model for organizing users"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    color = models.CharField(max_length=7, default='#3B82F6')  # Hex color

    def __str__(self):
        return self.name


class SMSMessage(models.Model):
    """Main SMS Message model matching frontend structure"""
    STATUS_CHOICES = [
        ('new', 'New'),
        ('in-progress', 'In Progress'),
        ('replied', 'Replied'),
        ('closed', 'Closed'),
    ]

    CATEGORY_CHOICES = [
        ('power-outage', 'Power Outage'),
        ('wire-cut', 'Wire Cut'),
        ('fallen-pole', 'Fallen Pole'),
        ('corruption', 'Corruption'),
        ('billing', 'Billing'),
        ('general', 'General'),
        ('other', 'Other'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    # Core fields
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone_number = models.CharField(max_length=20)
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    # SMPP specific fields
    message_id = models.CharField(max_length=100, null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)

    # Status and categorization
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='general')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')

    # Assignment and case management
    case_id = models.CharField(max_length=50, unique=True)
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_messages')
    tags = models.JSONField(default=list)

    # Archive functionality
    is_archived = models.BooleanField(default=False)
    archived_at = models.DateTimeField(null=True, blank=True)
    archived_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='archived_messages')

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['category']),
            models.Index(fields=['priority']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['case_id']),
        ]

    def save(self, *args, **kwargs):
        if not self.case_id:
            # Generate case ID like CASE-2024-001
            from django.utils import timezone
            year = timezone.now().year
            count = SMSMessage.objects.filter(timestamp__year=year).count() + 1
            self.case_id = f"CASE-{year}-{count:03d}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.case_id} - {self.phone_number}"


class Reply(models.Model):
    """Reply model for message conversations"""
    REPLY_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('failed', 'Failed'),
        ('rejected', 'Rejected'),
        ('received', 'Received'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='replies')
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    sender = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    is_from_customer = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=REPLY_STATUS_CHOICES, default='pending')

    # SMPP specific fields
    messageId = models.CharField(max_length=100, null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['timestamp']

    # def __str__(self):
    #     return f"Reply to {self.message.case_id} by {self.sender.username}"


class Attachment(models.Model):
    """Attachment model for files"""
    TYPE_CHOICES = [
        ('image', 'Image'),
        ('document', 'Document'),
        ('other', 'Other'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    file = models.FileField(upload_to='attachments/')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='other')
    size = models.BigIntegerField()
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)

    # Can be attached to messages or replies
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, null=True, blank=True, related_name='attachments')
    reply = models.ForeignKey(Reply, on_delete=models.CASCADE, null=True, blank=True, related_name='attachments')

    @property
    def url(self):
        return self.file.url if self.file else ''

    def __str__(self):
        return self.name


class TimelineEvent(models.Model):
    """Timeline events for case tracking"""
    EVENT_TYPES = [
        ('message', 'Message'),
        ('assignment', 'Assignment'),
        ('status_change', 'Status Change'),
        ('reply', 'Reply'),
        ('report', 'Report'),
        ('note', 'Note'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='timeline_events')
    type = models.CharField(max_length=20, choices=EVENT_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    description = models.TextField()
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['timestamp']

    def __str__(self):
        return f"{self.type} - {self.message.case_id}"


class AssignmentHistory(models.Model):
    """Track assignment changes"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='assignment_history')
    from_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assignments_from')
    to_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assignments_to')
    timestamp = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True)
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assignments_made')

    def __str__(self):
        return f"Assignment: {self.message.case_id} to {self.to_user.username}"


class ChecklistItem(models.Model):
    """Checklist items for cases"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    text = models.TextField()
    completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    completed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Can belong to messages or progress reports
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, null=True, blank=True, related_name='checklist_items')

    def __str__(self):
        return self.text[:50]


class ProgressReport(models.Model):
    """Progress reports for cases"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='progress_reports')
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    reported_by = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return f"Progress Report - {self.message.case_id}"


class ProgressUpdate(models.Model):
    """Individual progress updates within a report"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    progress_report = models.ForeignKey(ProgressReport, on_delete=models.CASCADE, related_name='updates')
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE)
    percent_complete = models.IntegerField(default=0)

    def __str__(self):
        return f"Update - {self.percent_complete}%"


class Notification(models.Model):
    """Notification system"""
    NOTIFICATION_TYPES = [
        ('assignment', 'Assignment'),
        ('message', 'Message'),
        ('report', 'Report'),
        ('status_change', 'Status Change'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    read = models.BooleanField(default=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    case_id = models.CharField(max_length=50, blank=True)
    message_id = models.UUIDField(null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.title} - {self.user.username}"


class ApprovalRequest(models.Model):
    """Approval request system"""
    APPROVAL_TYPES = [
        ('budget_approval', 'Budget Approval'),
        ('escalation', 'Escalation'),
        ('resource_request', 'Resource Request'),
        ('policy_exception', 'Policy Exception'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='approval_requests')
    type = models.CharField(max_length=20, choices=APPROVAL_TYPES)
    description = models.TextField()
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    requested_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='approval_requests_made')
    requested_at = models.DateTimeField(auto_now_add=True)
    approvers = models.ManyToManyField(User, related_name='approval_requests_to_approve')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approvals_made')
    approved_at = models.DateTimeField(null=True, blank=True)
    comments = models.TextField(blank=True)

    def __str__(self):
        return f"{self.type} - {self.message.case_id}"


class ApprovalHistory(models.Model):
    """History of approval decisions"""
    DECISION_CHOICES = [
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    approval_request = models.ForeignKey(ApprovalRequest, on_delete=models.CASCADE, related_name='approval_history')
    decision = models.CharField(max_length=20, choices=DECISION_CHOICES)
    comments = models.TextField()
    approved_by = models.ForeignKey(User, on_delete=models.CASCADE)
    approved_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.decision} by {self.approved_by.username}"


class AssignmentStatus(models.Model):
    """Enhanced assignment tracking"""
    STATUS_CHOICES = [
        ('assigned', 'Assigned'),
        ('accepted', 'Accepted'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.OneToOneField(SMSMessage, on_delete=models.CASCADE, related_name='assignment_status')
    assigned_to = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assignment_statuses')
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assignments_created')
    assigned_at = models.DateTimeField(auto_now_add=True)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    due_date = models.DateTimeField(null=True, blank=True)
    estimated_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    skills_required = models.JSONField(default=list)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='assigned')
    accepted_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    comments = models.TextField(blank=True)

    def __str__(self):
        return f"Assignment: {self.message.case_id} to {self.assigned_to.username}"


class DetailedReport(models.Model):
    """Detailed reports for cases"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(SMSMessage, on_delete=models.CASCADE, related_name='detailed_reports')
    case_id = models.CharField(max_length=50)
    title = models.CharField(max_length=200)
    summary = models.TextField()
    detailed_description = models.TextField()
    actions_taken = models.TextField()
    outcome = models.TextField()
    recommendations = models.TextField()
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateField(null=True, blank=True)
    follow_up_notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return f"Report: {self.title} - {self.case_id}"


class MessageTemplate(models.Model):
    """Message templates for quick responses"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    category = models.CharField(max_length=20, choices=SMSMessage.CATEGORY_CHOICES)
    content = models.TextField()
    variables = models.JSONField(default=list)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.category})"


class SystemSettings(models.Model):
    """System-wide settings"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    categories = models.JSONField(default=list)
    working_hours = models.JSONField(default=dict)
    auto_replies = models.JSONField(default=list)
    notifications = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "System Settings"

    def __str__(self):
        return "System Settings"


class Analytics(models.Model):
    """Analytics data cache"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    date = models.DateField(auto_now_add=True)
    total_cases = models.IntegerField(default=0)
    resolved_cases = models.IntegerField(default=0)
    pending_cases = models.IntegerField(default=0)
    average_response_time = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    category_counts = models.JSONField(default=dict)
    status_counts = models.JSONField(default=dict)
    daily_trends = models.JSONField(default=list)

    class Meta:
        ordering = ['-date']
        verbose_name_plural = "Analytics"

    def __str__(self):
        return f"Analytics for {self.date}"