/* Logs Table styles */
.logs-table {
    width: 100%;
    overflow-x: auto;
}

.logs-table .ant-table {
    min-width: 1150px; /* Match the scroll.x value */
}

.logs-table .ant-table-thead > tr > th {
    padding: 16px !important;
    font-weight: 600;
    background-color: #fafafa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.logs-table .ant-table-tbody > tr > td {
    padding: 16px !important;
    line-height: 1.5;
}

/* Fixed width columns */
.logs-table .ant-table-cell {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* For cells that should wrap */
.logs-table .ant-table-cell.wrap-content {
    white-space: normal;
    word-break: break-word;
}

/* Container styles */
.logs-table-container {
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Ensure the table header sticks when scrolling horizontally */
.logs-table .ant-table-header {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fafafa;
}

/* Add horizontal scroll indicator */
.logs-table-scroll-container {
    position: relative;
}

.logs-table-scroll-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 30px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.8));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
}

.logs-table-scroll-container:hover::after {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .logs-table .ant-table-tbody > tr > td,
    .logs-table .ant-table-thead > tr > th {
        padding: 12px 8px !important;
    }
}

/* Data Change Log Table specific styles */
.ant-tag {
    margin: 0;
    text-align: center;
    min-width: 100px;
}

.data-change-log-table .ant-table-cell {
    vertical-align: middle;
}

/* Model tag colors */
.model-tag-basestation {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.model-tag-transformer {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
}

.model-tag-inspection {
    background-color: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
}

.model-tag-lvfeeder {
    background-color: #f9f0ff;
    border-color: #d3adf7;
    color: #722ed1;
}
