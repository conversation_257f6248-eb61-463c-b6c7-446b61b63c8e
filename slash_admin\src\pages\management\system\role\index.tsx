import { <PERSON><PERSON>, <PERSON>, Popconfirm, Tag } from "antd";
import Table, { type ColumnsType } from "antd/es/table";
import { useEffect, useState } from "react";

import { ROLE_LIST } from "@/_mock/assets";
import { IconButton, Iconify } from "@/components/icon";

import { RoleModal, type RoleModalProps } from "./role-modal";

import type { Role } from "#/entity";
import { BasicStatus } from "#/enum";
import roleService from "@/api/services/roleService";
import { toast } from "sonner";

const ROLES: Role[] = ROLE_LIST as Role[];

const DEFAULE_ROLE_VALUE: Role = {
	id: "",
	name: "",
	label: "",
	status: BasicStatus.ENABLE,
	permission: [],
};
export default function RolePage() {
	const [roleModalPros, setRoleModalProps] = useState<RoleModalProps>({
		formValue: { ...DEFAULE_ROLE_VALUE },
		title: "New",
		show: false,
		onOk: () => {
			setRoleModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setRoleModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const [data, setData] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);
	const [permission, setPermission] = useState<any[]>([]);

	const fetchData = async () => {
		setLoading(true);
		try {
			const response = await roleService.getRole(); // Fetch data from the service
			console.log("API Response role:", response);

			if (response) {
				setData(response);
			}

			const response1 = await roleService.getpopulatedPermission(); // Fetch data from the service
			console.log("API Response peemisssion:", response);

			if (response) {
				setPermission(response1);
			}
		} catch (error) {
			console.error("Error fetching Role:", error);
			toast.error("Failed to load Role.");
		} finally {
			setLoading(false);
		}
	};

	// Fetch data when tableParams change
	useEffect(() => {
		fetchData();
	}, []);

	const handleDelete = async (id: string) => {
		try {
			await roleService.deleteRole(id); // Call the delete API
			toast.success("Role deleted successfully!");
			fetchData(); // Refresh the table data
		} catch (error) {
			toast.error("Failed to delete Role.");
		}
	};

	const columns: ColumnsType<Role> = [
		{
			title: "Name",
			dataIndex: "name",
			width: 300,
		},
		{
			title: "Label",
			dataIndex: "label",
		},
		{ title: "Order", dataIndex: "order", width: 60 },
		{
			title: "Status",
			dataIndex: "status",
			align: "center",
			width: 120,
			render: (status) => (
				<Tag color={status === BasicStatus.DISABLE ? "error" : "success"}>
					{status === BasicStatus.DISABLE ? "Disable" : "Enable"}
				</Tag>
			),
		},
		{ title: "Desc", dataIndex: "desc" },
		{
			title: "Action",
			key: "operation",
			align: "center",
			width: 100,
			// render: (_, record) => (
			// 	<div className="flex w-full justify-center text-gray">
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete the Role"
			// 			okText="Yes"
			// 			cancelText="No"
			// 			placement="left"
			// 			onConfirm={() => handleDelete(record.id)}
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</div>
			// ),
			render: (_, record) => (
							<div className="flex w-full justify-center text-gray gap-2">
								{/* <IconButton onClick={() => { push(`${pathname}/${record.id}`);}}>
									<Iconify icon="ix:view" size={18} />
								</IconButton> */}
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
									<Popconfirm
										title="Delete the Role"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDelete(record.id)}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</div>
						),
		},
	];

	const onCreate = () => {
		setRoleModalProps((prev) => ({
			...prev,
			show: true,
			title: "Create New",
			permission,
			formValue: {
				...prev.formValue,
				...DEFAULE_ROLE_VALUE,
			},
		}));
	};

	const onEdit = (formValue: Role) => {
		setRoleModalProps((prev) => ({
			...prev,
			show: true,
			title: "Edit",
			permission,
			formValue,
		}));
	};

	return (
		<Card
			title="Role List"
			extra={
				<Button type="primary" onClick={onCreate}>
					New
				</Button>
			}
		>
			<Table
				rowKey="id"
				size="small"
				scroll={{ x: "max-content" }}
				pagination={false}
				columns={columns}
				dataSource={data}
			/>

			<RoleModal {...roleModalPros} />
		</Card>
	);
}
