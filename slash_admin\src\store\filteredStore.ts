// import { create } from "zustand";

// // Define types for filters
// export type FilterType = {
//   region?: string;
//   csc?: string
// };

// type FilteredStore = {
//   filteredbaseStation: []
//   setFilteredbaseStation: (filteredbaseStation: any) => void,
// };

// // Zustand store for managing global state
// export const useFilteredStore = create<FilteredStore>(
//   (set) => ({
//     filteredbaseStation: [],
//     setFilteredbaseStation: (filteredbaseStation) => set({ filteredbaseStation }),
// }));

import { create } from "zustand";

// Define types for filters
export type FilterType = {
	region?: string;
	csc?: string;
};

// // Define a type for the base station object (example)
// type BaseStation = {
//   id: string;
//   name: string;
//   region: string;
//   csc: string;
// };

export type FilteredStore = {
	filteredbaseStation: [];
	setFilteredbaseStation: (filteredbaseStation: any) => void;
	searchType: string;
	setSearchType: (type: string) => void;
	missingBasestations: [];
	missingGPSStations: [];
	setMissingBasestations: (missingBasestations: any) => void;
	setMissingGPSStations: (missingGPSStations: any) => void;
	isLoading: boolean;
	setIsLoading: (isLoading: boolean) => void;
};

// Zustand store for managing global state
export const useFilteredStore = create<FilteredStore>((set) => ({
	filteredbaseStation: [],
	setFilteredbaseStation: (filteredbaseStation) => set({ filteredbaseStation }),
	searchType: "",
	setSearchType: (searchType) => set({ searchType }),
	missingBasestations: [],
	missingGPSStations: [],
	setMissingBasestations: (missingBasestations) => set({ missingBasestations }),
	setMissingGPSStations: (missingGPSStations) => set({ missingGPSStations }),
	isLoading: false,
	setIsLoading: (isLoading) => set({ isLoading }),
}));

