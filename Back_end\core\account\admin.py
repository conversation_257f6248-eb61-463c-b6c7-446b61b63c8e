from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, Role, Permission, Region, CSCCenter, Substation, Feeder, RegionData

# Register your models here.

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ['username', 'email', 'first_name', 'last_name', 'role', 'department', 'is_active']
    list_filter = ['role', 'department', 'is_active', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Info', {
            'fields': ('role', 'department', 'avatar', 'phone', 'full_name', 'region', 'csc', 'title', 'address', 'city', 'about')
        }),
    )

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'label', 'status', 'order']
    list_filter = ['status']
    search_fields = ['name', 'label']

@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ['name', 'label', 'type', 'status', 'order']
    list_filter = ['type', 'status']
    search_fields = ['name', 'label']

@admin.register(Region)
class RegionAdmin(admin.ModelAdmin):
    list_display = ['csc_code', 'name']
    search_fields = ['csc_code', 'name']

@admin.register(CSCCenter)
class CSCCenterAdmin(admin.ModelAdmin):
    list_display = ['csc_code', 'name', 'region']
    list_filter = ['region']
    search_fields = ['csc_code', 'name']

@admin.register(Substation)
class SubstationAdmin(admin.ModelAdmin):
    list_display = ['name', 'region']
    list_filter = ['region']
    search_fields = ['name']

@admin.register(Feeder)
class FeederAdmin(admin.ModelAdmin):
    list_display = ['feeder_name', 'voltage_level', 'substation', 'peak_load']
    list_filter = ['voltage_level', 'substation__region']
    search_fields = ['feeder_name']

@admin.register(RegionData)
class RegionDataAdmin(admin.ModelAdmin):
    list_display = ['region', 'last_updated']
    list_filter = ['last_updated']
    readonly_fields = ['last_updated']