import React, { useState } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon, ArrowDownTrayIcon, EyeIcon } from '@heroicons/react/24/outline';
import { Attachment } from '../../../../types';

interface AttachmentViewerProps {
  attachments: Attachment[];
}

export default function AttachmentViewer({ attachments }: AttachmentViewerProps) {
  const [selectedAttachment, setSelectedAttachment] = useState<Attachment | null>(null);

  if (attachments.length === 0) {
    return null;
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return '🖼️';
      case 'document':
        return '📄';
      default:
        return '📎';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDownload = (attachment: Attachment) => {
    // In a real app, this would trigger a download
    console.log('Downloading:', attachment.name);
  };

  const canPreview = (attachment: Attachment) => {
    return attachment.type === 'image' || attachment.name.toLowerCase().endsWith('.pdf');
  };

  return (
    <>
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Attachments</h4>
        <div className="grid grid-cols-1 gap-3">
          {attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">{getFileIcon(attachment.type)}</span>
                <div>
                  <p className="text-sm font-medium text-gray-900">{attachment.name}</p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(attachment.size)} • Uploaded by {attachment.uploadedBy.name}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {canPreview(attachment) && (
                  <button
                    onClick={() => setSelectedAttachment(attachment)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                    title="Preview"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                )}
                <button
                  onClick={() => handleDownload(attachment)}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  title="Download"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Preview Modal */}
      <Dialog
        open={selectedAttachment !== null}
        onClose={() => setSelectedAttachment(null)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/75" aria-hidden="true" />
        
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            {selectedAttachment && (
              <>
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                  <Dialog.Title className="text-lg font-medium text-gray-900">
                    {selectedAttachment.name}
                  </Dialog.Title>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleDownload(selectedAttachment)}
                      className="p-2 text-gray-400 hover:text-gray-600"
                      title="Download"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setSelectedAttachment(null)}
                      className="p-2 text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
                
                <div className="p-4 max-h-[70vh] overflow-auto">
                  {selectedAttachment.type === 'image' ? (
                    <img
                      src={selectedAttachment.url}
                      alt={selectedAttachment.name}
                      className="max-w-full h-auto mx-auto"
                    />
                  ) : selectedAttachment.name.toLowerCase().endsWith('.pdf') ? (
                    <iframe
                      src={selectedAttachment.url}
                      className="w-full h-96 border-0"
                      title={selectedAttachment.name}
                    />
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p>Preview not available for this file type</p>
                      <button
                        onClick={() => handleDownload(selectedAttachment)}
                        className="mt-4 px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700"
                      >
                        Download File
                      </button>
                    </div>
                  )}
                </div>
              </>
            )}
          </Dialog.Panel>
        </div>
      </Dialog>
    </>
  );
}