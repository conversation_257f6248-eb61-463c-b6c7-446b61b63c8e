import { useEffect, useState } from "react";
import { <PERSON>, <PERSON>, Typo<PERSON>, Spin, Button, Timeline, Empty, Badge, Card } from "antd";
import { Iconify } from "@/components/icon";
import dayjs from 'dayjs';
import { themeVars } from "@/theme/theme.css";
import BasestationMap from "../transformer/BasestationMap";
import transformerService from "@/api/services/transformerService";
import { useParams, useRouter } from "@/router/hooks";
import { formatDate } from "@fullcalendar/core/index.js";
import { toast } from "sonner";

interface TransformerMovement {
  id: number;
  timestamp: string;
  transformer_id: string;
  movement_type: 'moved_in' | 'moved_out' | 'feeder_change' | 'substation_change';
  old_location: string;
  new_location: string;
  changed_by: string;
}

interface CurrentTransformer {
  id: string;
  serial_number: string;
}

const ITEMS_PER_PAGE = 100;

export default function BasestationDetail() {
  const { push } = useRouter();
  const { station_code } = useParams();
  const [data, setData] = useState<Basestation | null>(null);
  const [loading, setLoading] = useState(false);
  const [showTimeline, setShowTimeline] = useState(false);
  const [changeLogs, setChangeLogs] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [timelineLoading, setTimelineLoading] = useState(false);
  const [currentTransformers, setCurrentTransformers] = useState<CurrentTransformer[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      if (!station_code) return;
      setLoading(true);
      try {
        const response = await transformerService.getBasestation(station_code);
        setData(response);
      } catch (error) {
        console.error("Error fetching basestation:", error);
        toast.error("Failed to load basestation details");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [station_code]);

  const fetchChangeLogs = async (page: number, append: boolean = false) => {
    setTimelineLoading(true);
    try {
      const response = await transformerService.getBasestationChangeLogs(station_code, page, ITEMS_PER_PAGE);
      
      setCurrentTransformers(response.current_transformers || []);
      
      const newLogs = response.movements?.results || [];
      const totalCount = response.movements?.count || 0;

      if (append) {
        setChangeLogs(prev => [...prev, ...newLogs]);
      } else {
        setChangeLogs(newLogs);
      }

      setHasMore(page * ITEMS_PER_PAGE < totalCount);
    } catch (error) {
      console.error("Error fetching change logs:", error);
      toast.error("Failed to load change logs");
      setChangeLogs([]);
      setCurrentTransformers([]);
    } finally {
      setTimelineLoading(false);
    }
  };

  const handleLoadMore = () => {
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    fetchChangeLogs(nextPage, true);
  };

  const handleTimelineToggle = () => {
    if (!showTimeline && changeLogs.length === 0) {
      setCurrentPage(1);
      fetchChangeLogs(1, false);
    }
    setShowTimeline(!showTimeline);
  };

  const basestationDetails = [
    { label: "Station Code", value: data?.station_code },
    { label: "Region", value: data?.region },
    { label: "CSC", value: data?.csc },
    { label: "Substation", value: data?.substation },
    { label: "Feeder", value: data?.feeder },
    { label: "Address", value: data?.address },
    { label: "GPS Location", value: data?.gps_location },
    { label: "Accuracy", value: data?.accuracy },
    { label: "Created At", value: data?.created_at ? dayjs(data.created_at).format("M/D/YYYY HH:mm") : '' },
    { label: "Updated At", value: data?.updated_at ? dayjs(data.updated_at).format("M/D/YYYY HH:mm") : '' },
    { label: "Created By", value: data?.created_by?.username },
    { label: "Updated By", value: data?.updated_by?.username },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Row gutter={[16, 16]}>
        <Col span={24} lg={12}>
          <Card className="flex-col">
            <div className="flex w-full flex-col">
              <Typography.Title level={2}>Basestation Details</Typography.Title>
              <div className="mt-2 flex flex-col gap-4">
                {basestationDetails.map((item, index) => (
                  <div className="flex" key={index}>
                    <div className="mr-2">{item.label}:</div>
                    <div className="opacity-50">{item.value || '-'}</div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>

        <Col span={24} xs={24} sm={24} md={24} lg={12}>
          <div className="flex justify-end mb-4">
            <Button 
              type="text"
              icon={<Iconify icon="material-symbols-light:move-outline-rounded" />}
              onClick={handleTimelineToggle}
            >
              Basestation History
            </Button>
          </div>
          <div className="flex items-center justify-center h-full">
            {showTimeline ? (
              <Card className="w-full flex-col">
                <Typography.Title level={4}>Basestation History</Typography.Title>

                {/* Current Transformers Section */}
                <div className="mb-6">
                  <Typography.Title level={5} className="mb-3">
                    Current Transformers
                  </Typography.Title>
                  {currentTransformers.length > 0 ? (
                    <div className="bg-primary/10 p-4 rounded-lg">
                      <div className="grid gap-3">
                        {currentTransformers.map((transformer) => (
                          <div 
                            key={transformer.id}
                            className="bg-white p-3 rounded-md shadow-sm flex items-center justify-between"
                          >
                            <div className="flex items-center gap-2">
                              <Iconify 
                                icon="mdi:transformer"
                                className="text-primary text-xl"
                              />
                              <div>
                                <p className="font-medium">Transformer ID: {transformer.id}</p>
                                <p className="text-sm text-gray-500">
                                  Serial Number: {transformer.serial_number}
                                </p>
                              </div>
                            </div>
                            <Button 
                              type="link"
                              onClick={() => push(`/transformer/${transformer.id}`)}
                              icon={<Iconify icon="material-symbols:arrow-forward" />}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Empty description="No transformers currently at this location" />
                  )}
                </div>

                {/* Historical Movements Timeline */}
                <Typography.Title level={5} className="mb-3">
                  Historical Movements
                </Typography.Title>
                {changeLogs.length > 0 ? (
                  <Timeline>
                    {changeLogs.map((log: TransformerMovement, index) => (
                      <Timeline.Item 
                        key={index}
                        dot={
                          <Badge 
                            status={
                              log.movement_type === 'moved_in' ? 'success' : 
                              log.movement_type === 'moved_out' ? 'warning' :
                              'processing'
                            }
                            style={{ 
                              backgroundColor: 
                                log.movement_type === 'moved_in' ? themeVars.colors.success :
                                log.movement_type === 'moved_out' ? themeVars.colors.warning :
                                themeVars.colors.primary
                            }}
                          />
                        }
                      >
                        <div className="bg-gray-50 p-2 rounded-lg mb-2">
                          <p className="font-medium text-primary">
                            {dayjs(log.timestamp).format("MMM DD, YYYY h:mm A")}
                          </p>
                          {log.movement_type === 'feeder_change' ? (
                            <p className="text-gray-800 font-medium flex items-center gap-1">
                              <span className="flex items-center gap-1 text-primary">
                                <Iconify icon="material-symbols:electric-bolt" className="text-primary" />
                                Feeder changed
                              </span>
                            </p>
                          ) : 
                            log.movement_type === 'substation_change' ? (
                              <p className="text-gray-800 font-medium flex items-center gap-1">
                                <span className="flex items-center gap-1 text-primary">
                                  <Iconify icon="material-symbols:subtitles" className="text-primary" />
                                  Substation changed
                                </span>
                              </p>
                            ) : 
                          (
                            <p className="text-gray-800 font-medium flex items-center gap-1">
                              Transformer 
                              <a onClick={() => push(`/transformer/${log.transformer_id.replace('TR-', '')}`)}>
                                {log.transformer_id.replace('TR-', '')} 
                              </a>

                              {log.movement_type === 'moved_in' ? (
                                <span className="flex items-center gap-1 text-success">
                                  <Iconify icon="material-symbols:input-rounded" className="text-success" />
                                  moved into
                                </span>
                              ) : (
                                <span className="flex items-center gap-1 text-warning">
                                  <Iconify icon="material-symbols:output-rounded" className="text-warning" />
                                  moved out of
                                </span>
                              )}
                              this location
                            </p>
                          )}
                          <p className="text-gray-600">Changed by: {log.changed_by}</p>
                          <p className="text-gray-600">Reason: {log.reason}</p>
                          <div className="mt-2">
                            {log.movement_type === 'feeder_change' ? (
                              <>
                                <p className="text-gray-500">Previous Feeder: {log.old_location}</p>
                                <p className="text-primary">New Feeder: {log.new_location}</p>
                              </>
                            ) : log.movement_type === 'moved_in' ? (
                              <>
                                <p className="text-gray-500">From: {log.old_location}</p>
                                <p className="text-success">To: {log.new_location}</p>
                              </>
                            ) : (
                              <>
                                <p className="text-gray-500">From: {log.old_location}</p>
                                <p className="text-warning">To: {log.new_location}</p>
                              </>
                            )}
                          </div>
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                ) : (
                  <Empty description="No historical movements found" />
                )}

                {!timelineLoading && hasMore && changeLogs.length > 0 && (
                  <div className="flex justify-center mt-4">
                    <Button 
                      onClick={handleLoadMore}
                      type="default"
                      icon={<Iconify icon="mdi:chevron-down" />}
                    >
                      Load More
                    </Button>
                  </div>
                )}
              </Card>
            ) : (
              <BasestationMap />
            )}
          </div>
        </Col>

        <Col span={24} lg={12}>
          {data?.gps_location && <BasestationMap gps_location={data.gps_location} />}
        </Col>
      </Row>
    </div>
  );
}

function BasestationSvg() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={312}
      height={312}
      viewBox="0 0 53 53"
      className=""
    >
      {/* Add your basestation SVG path here */}
      <path
        fill="#f8f9fe"
        d="M26.5 0L53 26.5 26.5 53 0 26.5z"
      />
    </svg>
  );
}
