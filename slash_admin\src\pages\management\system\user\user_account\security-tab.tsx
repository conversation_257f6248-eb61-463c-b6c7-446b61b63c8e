import { Button, Form, Input } from "antd";
import Card from "@/components/card";
import { toast } from "sonner";
import userService from "@/api/services/userService";
import type { UserInfo } from "#/entity";

type FieldType = {
	newPassword?: string;
	confirmPassword?: string;
};

interface SecurityTabProps {
	user: UserInfo;
	onUpdate: () => void;
}

export default function SecurityTab({ user, onUpdate }: SecurityTabProps) {
	const initFormValues = {
		newPassword: "",
		confirmPassword: "",
	};

	const [form] = Form.useForm<FieldType>();

	const onFinish = async ({ newPassword, confirmPassword }: FieldType) => {
		try {
			if (!newPassword || !confirmPassword) {
				toast.error("Please fill in all password fields");
				return;
			}

			if (newPassword !== confirmPassword) {
				toast.error("New passwords do not match");
				return;
			}

			const data = {
				new_password: newPassword,
			};

			await userService.adminResetPassword(user.id, data);

			toast.success("Password updated successfully");
			form.resetFields();
			onUpdate();
		} catch (error) {
			toast.error("Failed to update password");
			console.error("Password update failed:", error);
		}
	};

	return (
		<Card className="!h-auto flex-col">
			<Form
				form={form}
				layout="vertical"
				initialValues={initFormValues}
				labelCol={{ span: 8 }}
				className="w-full"
				onFinish={onFinish}
			>
				<Form.Item<FieldType> 
					label="New Password" 
					name="newPassword"
					rules={[
						{ required: true, message: 'Please input new password!' },
						{ min: 6, message: 'Password must be at least 6 characters!' }
					]}
				>
					<Input.Password />
				</Form.Item>

				<Form.Item<FieldType> 
					label="Confirm New Password" 
					name="confirmPassword"
					rules={[
						{ required: true, message: 'Please confirm new password!' },
						({ getFieldValue }) => ({
							validator(_, value) {
								if (!value || getFieldValue('newPassword') === value) {
									return Promise.resolve();
								}
								return Promise.reject(new Error('Passwords do not match!'));
							},
						}),
					]}
				>
					<Input.Password />
				</Form.Item>

				<div className="flex w-full justify-end">
					<Button type="primary" htmlType="submit">
						Reset Password
					</Button>
				</div>
			</Form>
		</Card>
	);
}

