from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class SmsAppConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'sms_app'

    def ready(self):
        """Called when Django starts up"""
        # Import here to avoid circular imports
        from django.conf import settings

        # Auto-start SMPP client if enabled in settings
        if getattr(settings, 'AUTO_START_SMPP', False):
            try:
                from .sms_handler import start_smpp_client
                logger.info("🚀 Auto-starting SMPP client...")
                start_smpp_client()
            except Exception as e:
                logger.error(f"❌ Failed to auto-start SMPP client: {e}")