import { faker } from "@faker-js/faker";
import { Button, Col, Form, Input, Row, Space, Switch } from "antd";

import Card from "@/components/card";
import { UploadAvatar } from "@/components/upload";
import userStore, { useUserInfo, useUserActions } from "@/store/userStore";
import { toast } from "sonner";
import userService from "@/api/services/userService";
import { StorageEnum } from "#/enum";

type FieldType = {
	name: string;
	email: string;
	phone: string;
	full_name?: string;
	region?: string;
	csc?: string;
	title?: string;
	address: string;
	city: string;
	about: string;
};
export default function GeneralTab() {
	const { id, avatar, username, email, phone,full_name, region, csc, title, address, city, about } = useUserInfo();
	const initFormValues = {
		name: username || "",
		email: email || "",
		phone: phone || "",
		full_name: full_name || "",
		region: region || "",
		csc: csc || "",
		title: title || "",
		address: address || "",
		city: city || "",
		about: about || "",
	};
	const [form] = Form.useForm<FieldType>();

	const onFinish = async (values: FieldType) => {
		try {
			const userData = {
				username: values.name,
				email: values.email,
				phone: values.phone,
				full_name: values.full_name,
				region: values.region,
				csc: values.csc,
				title: values.title,
				address: values.address,
				city: values.city,
				// postal_code: values.code,
				about: values.about,
			};

			console.log("userData", userData, "id", id);

			if (!id) {
				throw new Error("User ID is required");
			}
			await userService.updateUserProfile(id, userData);

			// Update the userStore with the new userInfo
			const updatedUserInfo = { ...userStore.getState().userInfo, ...userData };
			userStore.setState({ userInfo: updatedUserInfo });

			// Persist the updated userStore into localStorage
			const storageData = {
				[StorageEnum.UserInfo]: updatedUserInfo,
				[StorageEnum.UserToken]: userStore.getState().userToken,
			};
			localStorage.setItem("userStore", JSON.stringify(storageData));

			// Show success toast
			toast.success("Profile updated successfully!");
		} catch (error) {
			toast.error("Failed to update profile");
			console.error("Update failed:", error);
		}
	};

	return (
		<Row gutter={[16, 16]}>
			<Col span={24} lg={8}>
				<Card className="flex-col !px-6 !pb-10 !pt-20">
					<UploadAvatar defaultAvatar={avatar} />

					{/* <Space className="py-6">
						<div>Public Profile</div>
						<Switch size="small" />
					</Space> */}

					{/* <Button type="primary" danger>
						Delete User
					</Button> */}
				</Card>
			</Col>
			<Col span={24} lg={16}>
				<Card>
					<Form
						form={form}
						layout="vertical"
						initialValues={initFormValues}
						labelCol={{ span: 8 }}
						className="w-full"
						onFinish={onFinish}
					>
						<Row gutter={16}>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Username"
									name="name"
								>
									<Input disabled />
								</Form.Item>
							</Col>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Email"
									name="email"
								>
									<Input/>
								</Form.Item>
							</Col>
						</Row>

						<Row gutter={16}>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Full Name"
									name="full_name"
									rules={[{ required: true, message: "Please input your full name!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Title"
									name="title"
									rules={[{ required: true, message: "Please input your title!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
						</Row>

						<Row gutter={16}>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Region"
									name="region"
									rules={[{ required: true, message: "Please input your region!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
							<Col span={12}>
								<Form.Item<FieldType>
									label="CSC"
									name="csc"
									// rules={[{ required: true, message: "Please input your CSC!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
						</Row>

						<Row gutter={16}>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Phone"
									name="phone"
									rules={[{ required: true, message: "Please input your phone number!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
							<Col span={12}>
								<Form.Item<FieldType>
									label="Address"
									name="address"
									rules={[{ required: true, message: "Please input your address!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
						</Row>

						<Row gutter={16}>
							<Col span={12}>
								<Form.Item<FieldType>
									label="City"
									name="city"
									rules={[{ required: true, message: "Please input your city!" }]}
								>
									<Input />
								</Form.Item>
							</Col>
							{/* <Col span={12}>
								<Form.Item<FieldType> label="Code" name="code">
									<Input />
								</Form.Item>
							</Col> */}
						</Row>

						<Form.Item<FieldType> label="About" name="about">
							<Input.TextArea />
						</Form.Item>

						<div className="flex w-full justify-end">
							<Button type="primary" htmlType="submit">
								Save Changes
							</Button>
						</div>
					</Form>
				</Card>
			</Col>
		</Row>
	);
}
