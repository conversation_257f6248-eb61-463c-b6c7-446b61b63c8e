import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

// Define types for filters
export interface FilterType {
	ServiceType?: string;
	BodyCondition?: string;
	Arrester?: string;
	DropOutFuse?: string;
	FuseLink?: string;
	MvBushing?: string;
	LvBushing?: string;
	OilLevel?: string;
	InsulationLevel?: string;
	HornGap?: string;
	silica_gel?: string;
	HasLinkage?: string;
	ArresterBodyGround?: string;
	NeutralGround?: string;
	StatusOfMounting?: string;
	MountingCondition?: string;
	NLoadCurrent?: number;
	RSVoltage?: number;
	RTVoltage?: number;
	TSVoltage?: number;
	station_code?: string;
	region?: string;
	csc?: string;
}

type DashboardStore = {
	regionAndcsc: [];
	regions: [];
	isFiltersFullOpen: boolean;
	filters: FilterType;
	viewMode: "globe" | "list" | "grid";
	setRegionAndcsc: (regionAndcsc: any) => void;
	setRegions: (regions: any) => void;
	setIsFiltersFullOpen: (open: boolean) => void;
	setFilters: (filters: FilterType) => void;
	setViewMode: (mode: "globe" | "list" | "grid") => void;
	toggleFiltersFullOpen: () => void;
};

// Zustand store for managing global state
export const useDashboardStore = create<DashboardStore>((set) => ({
	regionAndcsc: [],
	regions: [],
	isFiltersFullOpen: false,
	filters: {
		searchType: "",

		Substation: "",
		Feeder: "",

		TransformerLocation: "",
		TransformerStatus: "",
		Capacity: "",
		PrimaryVoltage: "",
		CoolingType: "",
		Manufacturer: "",
		VectorGroup: "",
		ServiceType: "",
		YearOfManufacturing: [null, null],

		Arrester: "",
		DropOutFuse: "",
		FuseLink: "",
		MVBushing: "",
		MVCableLag: "",
		LVBushing: "",
		LVCableLag: "",

		// location: "",
		// beds: "",
		// baths: "",
		// propertyType: "",
		// amenities: [],
		// availableFrom: "",
		// priceRange: [],
		// squareFeet: [],
		// coordinates: []
	},
	viewMode: "globe",
	setRegionAndcsc: (regionAndcsc) => set({ regionAndcsc }),
	setRegions: (regions) => set({ regions }),
	setIsFiltersFullOpen: (open) => set({ isFiltersFullOpen: open }),
	setFilters: (filters) => set({ filters }),
	setViewMode: (mode) => set({ viewMode: mode }),
	toggleFiltersFullOpen: () => set((state) => ({ isFiltersFullOpen: !state.isFiltersFullOpen })),
}));

// // Dummy data for demonstration purposes
// export const DUMMY_FILTERS: FilterType = {
//   priceRange: [100000, 500000],
//   squareFeet: [null, 2000],
//   coordinates: [37.7749, -122.4194],
//   bedrooms: "3",
//   bathrooms: "2",
// };

// export const useDashboard = () => useDashboardStore((state) => state.dashboard);
// export const useDAshboardActions = () => useDashboardStore((state) => state.actions);

