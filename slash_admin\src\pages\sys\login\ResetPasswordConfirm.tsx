import React, { useEffect } from 'react';
import { Form, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { usePathname, useRouter, useSearchParams } from "@/router/hooks";
import userService from '@/api/services/userService';
import { toast } from 'sonner';
import { SvgIcon } from '@/components/icon';
import { ReturnButton } from './components/ReturnButton';
import { LoginStateEnum, useLoginStateContext } from './providers/LoginStateProvider';

function ResetPasswordConfirm() {
    const { push } = useRouter();
    const { t } = useTranslation();
    const { loginState, backToLogin, setLoginState } = useLoginStateContext();
    
    // Fix: Get URL search params directly
    const searchParams = new URLSearchParams(window.location.search);
    const uid = searchParams.get('uid');
    const token = searchParams.get('token');

    useEffect(() => {
        const pathname = window.location.pathname;
        if (pathname.includes('reset-password') && uid && token) {
            setLoginState(LoginStateEnum.RESET_PASSWORD_CONFIRM);
        }
    }, [setLoginState, uid, token]);

    if (loginState !== LoginStateEnum.RESET_PASSWORD_CONFIRM) return null;

    const handleReturn = () => {
        setLoginState(LoginStateEnum.LOGIN);
        push('/login');
    };

    const onFinish = async (values: any) => {
        try {
            if (values.new_password !== values.confirm_password) {
                toast.error("Passwords don't match");
                return;
            }

            if (!uid || !token) {
                toast.error("Invalid reset link");
                return;
            }

            const response = await userService.confirmResetPassword({
                uid,
                token,
                new_password: values.new_password,
            });

            toast.success("Password reset successfully");
            setLoginState(LoginStateEnum.LOGIN);
            push('/login');
        } catch (error) {
            console.error("Password reset error:", error);
            toast.error(error?.response?.data?.message || "Failed to reset password");
        }
    };

    return (
        <>
            <div className="mb-4 text-center text-2xl font-bold xl:text-3xl">
                Reset Password
            </div>
            <Form
                name="reset_password_form"
                size="large"
                onFinish={onFinish}
            >
                <Form.Item
                    name="new_password"
                    rules={[
                        { required: true, message: "Please input your new password" },
                        { min: 8, message: "Password must be at least 8 characters" }
                    ]}
                >
                    <Input.Password placeholder="New Password" />
                </Form.Item>

                <Form.Item
                    name="confirm_password"
                    rules={[
                        { required: true, message: "Please confirm your password" },
                        { min: 8, message: "Password must be at least 8 characters" }
                    ]}
                >
                    <Input.Password placeholder="Confirm Password" />
                </Form.Item>

                <Form.Item>
                    <Button type="primary" htmlType="submit" className="w-full !bg-black">
                        Reset Password
                    </Button>
                </Form.Item>
            </Form>

            <ReturnButton onClick={handleReturn} />
        </>
    );
}

export default ResetPasswordConfirm;

