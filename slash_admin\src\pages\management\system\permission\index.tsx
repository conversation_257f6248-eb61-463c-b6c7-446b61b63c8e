import { <PERSON><PERSON>, <PERSON>, Popconfirm, Tag } from "antd";
import Table, { type ColumnsType } from "antd/es/table";
import { isNil } from "ramda";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { IconButton, Iconify, SvgIcon } from "@/components/icon";
import { useUserPermission } from "@/store/userStore";

import PermissionModal, { type PermissionModalProps } from "./permission-modal";

import type { Permission } from "#/entity";
import { BasicStatus, PermissionType } from "#/enum";
import { toast } from "sonner";
import roleService from "@/api/services/roleService";

const defaultPermissionValue: Permission = {
	id: "",
	parentId: "",
	name: "",
	label: "",
	route: "",
	component: "",
	icon: "",
	hide: false,
	status: BasicStatus.ENABLE,
	type: PermissionType.CATALOGUE,
};
export default function PermissionPage() {
	// const permissions = useUserPermission();
	const { t } = useTranslation();

	const [data, setData] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);

	const [permissionModalProps, setPermissionModalProps] = useState<PermissionModalProps>({
		formValue: { ...defaultPermissionValue },
		title: "New",
		show: false,
		onOk: () => {
			setPermissionModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setPermissionModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	// const [data, setData] = useState<any[]>([]);
	// const [loading, setLoading] = useState(false);
	// const [permissions, setPermissions] = useState<any[]>([]);

	const fetchData = async () => {
		setLoading(true);
		try {
			const response = await roleService.getpopulatedPermission(); // Fetch data from the service
			console.log("API Response:", response);

			if (response) {
				setData(response);
			}
		} catch (error) {
			console.error("Error fetching Permission:", error);
			toast.error("Failed to load Permission.");
		} finally {
			setLoading(false);
		}
	};

	// Fetch data when tableParams change
	useEffect(() => {
		fetchData();
	}, []);

	const handleDelete = async (id: string) => {
		try {
			await roleService.deletePermission(id); // Call the delete API
			toast.success("Permission deleted successfully!");
			fetchData(); // Refresh the table data
		} catch (error) {
			toast.error("Failed to delete Permission.");
		}
	};

	const columns: ColumnsType<Permission> = [
		{
			title: "Name",
			dataIndex: "name",
			width: 300,
			render: (_, record) => <div>{t(record.label)}</div>,
		},
		{
			title: "Type",
			dataIndex: "type",
			width: 60,
			render: (_, record) => <Tag color="processing">{PermissionType[record.type]}</Tag>,
		},
		{
			title: "Icon",
			dataIndex: "icon",
			width: 60,
			render: (icon: string) => {
				if (isNil(icon)) return "";
				if (icon.startsWith("ic")) {
					return <SvgIcon icon={icon} size={18} className="ant-menu-item-icon" />;
				}
				return <Iconify icon={icon} size={18} className="ant-menu-item-icon" />;
			},
		},
		{
			title: "Component",
			dataIndex: "component",
		},
		{
			title: "Status",
			dataIndex: "status",
			align: "center",
			width: 120,
			render: (status) => (
				<Tag color={status === BasicStatus.DISABLE ? "error" : "success"}>
					{status === BasicStatus.DISABLE ? "Disable" : "Enable"}
				</Tag>
			),
		},
		{ title: "Order", dataIndex: "order", width: 60 },
		{
			title: "Action",
			key: "operation",
			align: "center",
			width: 100,
			// render: (_, record) => (
			// 	<div className="flex w-full justify-end text-gray">
			// 		{record?.type === PermissionType.CATALOGUE && (
			// 			<IconButton onClick={() => onCreate(record.id)}>
			// 				<Iconify icon="gridicons:add-outline" size={18} />
			// 			</IconButton>
			// 		)}
			// 		<IconButton onClick={() => onEdit(record)}>
			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
			// 		</IconButton>
			// 		<Popconfirm
			// 			title="Delete the Permission"
			// 			okText="Yes"
			// 			cancelText="No"
			// 			placement="left"
			// 			onConfirm={() => handleDelete(record.id)}
			// 		>
			// 			<IconButton>
			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
			// 			</IconButton>
			// 		</Popconfirm>
			// 	</div>
			// ),
			render: (_, record) => (
							<div className="flex w-full justify-center text-gray gap-2">
								{/* <IconButton onClick={() => { push(`${pathname}/${record.id}`);}}>
									<Iconify icon="ix:view" size={18} />
								</IconButton> */}
								{record?.type === PermissionType.CATALOGUE && (
						<IconButton onClick={() => onCreate(record.id)}>
							<Iconify icon="ix:addcircular" size={18} />
						</IconButton>
					)}
								<IconButton onClick={() => onEdit(record)}>
									<Iconify icon="ix:edit" size={18} />
								</IconButton>
									<Popconfirm
										title="Delete the Permission?"
										okText="Yes"
										cancelText="No"
										placement="left"
										onConfirm={() => handleDelete(record.id)}
										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
									>
										<IconButton>
											<Iconify icon="ix:delete" size={18} className="text-error" />
										</IconButton>
									</Popconfirm>
							</div>
						),
		},
	];

	const onCreate = (parentId?: string) => {
		setPermissionModalProps((prev) => ({
			...prev,
			show: true,
			...defaultPermissionValue,
			title: "New",
			permissions: data,
			formValue: { ...defaultPermissionValue, parentId: parentId ?? "" },
		}));
	};

	const onEdit = (formValue: Permission) => {
		setPermissionModalProps((prev) => ({
			...prev,
			show: true,
			title: "Edit",
			permissions: data,
			formValue,
		}));
	};
	return (
		<Card
			title="Permission List"
			extra={
				<Button type="primary" onClick={() => onCreate()}>
					New
				</Button>
			}
		>
			<Table
				rowKey="id"
				size="small"
				scroll={{ x: "max-content" }}
				pagination={false}
				columns={columns}
				dataSource={data}
			/>

			<PermissionModal {...permissionModalProps} />
		</Card>
	);
}
