import React, { useState } from 'react';
import { Table, Tabs } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { Column } from '@ant-design/plots';
import Scrollbar from '@/components/scrollbar';
import TransformerPieChart from './TransformerPieChart';

const { TabPane } = Tabs;

interface TableData {
  key: string;
  region: string;
  csc: string;
  // Transformer Types
  conservator: number;
  conservatorPercentage: number;
  hermetical: number;
  hermeticalPercentage: number;
  compact: number;
  compactPercentage: number;
  // Capacity
  kva10: number;
  kva10Percentage: number;
  kva25: number;
  kva25Percentage: number;
  kva50: number;
  kva50Percentage: number;
  // Cooling Type
  onan: number;
  onanPercentage: number;
  dryType: number;
  dryTypePercentage: number;
  // Manufacturer
  abbTanzania: number;
  abbTanzaniaPercentage: number;
  apex: number;
  apexPercentage: number;
  // Primary Voltage
  v11: number;
  v11Percentage: number;
  v33: number;
  v33Percentage: number;
  // Vector Group
  dyn11: number;
  dyn11Percentage: number;
  yyn0: number;
  yyn0Percentage: number;
  // Year of Manufacturing
  y2020: number;
  y2020Percentage: number;
  y2021: number;
  y2021Percentage: number;
  y2022: number;
  y2022Percentage: number;
}

interface CSCData extends TableData {
  key: string;
  csc: string;
}

interface RegionData extends TableData {
  key: string;
  region: string;
  children: CSCData[];
}

// Transformer Types Data
const transformerTypesData: RegionData[] =
 [
  {
    key: 'north',
    region: 'North',
    csc: '',
    conservator: 200,
    conservatorPercentage: 40,
    hermetical: 240,
    hermeticalPercentage: 48,
    compact: 60,
    compactPercentage: 12,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        conservator: 100,
        conservatorPercentage: 40,
        hermetical: 120,
        hermeticalPercentage: 48,
        compact: 30,
        compactPercentage: 12,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    conservator: 180,
    conservatorPercentage: 36,
    hermetical: 250,
    hermeticalPercentage: 50,
    compact: 70,
    compactPercentage: 14,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        conservator: 90,
        conservatorPercentage: 36,
        hermetical: 125,
        hermeticalPercentage: 50,
        compact: 35,
        compactPercentage: 14,
      }
    ]
  }
];

// Capacity Data
const capacityData: RegionData[] = [
  {
    key: 'north',
    region: 'North',
    csc: '',
    kva10: 100,
    kva10Percentage: 20,
    kva25: 200,
    kva25Percentage: 40,
    kva50: 200,
    kva50Percentage: 40,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        kva10: 50,
        kva10Percentage: 20,
        kva25: 100,
        kva25Percentage: 40,
        kva50: 100,
        kva50Percentage: 40,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    kva10: 120,
    kva10Percentage: 24,
    kva25: 180,
    kva25Percentage: 36,
    kva50: 200,
    kva50Percentage: 40,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        kva10: 60,
        kva10Percentage: 24,
        kva25: 90,
        kva25Percentage: 36,
        kva50: 100,
        kva50Percentage: 40,
      }
    ]
  }
];

// Cooling Type Data
const coolingTypeData: RegionData[] = [
  {
    key: 'north',
    region: 'North',
    csc: '',
    onan: 300,
    onanPercentage: 60,
    dryType: 200,
    dryTypePercentage: 40,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        onan: 150,
        onanPercentage: 60,
        dryType: 100,
        dryTypePercentage: 40,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    onan: 280,
    onanPercentage: 56,
    dryType: 220,
    dryTypePercentage: 44,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        onan: 140,
        onanPercentage: 56,
        dryType: 110,
        dryTypePercentage: 44,
      }
    ]
  }
];

// Manufacturer Data
const manufacturerData: RegionData[] = [
  {
    key: 'north',
    region: 'North',
    csc: '',
    abbTanzania: 240,
    abbTanzaniaPercentage: 48,
    apex: 260,
    apexPercentage: 52,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        abbTanzania: 120,
        abbTanzaniaPercentage: 48,
        apex: 130,
        apexPercentage: 52,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    abbTanzania: 230,
    abbTanzaniaPercentage: 46,
    apex: 270,
    apexPercentage: 54,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        abbTanzania: 115,
        abbTanzaniaPercentage: 46,
        apex: 135,
        apexPercentage: 54,
      }
    ]
  }
];

// Primary Voltage Data
const voltageData: RegionData[] = [
  {
    key: 'north',
    region: 'North',
    csc: '',
    v11: 300,
    v11Percentage: 60,
    v33: 200,
    v33Percentage: 40,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        v11: 150,
        v11Percentage: 60,
        v33: 100,
        v33Percentage: 40,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    v11: 280,
    v11Percentage: 56,
    v33: 220,
    v33Percentage: 44,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        v11: 140,
        v11Percentage: 56,
        v33: 110,
        v33Percentage: 44,
      }
    ]
  }
];

// Vector Group Data
const vectorGroupData: RegionData[] = [
  {
    key: 'north',
    region: 'North',
    csc: '',
    dyn11: 320,
    dyn11Percentage: 64,
    yyn0: 180,
    yyn0Percentage: 36,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        dyn11: 160,
        dyn11Percentage: 64,
        yyn0: 90,
        yyn0Percentage: 36,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    dyn11: 300,
    dyn11Percentage: 60,
    yyn0: 200,
    yyn0Percentage: 40,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        dyn11: 150,
        dyn11Percentage: 60,
        yyn0: 100,
        yyn0Percentage: 40,
      }
    ]
  }
];

// Year of Manufacturing Data
const manufacturingYearData: RegionData[] = [
  {
    key: 'north',
    region: 'North',
    csc: '',
    y2020: 160,
    y2020Percentage: 32,
    y2021: 200,
    y2021Percentage: 40,
    y2022: 140,
    y2022Percentage: 28,
    children: [
      {
        key: 'north-csc1',
        region: 'North',
        csc: 'CSC-1',
        y2020: 80,
        y2020Percentage: 32,
        y2021: 100,
        y2021Percentage: 40,
        y2022: 70,
        y2022Percentage: 28,
      }
    ]
  },
  {
    key: 'south',
    region: 'South',
    csc: '',
    y2020: 150,
    y2020Percentage: 30,
    y2021: 190,
    y2021Percentage: 38,
    y2022: 160,
    y2022Percentage: 32,
    children: [
      {
        key: 'south-csc1',
        region: 'South',
        csc: 'CSC-1',
        y2020: 75,
        y2020Percentage: 30,
        y2021: 95,
        y2021Percentage: 38,
        y2022: 80,
        y2022Percentage: 32,
      }
    ]
  }
];

// Add this new function to prepare data for graphs
const prepareGraphData = (tabKey: string, data: TableData[]) => {
  const filteredData = data.filter(item => item.key !== 'grand_total');
  
  switch (tabKey) {
    case 'transformer_types':
      return filteredData.flatMap(item => [
        { region: item.region, type: 'Conservator', value: item.conservator },
        { region: item.region, type: 'Hermetical', value: item.hermetical },
        { region: item.region, type: 'Compact', value: item.compact },
      ]);
    case 'capacity':
      return filteredData.flatMap(item => [
        { region: item.region, type: '10 kVA', value: item.kva10 },
        { region: item.region, type: '25 kVA', value: item.kva25 },
        { region: item.region, type: '50 kVA', value: item.kva50 },
      ]);
    case 'cooling_type':
      return filteredData.flatMap(item => [
        { region: item.region, type: 'ONAN', value: item.onan },
        { region: item.region, type: 'Dry Type', value: item.dryType },
      ]);
    case 'manufacturer':
      return filteredData.flatMap(item => [
        { region: item.region, type: 'ABB Tanzania', value: item.abbTanzania },
        { region: item.region, type: 'Apex', value: item.apex },
      ]);
    default:
      return [];
  }
};

// Add this new component for the graph
const TransformerGraph: React.FC<{ tabKey: string; data: TableData[] }> = ({ tabKey, data }) => {
  const graphData = prepareGraphData(tabKey, data);

  const config = {
    data: graphData,
    isGroup: true,
    xField: 'region',
    yField: 'value',
    seriesField: 'type',
    label: {
      position: 'middle',
      layout: [
        { type: 'interval-adjust-position' },
        { type: 'interval-hide-overlap' },
        { type: 'adjust-color' },
      ],
    },
    columnStyle: {
      radius: [4, 4, 0, 0],
    },
  };

  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold mb-4">Distribution Graph</h3>
      <Column {...config} height={400} width={400}/>
    </div>
  );
};

const TransformerAnalisis: React.FC = () => {
  const [activeTab, setActiveTab] = useState('transformer_types');

  // Function to get data based on tab key
  const getDataForTab = (tabKey: string) => {
    switch (tabKey) {
      case 'transformer_types':
        return transformerTypesData;
      case 'capacity':
        return capacityData;
      case 'cooling_type':
        return coolingTypeData;
      case 'manufacturer':
        return manufacturerData;
      case 'primary_voltage':
        return voltageData;
      case 'vector_group':
        return vectorGroupData;
      case 'manufacturing_year':
        return manufacturingYearData;
      default:
        return [];
    }
  };

  // Define columns for each tab
  const getColumns = (tabKey: string): ColumnsType<TableData> => {
    const baseColumns = [
      {
        title: 'Region',
        dataIndex: 'region',
        key: 'region',
        fixed: 'left',
      },
      {
        title: 'CSC',
        dataIndex: 'csc',
        key: 'csc',
        fixed: 'left',
      },
    ];

    const specificColumns = {
      transformer_types: [
        {
          title: 'Conservator',
          children: [
            {
              title: 'Count',
              dataIndex: 'conservator',
              key: 'conservator',
            },
            {
              title: '%',
              dataIndex: 'conservatorPercentage',
              key: 'conservatorPercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Hermetical',
          children: [
            {
              title: 'Count',
              dataIndex: 'hermetical',
              key: 'hermetical',
            },
            {
              title: '%',
              dataIndex: 'hermeticalPercentage',
              key: 'hermeticalPercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Compact',
          children: [
            {
              title: 'Count',
              dataIndex: 'compact',
              key: 'compact',
            },
            {
              title: '%',
              dataIndex: 'compactPercentage',
              key: 'compactPercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Total',
          children: [
            {
              title: 'Count',
              key: 'transformerTotal',
              render: (_, record) => record.conservator + record.hermetical + record.compact,
            },
            {
              title: '%',
              key: 'transformerTotalPercentage',
              render: () => '100%',
            },
          ],
        },
      ],
      capacity: [
        {
          title: '10 kVA',
          children: [
            {
              title: 'Count',
              dataIndex: 'kva10',
              key: 'kva10',
            },
            {
              title: '%',
              dataIndex: 'kva10Percentage',
              key: 'kva10Percentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: '25 kVA',
          children: [
            {
              title: 'Count',
              dataIndex: 'kva25',
              key: 'kva25',
            },
            {
              title: '%',
              dataIndex: 'kva25Percentage',
              key: 'kva25Percentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: '50 kVA',
          children: [
            {
              title: 'Count',
              dataIndex: 'kva50',
              key: 'kva50',
            },
            {
              title: '%',
              dataIndex: 'kva50Percentage',
              key: 'kva50Percentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Total',
          children: [
            {
              title: 'Count',
              key: 'capacityTotal',
              render: (_, record) => record.kva10 + record.kva25 + record.kva50,
            },
            {
              title: '%',
              key: 'capacityTotalPercentage',
              render: () => '100%',
            },
          ],
        },
      ],
      cooling_type: [
        {
          title: 'ONAN',
          children: [
            {
              title: 'Count',
              dataIndex: 'onan',
              key: 'onan',
            },
            {
              title: '%',
              dataIndex: 'onanPercentage',
              key: 'onanPercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Dry Type',
          children: [
            {
              title: 'Count',
              dataIndex: 'dryType',
              key: 'dryType',
            },
            {
              title: '%',
              dataIndex: 'dryTypePercentage',
              key: 'dryTypePercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Total',
          children: [
            {
              title: 'Count',
              key: 'coolingTotal',
              render: (_, record) => record.onan + record.dryType,
            },
            {
              title: '%',
              key: 'coolingTotalPercentage',
              render: () => '100%',
            },
          ],
        },
      ],
      manufacturer: [
        {
          title: 'ABB Tanzania',
          children: [
            {
              title: 'Count',
              dataIndex: 'abbTanzania',
              key: 'abbTanzania',
            },
            {
              title: '%',
              dataIndex: 'abbTanzaniaPercentage',
              key: 'abbTanzaniaPercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Apex',
          children: [
            {
              title: 'Count',
              dataIndex: 'apex',
              key: 'apex',
            },
            {
              title: '%',
              dataIndex: 'apexPercentage',
              key: 'apexPercentage',
              render: (value: number) => `${value}%`,
            },
          ],
        },
        {
          title: 'Total',
          children: [
            {
              title: 'Count',
              key: 'manufacturerTotal',
              render: (_, record) => record.abbTanzania + record.apex,
            },
            {
              title: '%',
              key: 'manufacturerTotalPercentage',
              render: () => '100%',
            },
          ],
        },
      ],
      primary_voltage: [
        {
          title: '11 kV',
          children: [
            { title: 'Count', dataIndex: 'v11', key: 'v11' },
            { title: '%', dataIndex: 'v11Percentage', key: 'v11Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: '33 kV',
          children: [
            { title: 'Count', dataIndex: 'v33', key: 'v33' },
            { title: '%', dataIndex: 'v33Percentage', key: 'v33Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: 'Total',
          children: [
            { title: 'Count', key: 'voltageTotal', render: (_, record) => record.v11 + record.v33 },
            { title: '%', key: 'voltageTotalPercentage', render: () => '100%' },
          ],
        },
      ],
      vector_group: [
        {
          title: 'Dyn11',
          children: [
            { title: 'Count', dataIndex: 'dyn11', key: 'dyn11' },
            { title: '%', dataIndex: 'dyn11Percentage', key: 'dyn11Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: 'Yyn0',
          children: [
            { title: 'Count', dataIndex: 'yyn0', key: 'yyn0' },
            { title: '%', dataIndex: 'yyn0Percentage', key: 'yyn0Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: 'Total',
          children: [
            { title: 'Count', key: 'vectorTotal', render: (_, record) => record.dyn11 + record.yyn0 },
            { title: '%', key: 'vectorTotalPercentage', render: () => '100%' },
          ],
        },
      ],
      manufacturing_year: [
        {
          title: '2020',
          children: [
            { title: 'Count', dataIndex: 'y2020', key: 'y2020' },
            { title: '%', dataIndex: 'y2020Percentage', key: 'y2020Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: '2021',
          children: [
            { title: 'Count', dataIndex: 'y2021', key: 'y2021' },
            { title: '%', dataIndex: 'y2021Percentage', key: 'y2021Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: '2022',
          children: [
            { title: 'Count', dataIndex: 'y2022', key: 'y2022' },
            { title: '%', dataIndex: 'y2022Percentage', key: 'y2022Percentage', render: (value: number) => `${value}%` },
          ],
        },
        {
          title: 'Total',
          children: [
            { title: 'Count', key: 'yearTotal', render: (_, record) => record.y2020 + record.y2021 + record.y2022 },
            { title: '%', key: 'yearTotalPercentage', render: () => '100%' },
          ],
        },
      ],
    };

    return [...baseColumns, ...(specificColumns[tabKey] || [])];
  };

  // Calculate summary data for each tab
  const getSummaryRow = (tabKey: string): TableData => {
    const summary = mockData.reduce((acc, curr) => {
      switch (tabKey) {
        case 'transformer_types':
          return {
            ...acc,
            conservator: (acc.conservator || 0) + curr.conservator,
            hermetical: (acc.hermetical || 0) + curr.hermetical,
            compact: (acc.compact || 0) + curr.compact,
          };
        case 'capacity':
          return {
            ...acc,
            kva10: (acc.kva10 || 0) + curr.kva10,
            kva25: (acc.kva25 || 0) + curr.kva25,
            kva50: (acc.kva50 || 0) + curr.kva50,
          };
        case 'cooling_type':
          return {
            ...acc,
            onan: (acc.onan || 0) + curr.onan,
            dryType: (acc.dryType || 0) + curr.dryType,
          };
        case 'manufacturer':
          return {
            ...acc,
            abbTanzania: (acc.abbTanzania || 0) + curr.abbTanzania,
            apex: (acc.apex || 0) + curr.apex,
          };
        default:
          return acc;
      }
    }, {} as Partial<TableData>);

    return {
      key: 'total',
      region: 'Total',
      csc: '',
      ...mockData[0], // Copy structure from first row
      ...summary, // Override with calculated sums
    } as TableData;
  };

  // Add this function to handle Grand Total row rendering
  const renderGrandTotalRow = (tabKey: string, data: TableData[]) => {
    // Only include region-level data (exclude CSC data)
    const regionData = data.filter((item: any) => !item.csc);

    // Calculate grand totals using only region data
    const grandTotal = regionData.reduce((acc, curr) => {
      switch (tabKey) {
        case 'transformer_types':
          return {
            conservator: (acc.conservator || 0) + (curr.conservator || 0),
            hermetical: (acc.hermetical || 0) + (curr.hermetical || 0),
            compact: (acc.compact || 0) + (curr.compact || 0),
          };
        case 'capacity':
          return {
            kva10: (acc.kva10 || 0) + (curr.kva10 || 0),
            kva25: (acc.kva25 || 0) + (curr.kva25 || 0),
            kva50: (acc.kva50 || 0) + (curr.kva50 || 0),
          };
        case 'cooling_type':
          return {
            onan: (acc.onan || 0) + (curr.onan || 0),
            dryType: (acc.dryType || 0) + (curr.dryType || 0),
          };
        case 'manufacturer':
          return {
            abbTanzania: (acc.abbTanzania || 0) + (curr.abbTanzania || 0),
            apex: (acc.apex || 0) + (curr.apex || 0),
          };
        case 'primary_voltage':
          return {
            v11: (acc.v11 || 0) + (curr.v11 || 0),
            v33: (acc.v33 || 0) + (curr.v33 || 0),
          };
        case 'vector_group':
          return {
            dyn11: (acc.dyn11 || 0) + (curr.dyn11 || 0),
            yyn0: (acc.yyn0 || 0) + (curr.yyn0 || 0),
          };
        case 'manufacturing_year':
          return {
            y2020: (acc.y2020 || 0) + (curr.y2020 || 0),
            y2021: (acc.y2021 || 0) + (curr.y2021 || 0),
            y2022: (acc.y2022 || 0) + (curr.y2022 || 0),
          };
        default:
          return acc;
      }
    }, {} as any);

    // Calculate percentages for grand totals
    const calculatePercentages = () => {
      switch (tabKey) {
        case 'transformer_types': {
          const total = grandTotal.conservator + grandTotal.hermetical + grandTotal.compact;
          return {
            conservatorPercentage: ((grandTotal.conservator / total) * 100).toFixed(1),
            hermeticalPercentage: ((grandTotal.hermetical / total) * 100).toFixed(1),
            compactPercentage: ((grandTotal.compact / total) * 100).toFixed(1),
          };
        }
        case 'capacity': {
          const total = grandTotal.kva10 + grandTotal.kva25 + grandTotal.kva50;
          return {
            kva10Percentage: ((grandTotal.kva10 / total) * 100).toFixed(1),
            kva25Percentage: ((grandTotal.kva25 / total) * 100).toFixed(1),
            kva50Percentage: ((grandTotal.kva50 / total) * 100).toFixed(1),
          };
        }
        case 'cooling_type': {
          const total = grandTotal.onan + grandTotal.dryType;
          return {
            onanPercentage: ((grandTotal.onan / total) * 100).toFixed(1),
            dryTypePercentage: ((grandTotal.dryType / total) * 100).toFixed(1),
          };
        }
        case 'manufacturer': {
          const total = grandTotal.abbTanzania + grandTotal.apex;
          return {
            abbTanzaniaPercentage: ((grandTotal.abbTanzania / total) * 100).toFixed(1),
            apexPercentage: ((grandTotal.apex / total) * 100).toFixed(1),
          };
        }
        case 'primary_voltage': {
          const total = grandTotal.v11 + grandTotal.v33;
          return {
            v11Percentage: ((grandTotal.v11 / total) * 100).toFixed(1),
            v33Percentage: ((grandTotal.v33 / total) * 100).toFixed(1),
          };
        }
        case 'vector_group': {
          const total = grandTotal.dyn11 + grandTotal.yyn0;
          return {
            dyn11Percentage: ((grandTotal.dyn11 / total) * 100).toFixed(1),
            yyn0Percentage: ((grandTotal.yyn0 / total) * 100).toFixed(1),
          };
        }
        case 'manufacturing_year': {
          const total = grandTotal.y2020 + grandTotal.y2021 + grandTotal.y2022;
          return {
            y2020Percentage: ((grandTotal.y2020 / total) * 100).toFixed(1),
            y2021Percentage: ((grandTotal.y2021 / total) * 100).toFixed(1),
            y2022Percentage: ((grandTotal.y2022 / total) * 100).toFixed(1),
          };
        }
        default:
          return {};
      }
    };

    const percentages = calculatePercentages();

    return (
      <Table.Summary.Row>
        <Table.Summary.Cell index={0}>Grand Total</Table.Summary.Cell>
        <Table.Summary.Cell index={1}></Table.Summary.Cell>
        {/* Add cells based on the tab */}
        {getGrandTotalCells(tabKey, grandTotal, percentages)}
      </Table.Summary.Row>
    );
  };

  // Modify the Table component in each TabPane
  const renderTable = (tabKey: string) => {
    const data = getDataForTab(tabKey);
    
    return (
      <Table
        columns={getColumns(tabKey)}
        dataSource={data}
        pagination={false}
        bordered
        size="middle"
        scroll={{ x: 'max-content' }}
        className="shadow-lg"
        summary={(currentData) => renderGrandTotalRow(tabKey, currentData as TableData[])}
      />
    );
  };

  // Modify the renderContent function to include both table and graph
  const renderContent = (tabKey: string) => {
    const data = getDataForTab(tabKey);
    
    return (
      <div>
        <Scrollbar>
          {renderTable(tabKey)}
        </Scrollbar>
        <div className="flex flex-wrap justify-center gap-4 mt-4">
          <TransformerGraph tabKey={tabKey} data={data} />
          <TransformerPieChart tabKey={tabKey} data={data} />
        </div>
      </div>
    );
  };

  return (
    <div className="w-full p-4">
      <h2 className="text-xl font-bold mb-4">Transformer Analysis</h2>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Transformer Types" key="transformer_types">
          {renderContent('transformer_types')}
        </TabPane>
        <TabPane tab="Capacity" key="capacity">
          {renderContent('capacity')}
        </TabPane>
        <TabPane tab="Cooling Type" key="cooling_type">
          {renderContent('cooling_type')}
        </TabPane>
        <TabPane tab="Manufacturer" key="manufacturer">
          {renderContent('manufacturer')}
        </TabPane>
        <TabPane tab="Primary Voltage" key="primary_voltage">
          {renderContent('primary_voltage')}
        </TabPane>
        <TabPane tab="Vector Group" key="vector_group">
          {renderContent('vector_group')}
        </TabPane>
        <TabPane tab="Year of Manufacturing" key="manufacturing_year">
          {renderContent('manufacturing_year')}
        </TabPane>
      </Tabs>
    </div>
  );
};

const getGrandTotalCells = (tabKey: string, grandTotal: any, percentages: any) => {
  const getRowTotal = () => {
    switch (tabKey) {
      case 'transformer_types':
        return grandTotal.conservator + grandTotal.hermetical + grandTotal.compact;
      case 'capacity':
        return grandTotal.kva10 + grandTotal.kva25 + grandTotal.kva50;
      case 'cooling_type':
        return grandTotal.onan + grandTotal.dryType;
      case 'manufacturer':
        return grandTotal.abbTanzania + grandTotal.apex;
      case 'primary_voltage':
        return grandTotal.v11 + grandTotal.v33;
      case 'vector_group':
        return grandTotal.dyn11 + grandTotal.yyn0;
      case 'manufacturing_year':
        return grandTotal.y2020 + grandTotal.y2021 + grandTotal.y2022;
      default:
        return 0;
    }
  };

  const rowTotal = getRowTotal();

  switch (tabKey) {
    case 'transformer_types':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.conservator}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.conservatorPercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.hermetical}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.hermeticalPercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{grandTotal.compact}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>{percentages.compactPercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={8}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={9}>100%</Table.Summary.Cell>
        </>
      );
    case 'capacity':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.kva10}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.kva10Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.kva25}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.kva25Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{grandTotal.kva50}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>{percentages.kva50Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={8}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={9}>100%</Table.Summary.Cell>
        </>
      );
    case 'cooling_type':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.onan}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.onanPercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.dryType}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.dryTypePercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
        </>
      );
    case 'manufacturer':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.abbTanzania}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.abbTanzaniaPercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.apex}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.apexPercentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
        </>
      );
    case 'primary_voltage':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.v11}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.v11Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.v33}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.v33Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
        </>
      );
    case 'vector_group':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.dyn11}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.dyn11Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.yyn0}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.yyn0Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>100%</Table.Summary.Cell>
        </>
      );
    case 'manufacturing_year':
      return (
        <>
          <Table.Summary.Cell index={2}>{grandTotal.y2020}</Table.Summary.Cell>
          <Table.Summary.Cell index={3}>{percentages.y2020Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={4}>{grandTotal.y2021}</Table.Summary.Cell>
          <Table.Summary.Cell index={5}>{percentages.y2021Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={6}>{grandTotal.y2022}</Table.Summary.Cell>
          <Table.Summary.Cell index={7}>{percentages.y2022Percentage}%</Table.Summary.Cell>
          <Table.Summary.Cell index={8}>{rowTotal}</Table.Summary.Cell>
          <Table.Summary.Cell index={9}>100%</Table.Summary.Cell>
        </>
      );
    default:
      return null;
  }
};

export default TransformerAnalisis
