import React, { useEffect, useState, useCallback, useMemo } from "react";
import { <PERSON><PERSON> } from "@/pages/components/ui/button";
import { cn } from "@/lib/utils";
import { Grid, List, Search, Globe } from "lucide-react";
import { Input } from "@/pages/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/pages/components/ui/select";
import orgService from "@/api/services/orgService";
import { useDashboardStore } from "@/store/dashboardStore";
import { debounce } from "lodash";
import transformerService from "@/api/services/transformerService";
import { useFilteredStore } from "@/store/filteredStore";

export interface FiltersState {
  region: string;
  csc: string;
}

// Constants
const DEBOUNCE_DELAY = 300; // ms

const FiltersBar: React.FC = () => {
  const {
    filters,
    isFiltersFullOpen,
    viewMode,
    setFilters,
    toggleFiltersFullOpen,
    setViewMode,
    regions,
    setRegions,
    regionAndcsc,
    setRegionAndcsc,
  } = useDashboardStore();

  const { setFilteredbaseStation, setSearchType } = useFilteredStore();

  const [searchInput, setSearchInput] = useState(filters.location || "");
  const [csc, setCSC] = useState<Array<{ key: string; name: string }>>([]);

  // Memoize the updateURL function to prevent recreation on each render
  const updateURL = useCallback(
    debounce(async (newFilters: FiltersState) => {
      try {
        setSearchType("");
        const response = await transformerService.getBasestationsFiltered(newFilters);
        if (response) {
          setFilteredbaseStation(response);
        }
      } catch (error) {
        console.error("Error fetching filtered basestations:", error);
      }
    }, DEBOUNCE_DELAY),
    [setFilteredbaseStation, setSearchType]
  );

  // Handle filter changes
  const handleFilterChange = useCallback(
    (key: keyof FiltersState, value: any) => {
      let newFilters;
      
      if (key === "region") {

		console.log("regionAndcsc", regionAndcsc,   value)

        // When region changes, reset CSC and update CSC dropdown options
        const filtercsc = regionAndcsc.find((region) => region.csc_code === value);

        setCSC(
          filtercsc?.csc_centers?.map((csc: any) => ({ 
            key: csc.csc_code, 
            name: csc.name 
          })) || []
        );
        newFilters = { ...filters, [key]: value, csc: "" };
      } else {
        newFilters = { ...filters, [key]: value };
      }

      setFilters(newFilters);
      updateURL(newFilters);
    },
    [filters, regionAndcsc, setFilters, updateURL]
  );

  // Handle location search
  const handleLocationSearch = useCallback(async () => {
    if (!searchInput.trim()) return;
    
    try {
      // Create filter object for basestation search
      const searchFilter = {
        searchType: "BaseStation",
        station_code: searchInput.trim()
      };

      const response = await transformerService.getBasestationsFiltered({
        searchType: "BaseStation",
        station_code: searchInput.trim()
      });
      
      if (response) {
        // Update filters with the search input
        setFilters((prev) => ({
          ...prev,
          station_code: searchInput.trim()
        }));

        // Update the map with search results
        setFilteredbaseStation(response);
        
        // Set search type to default (basestation view)
        setSearchType("");
      }
    } catch (err) {
      console.error("Error searching station code:", err);
    }
  }, [searchInput, setFilters, setFilteredbaseStation, setSearchType]);

  // Fetch regions when the component mounts
  useEffect(() => {
    const loadRegions = async () => {
      if (regions.length > 0) return;
      
      try {
        const data = await orgService.getOrgList();
        const formattedRegions = data.map((region: any) => ({ 
          key: region.csc_code, 
          name: region.name 
        }));
        
        setRegionAndcsc(data);
        setRegions(formattedRegions);
      } catch (error) {
        console.error("Error fetching regions:", error);
      }
    };
    
    loadRegions();
  }, [regions.length, setRegionAndcsc, setRegions]);

  // Memoize button classes to prevent recalculation on every render
  const allFiltersButtonClass = useMemo(() => 
    cn(
      "gap-2 rounded-xl border-primary-400 hover:bg-primary-500 hover:text-primary-100",
      isFiltersFullOpen && "bg-primary-700 text-primary-100"
    ), 
    [isFiltersFullOpen]
  );

  const viewModeClasses = useMemo(() => ({
    globe: cn(
      "px-3 py-1 rounded-none rounded-l-xl hover:bg-primary-600 hover:text-primary-50",
      viewMode === "globe" ? "bg-primary-700 text-primary-50" : ""
    ),
    list: cn(
      "px-3 py-1 rounded-none hover:bg-primary-600 hover:text-primary-50",
      viewMode === "list" ? "bg-primary-700 text-primary-50" : ""
    ),
    grid: cn(
      "px-3 py-1 rounded-none rounded-r-xl hover:bg-primary-600 hover:text-primary-50",
      viewMode === "grid" ? "bg-primary-700 text-primary-50" : ""
    )
  }), [viewMode]);

  return (
    <div className="flex justify-between items-center w-full py-5">
      <div className="flex items-center gap-4 p-2">
        {/* All Filters Button */}
        <Button
          variant="outline"
          className={allFiltersButtonClass}
          onClick={toggleFiltersFullOpen}
        >
          <span>All Filters</span>
        </Button>

        {/* Search Input */}
        <div className="flex items-center">
          <Input
            placeholder="Search Station Code"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="w-40 rounded-l-xl rounded-r-none border-primary-400 border-r-0"
            onKeyDown={(e) => e.key === 'Enter' && handleLocationSearch()}
          />
          <Button
            onClick={handleLocationSearch}
            className="rounded-r-xl rounded-l-none border-l-none border-primary-400 shadow-none border hover:bg-primary-700 hover:text-primary-50"
          >
            <Search className="w-4 h-4" />
          </Button>
        </div>

        {/* <div className="flex gap-1">
          <Select
            value={filters.region || ""}
            onValueChange={(value) => handleFilterChange("region", value)}
          >
            <SelectTrigger className="w-22 rounded-xl border-primary-400">
              <SelectValue placeholder="Regions" />
            </SelectTrigger>
            <SelectContent className="bg-gray-100 z-50">
              {regions.map((region) => (
                <SelectItem key={region.key} value={region.key}>
                  {region.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {csc.length > 0 && (
            <Select 
              value={filters.csc || ""} 
              onValueChange={(value) => handleFilterChange("csc", value)}
            >
              <SelectTrigger className="w-22 rounded-xl border-primary-400">
                <SelectValue placeholder="CSC" />
              </SelectTrigger>
              <SelectContent className="bg-gray-100 z-50">
                {csc.map((item) => (
                  <SelectItem key={item.key} value={item.key}>
                    {item.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div> */}
      </div>

      {/* View Mode Toggles */}
      {/* <div className="flex items-center gap-4 p-2">
        <div className="flex border rounded-xl">
          <Button
            variant="ghost"
            className={viewModeClasses.globe}
            onClick={() => setViewMode("globe")}
          >
            <Globe className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            className={viewModeClasses.list}
            onClick={() => setViewMode("list")}
          >
            <List className="w-5 h-5" />
          </Button>
          <Button
            variant="ghost"
            className={viewModeClasses.grid}
            onClick={() => setViewMode("grid")}
          >
            <Grid className="w-5 h-5" />
          </Button>
        </div>
      </div> */}
    </div>
  );
};

export default React.memo(FiltersBar);

