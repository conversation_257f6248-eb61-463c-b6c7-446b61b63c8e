import { Button, Form, Input } from "antd";

import Card from "@/components/card";
import { toast } from "sonner";
import { useUserActions } from "@/store/userStore";
import userService from "@/api/services/userService";

type FieldType = {
	oldPassword?: string;
	newPassword?: string;
	confirmPassword?: string;
};
export default function SecurityTab() {
	const initFormValues = {
		oldPassword: "",
		newPassword: "",
		confirmPassword: "",
	};

	const [form] = Form.useForm<FieldType>();

	const onFinish = async ({ oldPassword, newPassword, confirmPassword }: FieldType) => {
		try {
			if (!oldPassword || !newPassword || !confirmPassword) {
				toast.error("Please fill in all password fields");
				return;
			}

			if (newPassword !== confirmPassword) {
				toast.error("New passwords do not match");
				return;
			}

			const data = {
				old_password: oldPassword,
				new_password: newPassword,
			};

			console.log("data", data);

			await userService.updatePassword(data);

			toast.success("Password updated successfully");
			form.resetFields();
		} catch (error) {
			toast.error("Failed to update password");
			console.error("Password update failed:", error);
		}
	};

	return (
		<Card className="!h-auto flex-col">
			<Form
				form={form}
				layout="vertical"
				initialValues={initFormValues}
				labelCol={{ span: 8 }}
				className="w-full"
				onFinish={onFinish}
			>
				<Form.Item<FieldType> label="Old Password" name="oldPassword">
					<Input.Password />
				</Form.Item>

				<Form.Item<FieldType> label="New Password" name="newPassword">
					<Input.Password />
				</Form.Item>

				<Form.Item<FieldType> label="Confirm New Password" name="confirmPassword">
					<Input.Password />
				</Form.Item>

				<div className="flex w-full justify-end">
					<Button type="primary" htmlType="submit">
						Save Changes
					</Button>
				</div>
			</Form>
		</Card>
	);
}
