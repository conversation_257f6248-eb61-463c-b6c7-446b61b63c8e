import React from 'react';
import { Card, DatePicker, Input, Select, Space, Button } from 'antd';
import type { DatePickerProps } from 'antd';
import { SearchOutlined, ClearOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

interface LogFiltersProps {
    type: 'activity' | 'error' | 'changes';
    filters: any;
    onFilterChange: (filters: any) => void;
    onReset: () => void;
}

const LogFilters: React.FC<LogFiltersProps> = ({ type, filters, onFilterChange, onReset }) => {
    const handleDateRangeChange = (dates: any) => {
        onFilterChange({
            ...filters,
            start_date: dates?.[0]?.format('YYYY-MM-DD'),
            end_date: dates?.[1]?.format('YYYY-MM-DD'),
        });
    };

    const renderActivityFilters = () => (
        <Space wrap>
            <Input
                placeholder="User"
                value={filters.user}
                onChange={(e) => onFilterChange({ ...filters, user: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Select
                placeholder="Action"
                value={filters.action}
                onChange={(value) => onFilterChange({ ...filters, action: value })}
                style={{ width: 120 }}
                allowClear
                options={[
                    { value: 'CREATE', label: 'Create' },
                    { value: 'UPDATE', label: 'Update' },
                    { value: 'DELETE', label: 'Delete' },
                ]}
            />
            <Input
                placeholder="Model Name"
                value={filters.model_name}
                onChange={(e) => onFilterChange({ ...filters, model_name: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Input
                placeholder="Record ID"
                value={filters.record_id}
                onChange={(e) => onFilterChange({ ...filters, record_id: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
        </Space>
    );

    const renderErrorFilters = () => (
        <Space wrap>
            <Input
                placeholder="User"
                value={filters.user}
                onChange={(e) => onFilterChange({ ...filters, user: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Select
                placeholder="Level"
                value={filters.level}
                onChange={(value) => onFilterChange({ ...filters, level: value })}
                style={{ width: 120 }}
                allowClear
                options={[
                    { value: 'ERROR', label: 'Error' },
                    { value: 'WARNING', label: 'Warning' },
                    { value: 'INFO', label: 'Info' },
                    { value: 'CRITICAL', label: 'Critical' },
                ]}
            />
            <Input
                placeholder="Message"
                value={filters.message}
                onChange={(e) => onFilterChange({ ...filters, message: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Input
                placeholder="Record ID"
                value={filters.record_id}
                onChange={(e) => onFilterChange({ ...filters, record_id: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
        </Space>
    );

    const renderChangeFilters = () => (
        <Space wrap>
            <Input
                placeholder="Changed By"
                value={filters.changed_by}
                onChange={(e) => onFilterChange({ ...filters, changed_by: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Input
                placeholder="Model Name"
                value={filters.model_name}
                onChange={(e) => onFilterChange({ ...filters, model_name: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Input
                placeholder="Field Name"
                value={filters.field_name}
                onChange={(e) => onFilterChange({ ...filters, field_name: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
            <Input
                placeholder="Record ID"
                value={filters.record_id}
                onChange={(e) => onFilterChange({ ...filters, record_id: e.target.value })}
                style={{ width: 200 }}
                allowClear
            />
        </Space>
    );

    return (
        <Card className="mb-4">
            <div className="space-y-4">
                <Space wrap>
                    <RangePicker
                        onChange={handleDateRangeChange}
                        value={[
                            filters.start_date ? dayjs(filters.start_date) : null,
                            filters.end_date ? dayjs(filters.end_date) : null
                        ]}
                    />
                    {type === 'activity' && renderActivityFilters()}
                    {type === 'error' && renderErrorFilters()}
                    {type === 'changes' && renderChangeFilters()}
                </Space>
                <div className="flex justify-end space-x-2">
                    <Button 
                        icon={<SearchOutlined />}
                        type="primary"
                        onClick={() => onFilterChange(filters)}
                    >
                        Search
                    </Button>
                    <Button 
                        icon={<ClearOutlined />}
                        onClick={onReset}
                    >
                        Reset
                    </Button>
                </div>
            </div>
        </Card>
    );
};

export default LogFilters;

