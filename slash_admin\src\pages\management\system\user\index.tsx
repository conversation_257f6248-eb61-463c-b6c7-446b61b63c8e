// import { <PERSON><PERSON>, <PERSON>, Popconfirm, Tag } from "antd";
// import Table, { type ColumnsType } from "antd/es/table";

// import { USER_LIST } from "@/_mock/assets";
// import { IconButton, Iconify } from "@/components/icon";
// import { usePathname, useRouter } from "@/router/hooks";
// import { getFullAvatarUrl } from "@/utils/url";

// import type { UserInfo, Role } from "#/entity";
// import { BasicStatus } from "#/enum";
// import { toast } from "sonner";
// import roleService from "@/api/services/roleService";
// import { useEffect, useState } from "react";
// import { UserModal, UserModalProps } from "./user-modal";

// // const USERS: UserInfo[] = USER_LIST as UserInfo[];

// const DEFAULT_USER_VALUE: UserInfo = {
// 	id: "",
// 	username: "",
// 	email: "",
// 	is_active: true,
// 	avatar: "",
// 	// role: [],
// 	// permission: [],
// };

// export default function RolePage() {
// 	const { push } = useRouter();
// 	const pathname = usePathname();

// 	const [data, setData] = useState<any[]>([]);
// 	const [loading, setLoading] = useState(false);

// 	const [userModalProps, setUserModalProps] = useState<UserModalProps>({
// 		formValue: { ...DEFAULT_USER_VALUE },
// 		title: "New",
// 		show: false,
// 		onOk: () => {
// 			setUserModalProps((prev) => ({ ...prev, show: false }));
// 		},
// 		onCancel: () => {
// 			setUserModalProps((prev) => ({ ...prev, show: false }));
// 		},
// 	});

// 	const fetchData = async () => {
// 		setLoading(true);
// 		try {
// 			const response = await roleService.getUser(); // Fetch data from the service
// 			console.log("API Response role:", response);

// 			if (response) {
// 				setData(response);
// 			}
// 		} catch (error) {
// 			console.error("Error fetching Role:", error);
// 			toast.error("Failed to load Role.");
// 		} finally {
// 			setLoading(false);
// 		}
// 	};

// 	useEffect(() => {
// 		fetchData();
// 	}, []);

// 	const columns: ColumnsType<UserInfo> = [
// 		{
// 			title: "username",
// 			dataIndex: "username",
// 			width: 300,
// 			render: (_, record) => {
// 				return (
// 					<div className="flex">
// 						<img alt="" src={getFullAvatarUrl(record.avatar)} className="h-10 w-10 rounded-full" />
// 						<div className="ml-2 flex flex-col">
// 							<span className="text-sm">{record.username}</span>
// 							<span className="text-xs text-text-secondary">{record.full_name || record.email}</span>
// 						</div>
// 					</div>
// 				);
// 			},
// 		},
// 		{
// 			title: "Role",
// 			dataIndex: "role",
// 			align: "center",
// 			width: 120,
// 			render: (role: Role) => <Tag color="cyan">{role?.name}</Tag>,
// 		},
// 		{
// 			title: "Status",
// 			dataIndex: "is_active",
// 			align: "center",
// 			width: 120,
// 			render: (is_active) => <Tag color={is_active ? "success" : "error"}>{is_active ? "Enable" : "Disable"}</Tag>,
// 		},
// 		{
// 			title: "Action",
// 			key: "operation",
// 			align: "center",
// 			width: 100,
// 			// render: (_, record) => (
// 			// 	<div className="flex w-full justify-center text-gray-500">
// 			// 		<IconButton
// 			// 			onClick={() => {
// 			// 				push(`${pathname}/${record.id}`);
// 			// 			}}
// 			// 		>
// 			// 			<Iconify icon="mdi:card-account-details" size={18} />
// 			// 		</IconButton>
// 			// 		<IconButton onClick={() => onEdit(record)}>
// 			// 			<Iconify icon="solar:pen-bold-duotone" size={18} />
// 			// 		</IconButton>
// 			// 		<Popconfirm
// 			// 			title="Delete the User"
// 			// 			okText="Yes"
// 			// 			cancelText="No"
// 			// 			placement="left"
// 			// 			onConfirm={() => handleDelete(record.id)}
// 			// 		>
// 			// 			<IconButton>
// 			// 				<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
// 			// 			</IconButton>
// 			// 		</Popconfirm>
// 			// 	</div>
// 			// ),

// 			render: (_, record) => (
// 							<div className="flex w-full justify-center text-gray gap-2">
// 								<IconButton onClick={() => { push(`${pathname}/${record.id}`);}}>
// 									<Iconify icon="ix:view" size={18} />
// 								</IconButton>
// 								<IconButton onClick={() => onEdit(record)}>
// 									<Iconify icon="ix:edit" size={18} />
// 								</IconButton>
// 									<Popconfirm
// 										title="Delete the User"
// 										okText="Yes"
// 										cancelText="No"
// 										placement="left"
// 										onConfirm={() => handleDelete(record.id)}
// 										getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
// 									>
// 										<IconButton>
// 											<Iconify icon="ix:delete" size={18} className="text-error" />
// 										</IconButton>
// 									</Popconfirm>
// 							</div>
// 						),
// 		},
// 	];

// 	const onCreate = () => {
// 		setUserModalProps((prev) => ({
// 			...prev,
// 			show: true,
// 			title: "Create User",
// 			formValue: {
// 				...prev.formValue,
// 				...DEFAULT_USER_VALUE,
// 			},
// 		}));
// 	};

// 	const onEdit = (formValue: UserInfo) => {
// 		setUserModalProps((prev) => ({
// 			...prev,
// 			show: true,
// 			title: "Edit",
// 			formValue,
// 		}));
// 	};

// 	const handleDelete = async (id: string) => {
// 		try {
// 			await roleService.deleteUser(id); // Call the delete API
// 			toast.success("User deleted successfully!");
// 			fetchData(); // Refresh the table data
// 		} catch (error) {
// 			toast.error("Failed to delete User.");
// 		}
// 	};

// 	return (
// 		<Card
// 			title="User List"
// 			extra={
// 				<Button type="primary" onClick={onCreate}>
// 					New
// 				</Button>
// 			}
// 		>
// 			<Table
// 				rowKey="id"
// 				size="small"
// 				scroll={{ x: "max-content" }}
// 				pagination={false}
// 				columns={columns}
// 				dataSource={data}
// 			/>

// 			<UserModal {...userModalProps} />
// 		</Card>
// 	);
// }


import { Button, Card, Popconfirm, Tag, Form, Input, Select } from "antd";
import Table, { type ColumnsType } from "antd/es/table";

import { USER_LIST } from "@/_mock/assets";
import { IconButton, Iconify } from "@/components/icon";
import { usePathname, useRouter } from "@/router/hooks";
import { getFullAvatarUrl } from "@/utils/url";

import type { UserInfo, Role } from "#/entity";
import { BasicStatus } from "#/enum";
import { toast } from "sonner";
import roleService from "@/api/services/roleService";
import { useEffect, useState } from "react";
import { UserModal, UserModalProps } from "./user-modal";

// Mock user list — replace this with real data if needed
const USERS: UserInfo[] = USER_LIST as UserInfo[];

// Default empty user value for modal
const DEFAULT_USER_VALUE: UserInfo = {
  id: "",
  username: "",
  email: "",
  is_active: true,
  avatar: "",
};

export default function RolePage() {
  const { push } = useRouter();
  const pathname = usePathname();

  const [originalData, setOriginalData] = useState<UserInfo[]>([]);
  const [data, setData] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState(false);

  // Filter states (used only after Apply is clicked)
  const [filters, setFilters] = useState({
    username: undefined as string | undefined,
    email: undefined as string | undefined,
    role: undefined as string | undefined,
    status: undefined as boolean | undefined,
  });

  // Local filter input values (for UI display and editing)
  const [localFilters, setLocalFilters] = useState({ ...filters });

  const [userModalProps, setUserModalProps] = useState<UserModalProps>({
    formValue: { ...DEFAULT_USER_VALUE },
    title: "New",
    show: false,
    onOk: () => {
      setUserModalProps((prev) => ({ ...prev, show: false }));
    },
    onCancel: () => {
      setUserModalProps((prev) => ({ ...prev, show: false }));
    },
  });

  // Fetch data from API once and keep it in memory
  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await roleService.getUser(); // Replace with real API call
      console.log("API Response role:", response);

      const userList = response || USERS; // fallback to mock data
      setOriginalData(userList);
      setData(userList); // initial load
    } catch (error) {
      console.error("Error fetching Role:", error);
      toast.error("Failed to load Role.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Handle local filter changes (before Apply is clicked)
  const handleLocalFilterChange = (changedValues: any) => {
    setLocalFilters((prev) => ({
      ...prev,
      ...changedValues,
    }));
  };

  // Reset local filters
  const resetLocalFilters = () => {
    setLocalFilters({
      username: undefined,
      email: undefined,
      role: undefined,
      status: undefined,
    });
  };

  // Apply filters and update table
  const applyFilters = () => {
    setFilters({ ...localFilters });
  };

  // Clear all filters and reset view
  const resetFiltersAndApply = () => {
    const emptyFilters = {
      username: undefined,
      email: undefined,
      role: undefined,
      status: undefined,
    };
    setLocalFilters(emptyFilters);
    setFilters(emptyFilters);
  };

  // Apply filters to local data
  useEffect(() => {
    let filtered = [...originalData];

    // Username filter
    if (filters.username) {
      filtered = filtered.filter((user) =>
        user.username?.toLowerCase().includes(filters.username?.toLowerCase() || "")
      );
    }

    // Email filter
    if (filters.email) {
      filtered = filtered.filter((user) =>
        user.email?.toLowerCase().includes(filters.email?.toLowerCase() || "")
      );
    }

    // Role filter
    if (filters.role) {
      filtered = filtered.filter(
        (user) => user.role?.id === filters.role
      );
    }

    // Status filter
    if (filters.status !== undefined && filters.status !== null) {
      filtered = filtered.filter(
        (user) => user.is_active === filters.status
      );
    }

    setData(filtered);
  }, [filters, originalData]);

  // Define table columns
  const columns: ColumnsType<UserInfo> = [
    {
      title: "Username",
      dataIndex: "username",
      width: 300,
      render: (_, record) => (
        <div className="flex">
          <img alt="" src={getFullAvatarUrl(record.avatar)} className="h-10 w-10 rounded-full" />
          <div className="ml-2 flex flex-col">
            <span className="text-sm">{record.username}</span>
            <span className="text-xs text-text-secondary">{record.full_name || record.email}</span>
          </div>
        </div>
      ),
    },
    {
      title: "Role",
      dataIndex: "role",
      align: "center",
      width: 120,
      render: (role: Role) => <Tag color="cyan">{role?.name}</Tag>,
    },
    {
      title: "Status",
      dataIndex: "is_active",
      align: "center",
      width: 120,
      render: (is_active) => (
        <Tag color={is_active ? "success" : "error"}>{is_active ? "Enable" : "Disable"}</Tag>
      ),
    },
    {
      title: "Action",
      key: "operation",
      align: "center",
      width: 100,
      render: (_, record) => (
        <div className="flex w-full justify-center text-gray gap-2">
          <IconButton onClick={() => push(`${pathname}/${record.id}`)}>
            <Iconify icon="ix:view" size={18} />
          </IconButton>
          <IconButton onClick={() => onEdit(record)}>
            <Iconify icon="ix:edit" size={18} />
          </IconButton>
          <Popconfirm
            title="Delete the User"
            okText="Yes"
            cancelText="No"
            placement="left"
            onConfirm={() => handleDelete(record.id)}
            getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
          >
            <IconButton>
              <Iconify icon="ix:delete" size={18} className="text-error" />
            </IconButton>
          </Popconfirm>
        </div>
      ),
    },
  ];

  // Modal actions
  const onCreate = () => {
    setUserModalProps((prev) => ({
      ...prev,
      show: true,
      title: "Create User",
      formValue: {
        ...prev.formValue,
        ...DEFAULT_USER_VALUE,
      },
    }));
  };

  const onEdit = (formValue: UserInfo) => {
    setUserModalProps((prev) => ({
      ...prev,
      show: true,
      title: "Edit",
      formValue,
    }));
  };

  const handleDelete = async (id: string) => {
    try {
      await roleService.deleteUser(id);
      toast.success("User deleted successfully!");
      fetchData(); // Refresh the table data
    } catch (error) {
      toast.error("Failed to delete User.");
    }
  };

  return (
    <Card
      title="User List"
      extra={<Button type="primary" onClick={onCreate}>New</Button>}
    >
      {/* 🔍 Client-side Filter Form */}
      <Form layout="inline" onFinish={applyFilters} onValuesChange={handleLocalFilterChange} style={{ marginBottom: 16 }}>
        <Form.Item label="Username" name="username">
          <Input placeholder="Search by username" />
        </Form.Item>

        <Form.Item label="Email" name="email">
          <Input placeholder="Search by email" />
        </Form.Item>

        <Form.Item label="Role" name="role">
          <Select placeholder="Select role" allowClear style={{ width: 150 }}>
            <Select.Option value="admin">Admin</Select.Option>
            <Select.Option value="user">User</Select.Option>
            <Select.Option value="guest">Guest</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item label="Status" name="status">
          <Select placeholder="Select status" allowClear style={{ width: 120 }}>
            <Select.Option value={true}>Enable</Select.Option>
            <Select.Option value={false}>Disable</Select.Option>
          </Select>
        </Form.Item>

        <Button htmlType="button" onClick={resetFiltersAndApply}>
          Reset
        </Button>
        <Button type="primary" htmlType="submit" style={{ marginLeft: 8 }}>
          Apply
        </Button>
      </Form>

      {/* 📊 Table */}
      <Table
        rowKey="id"
        size="small"
        scroll={{ x: "max-content" }}
        pagination={false}
        loading={loading}
        columns={columns}
        dataSource={data}
      />

      <UserModal {...userModalProps} />
    </Card>
  );
}