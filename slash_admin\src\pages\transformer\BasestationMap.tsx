import React, { useEffect, useState, useRef } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import icon from "leaflet/dist/images/marker-icon.png";
import iconRetina from "leaflet/dist/images/marker-icon-2x.png";
import shadow from "leaflet/dist/images/marker-shadow.png";

// Fix default marker icon
const DefaultIcon = L.icon({
  iconUrl: icon,
  iconRetinaUrl: iconRetina,
  shadowUrl: shadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
});
L.<PERSON>.prototype.options.icon = DefaultIcon;

// Define the shape of the `data` prop (just the gps_location string)
interface Props {
	gps_location?: string; // GPS location in "latitude,longitude" format
}

const BasestationMap: React.FC<Props> = ({ gps_location }) => {
	const mapRef = useRef<L.Map | null>(null);
	const mapContainerRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		// Function to parse GPS location string into [latitude, longitude]
		const parseGPSLocation = (gpsString?: string): [number, number] | null => {
			if (!gpsString) return null;
			const [lat, lng] = gpsString.split(",").map(Number);
			return lat && lng ? [lat, lng] : null;
		};

		// Clean up existing map instance if it exists
		if (mapRef.current) {
			mapRef.current.remove();
			mapRef.current = null;
		}

		// Initialize the map only if container exists and GPS location is valid
		if (mapContainerRef.current && gps_location) {
			const gpsCoordinates = parseGPSLocation(gps_location);

			if (gpsCoordinates) {
				// Create new map instance with scroll zoom enabled
				mapRef.current = L.map(mapContainerRef.current, {
					scrollWheelZoom: true
				}).setView(gpsCoordinates, 6);

				// Create layer control
				const layerControl = L.control.layers({}, {}, { position: 'topright' });

				// Add street view layer
				const streetLayer = L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
					maxZoom: 19,
					attribution: ''
				});

				// Add satellite view layer
				const satelliteLayer = L.tileLayer("https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}", {
					maxZoom: 19,
					attribution: ''
				});

				// Add both layers to the layer control
				layerControl.addBaseLayer(streetLayer, "Street View");
				layerControl.addBaseLayer(satelliteLayer, "Satellite View");

				// Add street layer as default
				streetLayer.addTo(mapRef.current);

				// Add layer control to map
				layerControl.addTo(mapRef.current);

				// Add marker
				L.marker(gpsCoordinates).addTo(mapRef.current);
			}
		}

		// Cleanup function
		return () => {
			if (mapRef.current) {
				mapRef.current.remove();
				mapRef.current = null;
			}
		};
	}, [gps_location]); // Only re-run if gps_location changes

	return (
		<div 
			ref={mapContainerRef} 
			style={{ height: "300px", width: "100%" }}
			className="rounded-lg overflow-hidden"
		/>
	);
};

export default BasestationMap;




