import React from 'react';
import clsx from 'clsx';

interface PriorityBadgeProps {
  priority: 'low' | 'medium' | 'high' | 'critical';
}

const priorityConfig = {
  'low': {
    label: 'Low',
    className: 'bg-green-100 text-green-700'
  },
  'medium': {
    label: 'Medium',
    className: 'bg-yellow-100 text-yellow-700'
  },
  'high': {
    label: 'High',
    className: 'bg-orange-100 text-orange-700'
  },
  'critical': {
    label: 'Critical',
    className: 'bg-red-100 text-red-700'
  }
};

export default function PriorityBadge({ priority }: PriorityBadgeProps) {
  const config = priorityConfig[priority];
  
  return (
    <span className={clsx(
      'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
      config.className
    )}>
      {config.label}
    </span>
  );
}