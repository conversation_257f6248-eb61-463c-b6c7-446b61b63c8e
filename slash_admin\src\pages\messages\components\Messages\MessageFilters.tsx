import React from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface MessageFiltersProps {
  filters: {
    status: string;
    category: string;
    priority: string;
    search: string;
  };
  onFiltersChange: (filters: any) => void;
}

export default function MessageFilters({ filters, onFiltersChange }: MessageFiltersProps) {
  return (
    <div className="p-4 border-b border-gray-200 space-y-3">
      {/* Search */}
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search messages..."
          value={filters.search}
          onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
        />
      </div>

      {/* Filter Dropdowns */}
      <div className="grid grid-cols-1 gap-2">
        <select
          value={filters.status}
          onChange={(e) => onFiltersChange({ ...filters, status: e.target.value })}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
        >
          <option value="all">All Status</option>
          <option value="new">New</option>
          <option value="in-progress">In Progress</option>
          <option value="replied">Replied</option>
          <option value="closed">Closed</option>
        </select>

        <select
          value={filters.category}
          onChange={(e) => onFiltersChange({ ...filters, category: e.target.value })}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
        >
          <option value="all">All Categories</option>
          <option value="power-outage">Power Outage</option>
          <option value="billing">Billing</option>
          <option value="wire-cut">Wire Cut</option>
          <option value="fallen-pole">Fallen Pole</option>
          <option value="corruption">Corruption</option>
          <option value="general">General</option>
          <option value="other">Other</option>
        </select>

        <select
          value={filters.priority}
          onChange={(e) => onFiltersChange({ ...filters, priority: e.target.value })}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
        >
          <option value="all">All Priorities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
      </div>
    </div>
  );
}