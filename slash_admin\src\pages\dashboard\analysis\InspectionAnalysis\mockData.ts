export interface InspectionData {
    key: string;
    region: string;
    csc: string;
    good?: number;
    goodPercentage?: number;
    fair?: number;
    fairPercentage?: number;
    poor?: number;
    poorPercentage?: number;
    ok?: number;
    okPercentage?: number;
    notOk?: number;
    notOkPercentage?: number;
    yes?: number;
    yesPercentage?: number;
    no?: number;
    noPercentage?: number;
    available?: number;
    availablePercentage?: number;
    notAvailable?: number;
    notAvailablePercentage?: number;
    children?: InspectionData[];
}

export const INSPECTION_TABS = {
    body_condition: "Body Condition",
    arrester: "Arrester",
    horn_gap: "Horn Gap",
    has_linkage: "Has Linkage",
    neutral_ground: "Neutral Ground",
    arrester_body_ground: "Arrester Body Ground"
};

export const mockInspectionData: Record<string, InspectionData[]> = {
    body_condition: [
        {
            key: "1",
            region: "North",
            csc: "Total",
            good: 150,
            goodPercentage: 60,
            fair: 75,
            fairPercentage: 30,
            poor: 25,
            poorPercentage: 10,
            children: [
                {
                    key: "1-1",
                    region: "North",
                    csc: "CSC-1",
                    good: 80,
                    goodPercentage: 61.5,
                    fair: 40,
                    fairPercentage: 30.8,
                    poor: 10,
                    poorPercentage: 7.7
                },
                {
                    key: "1-2",
                    region: "North",
                    csc: "CSC-2",
                    good: 70,
                    goodPercentage: 58.3,
                    fair: 35,
                    fairPercentage: 29.2,
                    poor: 15,
                    poorPercentage: 12.5
                }
            ]
        },
        {
            key: "2",
            region: "South",
            csc: "Total",
            good: 180,
            goodPercentage: 64.3,
            fair: 80,
            fairPercentage: 28.6,
            poor: 20,
            poorPercentage: 7.1,
            children: [
                {
                    key: "2-1",
                    region: "South",
                    csc: "CSC-1",
                    good: 100,
                    goodPercentage: 66.7,
                    fair: 40,
                    fairPercentage: 26.7,
                    poor: 10,
                    poorPercentage: 6.6
                }
            ]
        }
    ],
    
    horn_gap: [
        {
            key: "1",
            region: "North",
            csc: "Total",
            good: 300,
            goodPercentage: 75,
            poor: 100,
            poorPercentage: 25,
            children: [
                {
                    key: "1-1",
                    region: "North",
                    csc: "CSC-1",
                    good: 150,
                    goodPercentage: 75,
                    poor: 50,
                    poorPercentage: 25
                }
            ]
        }
    ],

    has_linkage: [
        {
            key: "1",
            region: "North",
            csc: "Total",
            yes: 350,
            yesPercentage: 70,
            no: 150,
            noPercentage: 30,
            children: [
                {
                    key: "1-1",
                    region: "North",
                    csc: "CSC-1",
                    yes: 175,
                    yesPercentage: 70,
                    no: 75,
                    noPercentage: 30
                }
            ]
        }
    ],

    arrester_body_ground: [
        {
            key: "1",
            region: "North",
            csc: "Total",
            available: 400,
            availablePercentage: 80,
            notAvailable: 100,
            notAvailablePercentage: 20,
            children: [
                {
                    key: "1-1",
                    region: "North",
                    csc: "CSC-1",
                    available: 200,
                    availablePercentage: 80,
                    notAvailable: 50,
                    notAvailablePercentage: 20
                }
            ]
        }
    ]
};

export const fetchMockInspectionData = (inspectionType: string): Promise<InspectionData[]> => {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(mockInspectionData[inspectionType] || []);
        }, 500);
    });
};


