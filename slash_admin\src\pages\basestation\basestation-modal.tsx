import { Form, Input, Modal, Select } from "antd";
import { useEffect, useState } from "react";
import type { Basestation } from "#/entity"; // Import the Basestation type
import { useMutation } from "@tanstack/react-query";
import transformerService from "@/api/services/transformerService";
import { toast } from "sonner";
import { TreeSelect } from "antd";
import type { TreeSelectProps } from "antd";
import orgService from "@/api/services/orgService";

const STATION_TYPES = [
	{ value: 'Single Wooden Pole', label: 'Single Wooden Pole' },
	{ value: 'Single Concrete Pole', label: 'Single Concrete Pole' },
	{ value: 'Single Steel Pole', label: 'Single Steel Pole' },
	{ value: 'Double Wooden Pole', label: 'Double Wooden Pole' },
	{ value: 'Double Concrete Pole', label: 'Double Concrete Pole' },
	{ value: 'Double Steel Pole', label: 'Double Steel Pole' },
	{ value: 'Triple Wooden Pole', label: 'Triple Wooden Pole' },
	{ value: 'Triple Concrete Pole', label: 'Triple Concrete Pole' },
	{ value: 'Triple Steel Pole', label: 'Triple Steel Pole' },
	{ value: 'Quadruple Wooden Pole', label: 'Quadruple Wooden Pole' },
	{ value: 'Quadruple Concrete Pole', label: 'Quadruple Concrete Pole' },
	{ value: 'Quadruple Steel Pole', label: 'Quadruple Steel Pole' },
	{ value: 'Ground Seat Foundation Elevated', label: 'Ground Seat Foundation Elevated' },
	{ value: 'Ground Seat Foundation Ground Level', label: 'Ground Seat Foundation Ground Level' },
	{ value: 'Net Station', label: 'Net Station' }, 
];

// Define the props for the BasestationModal
export interface BasestationModalProps {
	title: string;
	show: boolean;
	formValue: Basestation;
	onOk: () => void;
	onCancel: () => void;
	onDataChange: (newData: Basestation, isEdit: boolean) => void;
};

const treeData = [
	{
		value: "parent 1",
		title: "parent 1",
		children: [
			{
				value: "parent 1-0",
				title: "parent 1-0",
				children: [
					{
						value: "leaf1",
						title: "leaf1",
					},
					{
						value: "leaf2",
						title: "leaf2",
					},
					{
						value: "leaf3",
						title: "leaf3",
					},
					{
						value: "leaf4",
						title: "leaf4",
					},
					{
						value: "leaf5",
						title: "leaf5",
					},
					{
						value: "leaf6",
						title: "leaf6",
					},
				],
			},
			{
				value: "parent 1-1",
				title: "parent 1-1",
				children: [
					{
						value: "leaf11",
						title: <b style={{ color: "#08c" }}>leaf11</b>,
					},
				],
			},
		],
	},
];

// BasestationModal component
export function BasestationModal({ title, show, formValue, onOk, onCancel, onDataChange }: BasestationModalProps) {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);
	
	// State for storing the data
	const [regions, setRegions] = useState<any[]>([]);
	const [selectedCSCs, setSelectedCSCs] = useState<any[]>([]);
	const [selectedSubstations, setSelectedSubstations] = useState<any[]>([]);
	const [selectedFeeders, setSelectedFeeders] = useState<any[]>([]);

	// Update form values when formValue changes
	useEffect(() => {
		if (formValue) {
			form.setFieldsValue(formValue);
		}
	}, [formValue, form]);

	// Load regions on component mount
	useEffect(() => {
		const loadRegions = async () => {
			try {
				const data = await orgService.getOrgList();
				setRegions(data);
			} catch (error) {
				console.error("Error fetching regions:", error);
			}
		};
		loadRegions();
	}, []);

	// Load initial data when editing
	useEffect(() => {
		if (title === "Edit Basestation" && formValue && regions.length > 0) {
			// Find the region that matches the formValue's region
			const selectedRegion = regions.find(region => region.name === formValue.region);
			if (selectedRegion) {
				// Set the CSCs and substations for the selected region
				setSelectedCSCs(selectedRegion.csc_centers);
				setSelectedSubstations(selectedRegion.substations);
				
				// Find the substation that matches the formValue's substation
				const substation = selectedRegion.substations.find(
					(sub: any) => sub.name === formValue.substation
				);
				if (substation) {
					// Set the feeders for the selected substation
					setSelectedFeeders(substation.feeders || []);
				}
			}
		}
	}, [title, formValue, regions]);

	// Reset form when modal is closed
	useEffect(() => {
		if (!show) {
			form.resetFields();
		}
	}, [show, form]);

	// Handle region selection
	const handleRegionChange = (regionName: string) => {
		const selectedRegion = regions.find(region => region.name === regionName);
		if (selectedRegion) {
			setSelectedCSCs(selectedRegion.csc_centers);
			setSelectedSubstations(selectedRegion.substations);
			// Clear CSC, substation and feeder selections
			form.setFieldsValue({ 
				csc: undefined,
				substation: undefined, 
				feeder: undefined 
			});
		}
	};

	// Handle substation selection
	const handleSubstationChange = (substationName: string) => {
		const selectedRegion = regions.find(region => 
			region.substations.some((sub: any) => sub.name === substationName)
		);
		
		if (selectedRegion) {
			const substation = selectedRegion.substations.find(
				(sub: any) => sub.name === substationName
			);
			if (substation) {
				setSelectedFeeders(substation.feeders || []);
				// Clear feeder selection
				form.setFieldsValue({ feeder: undefined });
			}
		}
	};

	const handleOk = () => {
		form
			.validateFields()
			.then(async (values: any) => {
				setLoading(true);

				try {
					if (title === "Create New Basestation") {
						const response = await basestationMutation.mutateAsync(values);
						toast.success("Base Station created successfully!");
						onDataChange(response, false); // Add new data to table
					} else if (title === "Edit Basestation") {
						const updatedValues = form.getFieldsValue();
						await transformerService.updateBasestation(formValue.station_code, updatedValues as Partial<Basestation>);
						onDataChange({ ...formValue, ...updatedValues }, true); // Update existing data in table
						toast.success("Base Station updated successfully");
					}

					onOk();
				} catch (error) {
					toast.error("Operation failed. Please try again.");
				} finally {
					setLoading(false);
				}
			});
	};

	const basestationMutation = useMutation({
		mutationFn: transformerService.createBasestation,
	});

	// const [value, setValue] = useState<string>();

	// const onChange = (newValue: string) => {
	//   setValue(newValue);
	// };

	// const onPopupScroll: TreeSelectProps['onPopupScroll'] = (e) => {
	//   console.log('onPopupScroll', e);
	// };

	return (
		<Modal title={title} open={show} onOk={handleOk} onCancel={onCancel} confirmLoading={loading}>
			<Form initialValues={formValue} form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} layout="horizontal">
				{/* Add reason field only for edit mode */}
				{/* Region Select */}
				<Form.Item name="region" label="Select Region" rules={[{ required: true, message: "Please select a region!" }]}>
					<Select
						placeholder="Select a region"
						onChange={handleRegionChange}
						showSearch
						optionFilterProp="children"
						disabled={title === "Edit Basestation"}
					>
						{regions.map(region => (
							<Select.Option key={region.csc_code} value={region.name}>
								{region.name}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* CSC Select */}
				<Form.Item name="csc" label="Select CSC" rules={[{ required: true, message: "Please select a CSC!" }]}>
					<Select
						placeholder="Select a CSC"
						showSearch
						optionFilterProp="children"
						disabled={title === "Edit Basestation"}
					>
						{selectedCSCs.map(csc => (
							<Select.Option key={csc.csc_code} value={csc.name}>
								{csc.name}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* Substation Select */}
				<Form.Item name="substation" label="Substation" rules={[{ required: true }]}>
					<Select
						placeholder="Select a substation"
						onChange={handleSubstationChange}
						showSearch
						optionFilterProp="children"
					>
						{selectedSubstations.map((sub: any) => (
							<Select.Option key={sub.id} value={sub.name}>
								{sub.name}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* Feeder Select */}
				<Form.Item name="feeder" label="Feeder" rules={[{ required: true }]}>
					<Select
						placeholder="Select a feeder"
						showSearch
						optionFilterProp="children"
					>
						{selectedFeeders.map((feeder: any) => (
							<Select.Option key={feeder.id} value={feeder.feeder_name}>
								{feeder.feeder_name}
							</Select.Option>
						))}
					</Select>
				</Form.Item>

				{/* Station Type */}
				<Form.Item 
					name="station_type" 
					label="Station Type" 
					rules={[{ required: true, message: "Please select a station type!" }]}
				>
					<Select
						placeholder="Select station type"
						showSearch
						optionFilterProp="children"
						options={STATION_TYPES}
					/>
				</Form.Item>

				{/* Address */}
				<Form.Item<Basestation> label="Address" name="address" required>
					<Input.TextArea />
				</Form.Item>

				{/* GPS Location */}
				<Form.Item<Basestation> label="GPS Location" name="gps_location" required>
					<Input />
				</Form.Item>

				{title === "Edit Basestation" && (
					<Form.Item
						label="Reason for Update"
						name="reason"
						rules={[
							{ 
								required: true, 
								message: "Please provide a reason for the update" 
							},
							{
								min: 10,
								message: "Reason must be at least 10 characters long"
							}
						]}
					>
						<Input.TextArea 
							placeholder="Please provide a detailed reason for this update"
							rows={4}
						/>
					</Form.Item>
				)}
			</Form>
		</Modal>
	);
}







