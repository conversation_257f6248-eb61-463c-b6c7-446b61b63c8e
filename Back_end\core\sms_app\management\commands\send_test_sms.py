"""
Django management command to send test SMS.
Usage: python manage.py send_test_sms
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from sms_app.models import SMSMessage
from sms_app.tasks import send_sms


class Command(BaseCommand):
    help = 'Send a test SMS message via SMPP'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--phone',
            type=str,
            help='Phone number to send SMS to (e.g., 251912345678)',
        )
        parser.add_argument(
            '--message',
            type=str,
            help='Message content to send',
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Send as async Celery task (requires Celery worker)',
        )
    
    def handle(self, *args, **options):
        # Get phone number
        phone_number = options.get('phone')
        if not phone_number:
            phone_number = input("📱 Enter phone number (e.g., 251912345678): ").strip()
        
        if not phone_number:
            self.stdout.write(
                self.style.ERROR("❌ Phone number is required")
            )
            return
        
        # Get message content
        message_content = options.get('message')
        if not message_content:
            message_content = input("💬 Enter message content: ").strip()
        
        if not message_content:
            message_content = "Hello! This is a test SMS from the Django SMS app."
        
        # Check configuration
        smpp_config = getattr(settings, 'SMPP_CONFIG', {})
        if not smpp_config:
            self.stdout.write(
                self.style.ERROR("❌ SMPP_CONFIG not found in settings")
            )
            return
        
        self.stdout.write("📤 Creating SMS message...")
        self.stdout.write(f"📱 To: {phone_number}")
        self.stdout.write(f"💬 Message: {message_content}")
        
        try:
            # Create SMS message in database
            sms_message = SMSMessage.objects.create(
                phone_number=phone_number,
                content=message_content,
                status='pending',
                category='general',
                priority='medium'
            )
            
            self.stdout.write(f"✅ SMS message created with ID: {sms_message.id}")
            
            if options['async']:
                self.send_async(sms_message)
            else:
                self.send_sync(sms_message)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to create SMS message: {e}")
            )
    
    def send_async(self, sms_message):
        """Send SMS as async Celery task"""
        self.stdout.write("🚀 Sending SMS as async Celery task...")
        
        try:
            # Send the SMS task
            result = send_sms.delay(str(sms_message.id))
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ SMS task queued with ID: {result.id}")
            )
            self.stdout.write("ℹ️ Task is running in background via Celery worker")
            self.stdout.write("ℹ️ Make sure Celery worker is running: celery -A core worker -l info")
            self.stdout.write(f"ℹ️ Check SMS status in database: SMSMessage ID {sms_message.id}")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to queue SMS task: {e}")
            )
    
    def send_sync(self, sms_message):
        """Send SMS synchronously"""
        self.stdout.write("🚀 Sending SMS synchronously...")
        
        try:
            # Send the SMS task synchronously
            result = send_sms(str(sms_message.id))
            
            self.stdout.write(
                self.style.SUCCESS(f"✅ SMS sent successfully: {result}")
            )
            
            # Refresh from database to get updated status
            sms_message.refresh_from_db()
            self.stdout.write(f"📊 Final status: {sms_message.status}")
            if sms_message.message_id:
                self.stdout.write(f"🆔 Message ID: {sms_message.message_id}")
            if sms_message.sent_at:
                self.stdout.write(f"🕐 Sent at: {sms_message.sent_at}")
            if sms_message.error_message:
                self.stdout.write(f"❌ Error: {sms_message.error_message}")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Failed to send SMS: {e}")
            )
            
            # Check if message status was updated
            sms_message.refresh_from_db()
            if sms_message.error_message:
                self.stdout.write(f"💾 Error saved to database: {sms_message.error_message}")
