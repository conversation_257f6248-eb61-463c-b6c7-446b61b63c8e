import React from 'react';
import { Table, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import type { ErrorLog } from '@/api/services/logService';

interface ErrorLogsProps {
    loading: boolean;
    logs: ErrorLog[];
}

const ErrorLogs: React.FC<ErrorLogsProps> = ({ loading, logs }) => {
    const errorColumns = [
        {
            title: 'Timestamp',
            dataIndex: 'timestamp',
            render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: 'Level',
            dataIndex: 'level',
            render: (level: string) => (
                <Tag color={
                    level === 'ERROR' ? 'red' :
                    level === 'WARNING' ? 'orange' :
                    level === 'CRITICAL' ? 'purple' :
                    'blue'
                }>
                    {level}
                </Tag>
            ),
        },
        {
            title: 'Message',
            dataIndex: 'message',
            render: (message: string) => (
                <Typography.Text ellipsis={{ tooltip: message }}>
                    {message}
                </Typography.Text>
            ),
        },
        {
            title: 'User',
            dataIndex: 'user',
        },
        {
            title: 'IP Address',
            dataIndex: 'ip_address',
        }
    ];

    return (
        <Table
            columns={errorColumns}
            dataSource={logs}
            loading={loading}
            rowKey="id"
            pagination={{ pageSize: 10 }}
        />
    );
};

export default ErrorLogs;


