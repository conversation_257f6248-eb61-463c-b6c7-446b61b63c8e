import { useState, useEffect } from 'react';
import { Notification } from '../types';
import { useAuth } from '../contexts/AuthContext';

const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'assignment',
    title: 'New Assignment',
    message: 'You have been assigned to case CASE-2024-001',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    userId: '3',
    caseId: 'CASE-2024-001'
  },
  {
    id: '2',
    type: 'message',
    title: 'New Message',
    message: 'New SMS received from +1 (555) 123-4567',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
    userId: '2'
  },
  {
    id: '3',
    type: 'report',
    title: 'Progress Report',
    message: '<PERSON> submitted a progress report',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: true,
    userId: '1',
    caseId: 'CASE-2024-002'
  }
];

export function useNotifications() {
  const { currentUser } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>(
    mockNotifications.filter(n => n.userId === currentUser?.id)
  );

  useEffect(() => {
    // Listen for new notifications
    const handleNewNotification = (event: CustomEvent) => {
      const newNotification: Notification = {
        id: Date.now().toString(),
        ...event.detail,
        timestamp: new Date(),
        read: false,
        userId: currentUser?.id || ''
      };
      setNotifications(prev => [newNotification, ...prev]);
    };

    window.addEventListener('newNotification', handleNewNotification as EventListener);
    return () => window.removeEventListener('newNotification', handleNewNotification as EventListener);
  }, [currentUser]);

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead
  };
}