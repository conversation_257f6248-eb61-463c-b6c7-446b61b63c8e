import apiClient from "../apiClient";

import type { UserInfo, UserToken } from "#/entity";

export interface SignInReq {
	username: string;
	password: string;
}

export interface SignUpReq extends SignInReq {
	email: string;
}
export type SignInRes = UserToken & { user: UserInfo };

export enum RoleApi {
	createRole = "/auth/roles/",
	getRole = "/auth/roles/",
	updateRole = "/auth/roles/:id/",
	deleteRole = "/auth/roles/:id/",
}

export enum PermissionApi {
	createPermission = "/auth/permissions/",
	updatePermission = "/auth/permissions/:id/",
	getPermission = "/auth/permissions/",
	getpopulatedPermission = "/auth/populatedPermissions/",

	deletePermission = "/auth/permissions/:id/",
}

export enum UserApi {
	createUser = "/auth/users/",
	getUser = "/auth/users/",
	updateUser = "/auth/users/:id/",
	deleteUser = "/auth/users/:id/",
}

const createPermission = (data: any) => {
	console.log("data api", data);
	apiClient.post<any>({ url: PermissionApi.createPermission, data });
};
const getPermission = async () => apiClient.get<any>({ url: PermissionApi.getPermission });

const getpopulatedPermission = async () => {
	const res = await apiClient.get<any>({ url: PermissionApi.getpopulatedPermission });
	return res;
};

const updatePermission = async (id: string, data: Partial<any>): Promise<void> => {
	try {
		await apiClient.patch<any>({
			url: PermissionApi.updatePermission.replace(":id", id), // Replace :id with actual ID
			data, // Pass the updated data payload
		});
	} catch (error) {
		throw new Error("Failed to update basestation");
	}
};

const deletePermission = async (id: any): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: PermissionApi.deletePermission.replace(":id", id), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete basestation");
	}
};

const createRole = (data: any) => {
	apiClient.post<any>({ url: RoleApi.createRole, data });
};
const getRole = async () => apiClient.get<any>({ url: RoleApi.getRole });

const updateRole = async (id: string, data: Partial<any>): Promise<void> => {
	try {
		await apiClient.patch<any>({
			url: RoleApi.updateRole.replace(":id", id), // Replace :id with actual ID
			data, // Pass the updated data payload
		});
	} catch (error) {
		throw new Error("Failed to update basestation");
	}
};

const deleteRole = async (id: any): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: RoleApi.deleteRole.replace(":id", id), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete basestation");
	}
};

const createUser = (data: any) => {
	apiClient.post<any>({ url: UserApi.createUser, data });
};
const getUser = async () => apiClient.get<any>({ url: UserApi.getUser });

const updateUser = async (id: string, data: Partial<any>): Promise<void> => {
	try {
		await apiClient.patch<any>({
			url: UserApi.updateUser.replace(":id", id), // Replace :id with actual ID
			data, // Pass the updated data payload
		});
	} catch (error) {
		throw new Error("Failed to update basestation");
	}
};

const deleteUser = async (id: any): Promise<void> => {
	try {
		await apiClient.delete<any>({
			url: UserApi.deleteUser.replace(":id", id), // Replace :id with actual ID
		});
	} catch (error) {
		throw new Error("Failed nnn to delete basestation");
	}
};

export default {
	createPermission,
	updatePermission,
	getPermission,
	getpopulatedPermission,
	deletePermission,

	createRole,
	updateRole,
	getRole,
	deleteRole,

	createUser,
	updateUser,
	getUser,
	deleteUser,
};
