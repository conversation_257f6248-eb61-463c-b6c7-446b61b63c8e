#!/usr/bin/env python
"""
Test script to verify Redis fallback configuration is working.
This script tests if the cache system works with or without <PERSON><PERSON>.
"""

import os
import sys

# Add the core directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')

import django
django.setup()

from django.core.cache import cache
from django.conf import settings

def test_cache_configuration():
    """Test the cache configuration and fallback mechanism."""
    print("🧪 Testing Cache Configuration")
    print("=" * 50)
    
    # Print current cache backend
    cache_backend = settings.CACHES['default']['BACKEND']
    print(f"📋 Current cache backend: {cache_backend}")
    
    if 'redis' in cache_backend.lower():
        print("✅ Using Redis cache backend")
    else:
        print("⚠️  Using fallback cache backend (local memory)")
    
    # Test cache operations
    print("\n🔧 Testing cache operations...")
    
    try:
        # Test cache set
        test_key = 'redis_fallback_test'
        test_value = {'message': 'Cache is working!', 'timestamp': '2025-07-28'}
        
        cache.set(test_key, test_value, timeout=60)
        print("✅ Cache SET operation successful")
        
        # Test cache get
        retrieved_value = cache.get(test_key)
        if retrieved_value == test_value:
            print("✅ Cache GET operation successful")
            print(f"   Retrieved: {retrieved_value}")
        else:
            print("❌ Cache GET operation failed")
            print(f"   Expected: {test_value}")
            print(f"   Got: {retrieved_value}")
        
        # Test cache delete
        cache.delete(test_key)
        deleted_check = cache.get(test_key)
        if deleted_check is None:
            print("✅ Cache DELETE operation successful")
        else:
            print("❌ Cache DELETE operation failed")
        
        print("\n🎉 Cache system is working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Cache operations failed: {e}")
        return False

def test_concurrent_views_cache():
    """Test the concurrent views cache manager."""
    print("\n🚀 Testing Concurrent Views Cache Manager")
    print("=" * 50)
    
    try:
        from transformer.concurrent_views import ConcurrentCacheManager
        
        cache_manager = ConcurrentCacheManager()
        
        # Test cache key generation
        test_params = {'searchType': 'BaseStation', 'pageSize': 20}
        cache_key = cache_manager.get_cache_key('test', test_params)
        print(f"✅ Cache key generated: {cache_key}")
        
        # Test cache operations
        test_data = {'results': [], 'count': 0, 'test': True}
        cache_manager.set_cached_data(cache_key, test_data, 60)
        print("✅ Concurrent cache SET successful")
        
        retrieved_data = cache_manager.get_cached_data(cache_key)
        if retrieved_data == test_data:
            print("✅ Concurrent cache GET successful")
        else:
            print("⚠️  Concurrent cache GET returned different data")
        
        print("🎉 Concurrent views cache manager is working!")
        return True
        
    except Exception as e:
        print(f"❌ Concurrent views cache test failed: {e}")
        return False

if __name__ == '__main__':
    print("🔧 Redis Fallback Configuration Test")
    print("=" * 60)
    
    cache_success = test_cache_configuration()
    concurrent_success = test_concurrent_views_cache()
    
    print("\n📊 SUMMARY")
    print("=" * 30)
    if cache_success and concurrent_success:
        print("✅ All tests passed! Your application will work without Redis.")
        print("💡 The export endpoint should now work without Redis connection errors.")
    else:
        print("❌ Some tests failed. Check the configuration.")
    
    print("\n🚀 You can now run your Django server:")
    print("   python core/manage.py runserver 8000")
