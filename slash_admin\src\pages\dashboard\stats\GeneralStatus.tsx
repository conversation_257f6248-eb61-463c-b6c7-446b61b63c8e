import React, { useState, useEffect } from 'react';
import { Row, Col, Spin } from 'antd';
import axios from 'axios';
import { themeVars } from "@/theme/theme.css";
import AnalysisCard from './analysis-card';

// Import your icons
import transformer_icon from "@/assets/images/glass/transformer.png";
import inspection_icon from "@/assets/images/glass/inspection.png";
import region_icon from "@/assets/images/glass/region.png";
import csc_icon from "@/assets/images/glass/csc.png";
import substation_icon from "@/assets/images/glass/substation.png";
import basestation_icon from "@/assets/images/glass/map.png";
import feeder_icon from "@/assets/images/glass/feeder.png";
import pending_icon from "@/assets/images/glass/pending.png";
import recent_icon from "@/assets/images/glass/recent.png";
import lvfeeder_icon from "@/assets/images/glass/lvfeeder.png";
import apiClient from '@/api/apiClient';

interface GeneralStats {
  totalTransformers: number;
  totalInspections: number;
  totalRegions: number;
  totalCSCs: number;
  recentInspections: number;
  pendingInspections: number;
  totalSubstations: number;
  totalFeeders: number;
  totalLvFeeders: number;
  totalBasestations: number;
}

const GeneralStatus: React.FC = () => {
  const [stats, setStats] = useState<GeneralStats>({
    totalTransformers: 0,
    totalInspections: 0,
    totalRegions: 0,
    totalCSCs: 0,
    recentInspections: 0,
    pendingInspections: 0,
    totalSubstations: 0,
    totalFeeders: 0,
    totalLvFeeders: 0,
    totalBasestations: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGeneralStats = async () => {
      try {
        // const response = await axios.get(`${import.meta.env.VITE_APP_BASE_API}/api/transformer/general-stats/`);
        const response = await apiClient.get<any>({ url: `/api/transformer/general-stats/`});
        setStats({
          totalTransformers: response.total_transformers,
          totalInspections: response.total_inspections,
          totalRegions: response.total_regions,
          totalCSCs: response.total_cscs,
          recentInspections: response.recent_inspections,
          pendingInspections: response.pending_inspections,
          totalSubstations: response.total_substations,
          totalFeeders: response.total_feeders,
          totalLvFeeders: response.total_lv_feeders,
          totalBasestations: response.total_basestations
        });
      } catch (error) {
        console.error('Error fetching general stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchGeneralStats();
  }, []);

  if (loading) {
    return (
      <div className="h-[400px] flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="w-full">
      <Row gutter={[16, 16]} justify="center">
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={transformer_icon}
            title={stats.totalTransformers.toString()}
            subtitle="Total Transformers"
            style={{
              color: themeVars.colors.palette.success.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.success.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={basestation_icon} // You might want to use a different icon
            title={stats.totalBasestations.toString()}
            subtitle="Total Base Stations"
            style={{
              color: themeVars.colors.palette.info.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.info.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={inspection_icon}
            title={stats.totalInspections.toString()}
            subtitle="Total Inspections"
            style={{
              color: themeVars.colors.palette.info.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.info.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={region_icon}
            title={stats.totalRegions.toString()}
            subtitle="Total Regions"
            style={{
              color: themeVars.colors.palette.warning.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.warning.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={csc_icon}
            title={stats.totalCSCs.toString()}
            subtitle="Total CSCs"
            style={{
              color: themeVars.colors.palette.error.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.error.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={recent_icon}
            title={stats.recentInspections.toString()}
            subtitle="Recent Inspections"
            style={{
              color: themeVars.colors.palette.success.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.success.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={pending_icon}
            title={stats.pendingInspections.toString()}
            subtitle="OverDue Inspections"
            style={{
              color: themeVars.colors.palette.info.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.info.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={substation_icon}
            title={stats.totalSubstations.toString()}
            subtitle="Total Substations"
            style={{
              color: themeVars.colors.palette.warning.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.warning.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={feeder_icon}
            title={stats.totalFeeders.toString()}
            subtitle="Total Feeders"
            style={{
              color: themeVars.colors.palette.error.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.error.defaultChannel}, .2)`,
            }}
          />
        </Col>
        <Col lg={6} md={12} span={24}>
          <AnalysisCard
            cover={lvfeeder_icon}
            title={stats.totalLvFeeders.toString()}
            subtitle="Total LV Feeders"
            style={{
              color: themeVars.colors.palette.success.dark,
              backgroundColor: `rgba(${themeVars.colors.palette.success.defaultChannel}, .2)`,
            }}
          />
        </Col>
      </Row>
    </div>
  );
};

export default GeneralStatus;




