import React, { useState } from "react";
import { Tabs } from "antd";
import TransformerAnalysis from "./TransformerAnaysis";  // Updated import path
import InspectionAnalysis from "./InspectionAnalysis";
import LatestInspection from "./latest-inspection";
import LvFeederAnalysis from "./LvFeederAnalysis";
import LatestLvFeederInspection from "./LatestLvFeederInspection";

const { TabPane } = Tabs;

const TransformerFilter: React.FC = () => {
	const [activeTab, setActiveTab] = useState("transformer");

	return (
		<div className="w-full p-4">
			<Tabs activeKey={activeTab} onChange={setActiveTab}>
				<TabPane tab="Transformer Analysis" key="transformer">
					<TransformerAnalysis />
				</TabPane>
				<TabPane tab="Inspection Analysis" key="inspection">
					<InspectionAnalysis />
				</TabPane>
				<TabPane tab="LvFeeder Analysis" key="lvfeeder">
					<LvFeederAnalysis />
				</TabPane>
				<TabPane tab="Latest Inspection" key="latest-inspection">
					<LatestInspection/>
				</TabPane>
				<TabPane tab="Latest LvFeeder Inspection" key="latest-lvfeeder-inspection">
					<LatestLvFeederInspection/>
				</TabPane>
			</Tabs>
		</div>
	);
};

export default TransformerFilter;



