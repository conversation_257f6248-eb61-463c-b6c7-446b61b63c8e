import React from "react";
import { Pie } from "@ant-design/plots";

const TransformerPieChart: React.FC<{ tabKey: string; data: TableData[] }> = ({ tabKey, data }) => {
	const preparePieData = () => {
		const filteredData = data.filter((item) => item.key !== "grand_total");
		const total = filteredData.reduce((acc, curr) => {
			switch (tabKey) {
				case "transformer_types":
					return acc + curr.conservator + curr.hermetical + curr.compact;
				case "capacity":
					return (
						acc +
						curr.kva10 +
						curr.kva25 +
						curr.kva50 +
						curr.kva100 +
						curr.kva200 +
						curr.kva315 +
						curr.kva400 +
						curr.kva500 +
						curr.kva630 +
						curr.kva800 +
						curr.kva1250 +
						curr.kva2500 +
						curr.kvaOther
					);
				case "cooling_type":
					return acc + curr.onan + curr.dryType;
				case "manufacturer":
					return acc + curr.abbTanzania + curr.apex;
				default:
					return acc;
			}
		}, 0);

		switch (tabKey) {
			case "transformer_types":
				return [
					{ type: "Conservator", value: filteredData.reduce((acc, curr) => acc + curr.conservator, 0) },
					{ type: "Hermetical", value: filteredData.reduce((acc, curr) => acc + curr.hermetical, 0) },
					{ type: "Compact", value: filteredData.reduce((acc, curr) => acc + curr.compact, 0) },
				];
			case "capacity":
				return [
					{ type: "10 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva10, 0) },
					{ type: "25 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva25, 0) },
					{ type: "50 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva50, 0) },
					{ type: "100 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva100, 0) },
					{ type: "200 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva200, 0) },
					{ type: "315 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva315, 0) },
					{ type: "400 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva400, 0) },
					{ type: "500 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva500, 0) },
					{ type: "630 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva630, 0) },
					{ type: "800 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva800, 0) },
					{ type: "1250 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva1250, 0) },
					{ type: "2500 kVA", value: filteredData.reduce((acc, curr) => acc + curr.kva2500, 0) },
					{ type: "Other", value: filteredData.reduce((acc, curr) => acc + (curr.kvaOther || 0), 0) },
				].filter((item) => item.value > 0); // Only show non-zero values
			case "cooling_type":
				return [
					{ type: "ONAN", value: filteredData.reduce((acc, curr) => acc + curr.onan, 0) },
					{ type: "Dry Type", value: filteredData.reduce((acc, curr) => acc + curr.dryType, 0) },
				];
			case "manufacturer":
				return [
					{ type: "ABB Tanzania", value: filteredData.reduce((acc, curr) => acc + curr.abbTanzania, 0) },
					{ type: "Apex", value: filteredData.reduce((acc, curr) => acc + curr.apex, 0) },
				];
			case "primary_voltage":
				return [
					{ type: "11 kV", value: filteredData.reduce((acc, curr) => acc + curr.v11, 0) },
					{ type: "33 kV", value: filteredData.reduce((acc, curr) => acc + curr.v33, 0) },
				];
			case "vector_group":
				return [
					{ type: "Dyn11", value: filteredData.reduce((acc, curr) => acc + curr.dyn11, 0) },
					{ type: "Yyn0", value: filteredData.reduce((acc, curr) => acc + curr.yyn0, 0) },
				];
			case "manufacturing_year":
				return [
					{ type: "2020", value: filteredData.reduce((acc, curr) => acc + curr.y2020, 0) },
					{ type: "2021", value: filteredData.reduce((acc, curr) => acc + curr.y2021, 0) },
					{ type: "2022", value: filteredData.reduce((acc, curr) => acc + curr.y2022, 0) },
				];
			default:
				return [];
		}
	};

	const config = {
		data: preparePieData(),
		angleField: "value",
		colorField: "type",
		radius: 0.8,
		label: {
			type: "outer",
			content: "{name} {percentage}",
		},
		interactions: [{ type: "element-active" }],
	};

	return (
		<div className="mt-8">
			<h3 className="text-lg font-semibold mb-4">Pie Chart</h3>
			<Pie {...config} height={400} width={400} />
		</div>
	);
};

export default TransformerPieChart;
