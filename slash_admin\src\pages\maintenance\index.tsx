import { <PERSON><PERSON>, Card, Input, Modal, Popconfirm, Space, Table, Tag, Form, Row, Col, Select, DatePicker } from "antd";
import { FilterOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import type { ColumnsType, TablePaginationConfig } from "antd/es/table";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import MaintenanceModal from "./maintenance-modal";
import maintenanceService from "@/api/services/maintenanceService";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import roleService from "@/api/services/roleService";
import type { UserInfo } from "#/entity";
import { IconButton, Iconify } from "@/components/icon";
import { usePathname, useRouter } from "@/router/hooks";

const { Option } = Select;
const { RangePicker } = DatePicker;

interface MaintenanceRecord {
	id: string;
	code: string;
	details: string;
	asset: string;
	parent_asset?: string;
	status: "New" | "Assigned" | "In Progress" | "Waiting For Parts" | "Complete";
	priority: "Low" | "Medium" | "High" | "Critical";
	type: "Breakdown" | "Corrective" | "Inspection" | "Preventive" | "Movement";
	date_created: string;
	date_scheduled: string;
	date_due: string;
	date_completed?: string;
	created_by?: number;
	assigned_to?: number[];
	completed_by?: number;
}

const DEFAULT_MAINTENANCE_VALUE: MaintenanceRecord = {
	id: "",
	code: "",
	details: "",
	asset: "",
	status: "New",
	priority: "Low",
	type: "Preventive",
	date_created: "",
	date_scheduled: "",
	date_due: "",
};

export type MaintenanceModalProps = {
	formValue: MaintenanceRecord;
	title: string;
	show: boolean;
	onOk: VoidFunction;
	onCancel: VoidFunction;
};

const MaintenancePage: React.FC = () => {
	const { push } = useRouter();
	const pathname = usePathname();
	// Add users query
	const { data: users = [], isLoading: usersLoading } = useQuery<UserInfo[]>({
		queryKey: ["users"],
		queryFn: () => roleService.getUser(),
		staleTime: 5 * 60 * 1000, // Cache for 5 minutes
	});

	const [showFilters, setShowFilters] = useState(false);
	const [maintenanceModalProps, setMaintenanceModalProps] = useState<MaintenanceModalProps>({
		formValue: { ...DEFAULT_MAINTENANCE_VALUE },
		title: "New",
		show: false,
		onOk: () => {
			setMaintenanceModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setMaintenanceModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const [filters, setFilters] = useState({
		createdByUsers: "All Users",
		assigned_toUsers: "All Users",
		completedByUsers: "All Users",
		assetParentAsset: "All Assets",
		priority: "All Priorities",
		status: "All Status",
		type: "All Types",
		failureCode: "All Failure Codes",
		dateCreated: null as [dayjs.Dayjs | null, dayjs.Dayjs | null] | null,
		dateDue: null as [dayjs.Dayjs | null, dayjs.Dayjs | null] | null,
		dateCompleted: null as [dayjs.Dayjs | null, dayjs.Dayjs | null] | null,
	});

	const [loading, setLoading] = useState(false);
	const [data, setData] = useState<MaintenanceRecord[]>([]);
	const [tableParams, setTableParams] = useState<{
		pagination: TablePaginationConfig;
	}>({
		pagination: {
			current: 1,
			pageSize: 10,
		},
	});

	const columns: ColumnsType<MaintenanceRecord> = [
		{
			title: "CODE",
			dataIndex: "code",
			width: 100,
			fixed: "left",
			render: (code) => <span className="font-medium">{code}</span>,
		},
		{
			title: "DETAILS",
			dataIndex: "details",
			width: 250,
			ellipsis: true,
		},
		{
			title: "ASSETS",
			dataIndex: "assets",
			width: 200,
			ellipsis: true,
			responsive: ["md"],
		},
		{
			title: "STATUS",
			dataIndex: "status",
			width: 120,
			align: "center",
			render: (status) => {
				const colorMap: Record<string, string> = {
					New: "default",
					Assigned: "processing",
					"In Progress": "warning",
					"Waiting For Parts": "error",
					Complete: "success",
				};
				return <Tag color={colorMap[status]}>{status}</Tag>;
			},
		},
		{
			title: "PRIORITY",
			dataIndex: "priority",
			width: 100,
			align: "center",
			responsive: ["lg"],
			render: (priority) => {
				const colorMap: Record<string, string> = {
					Low: "default",
					Medium: "warning",
					High: "error",
					Critical: "volcano",
				};
				return <Tag color={colorMap[priority]}>{priority}</Tag>;
			},
		},
		{
			title: "TYPE",
			dataIndex: "type",
			width: 120,
			align: "center",
			responsive: ["lg"],
			render: (type) => <Tag color="cyan">{type}</Tag>,
		},
		{
			title: "DATE DUE",
			dataIndex: "dateDue",
			width: 120,
			responsive: ["md"],
			render: (date) => dayjs(date).format("YYYY-MM-DD"),
		},
		{
			title: "DATE COMPLETED",
			dataIndex: "dateCompleted",
			width: 120,
			responsive: ["lg"],
			render: (date) => (date ? dayjs(date).format("YYYY-MM-DD") : "-"),
		},
		{
			title: "Action",
			key: "action",
			width: 100,
			fixed: "right",
			align: "center",
			render: (_, record) => (
				<div className="flex w-full justify-center text-gray">
					<IconButton
						onClick={() => {
							console.log(`${pathname}/${record.id}`);
							push(`${pathname}/${record.id}`);
						}}
					>
						<Iconify icon="carbon:view" size={18} />
					</IconButton>
					<IconButton onClick={() => handleEdit(record)}>
						<Iconify icon="solar:pen-bold-duotone" size={18} />
					</IconButton>
					<Popconfirm
						title="Delete this maintenance record?"
						okText="Yes"
						cancelText="No"
						placement="left"
						onConfirm={() => handleDelete(record.id)}
						getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
					>
						<IconButton>
							<Iconify icon="mingcute:delete-2-fill" size={18} className="text-error" />
						</IconButton>
					</Popconfirm>
				</div>
			),
		},
	];

	const handleEdit = (record: MaintenanceRecord) => {
		const formattedRecord = {
			...record,
			date_scheduled: record.date_scheduled ? dayjs(record.date_scheduled) : null,
			date_due: record.date_due ? dayjs(record.date_due) : null,
			date_completed: record.date_completed ? dayjs(record.date_completed) : null,
		};

		setMaintenanceModalProps((prev) => ({
			...prev,
			show: true,
			title: "Edit",
			formValue: formattedRecord,
		}));
	};

	const handleDelete = async (id: string) => {
		try {
			await maintenanceService.deleteMaintenance(id);
			toast.success("Maintenance record deleted successfully");
			fetchData(); // Refresh the table after deletion
		} catch (error) {
			toast.error("Failed to delete maintenance record");
		}
	};

	const onCreate = () => {
		setMaintenanceModalProps((prev) => ({
			...prev,
			show: true,
			title: "New",
			formValue: { ...DEFAULT_MAINTENANCE_VALUE },
		}));
	};

	const handleApplyFilters = () => {
		fetchData(filters);
	};

	const handleResetFilters = () => {
		const resetFilters = {
			createdByUsers: "All Users",
			assigned_toUsers: "All Users",
			completedByUsers: "All Users",
			assetParentAsset: "All Assets",
			priority: "All Priorities",
			status: "All Status",
			type: "All Types",
			failureCode: "All Failure Codes",
			dateCreated: null,
			dateDue: null,
			dateCompleted: null,
		};
		setFilters(resetFilters);
		fetchData(resetFilters);
	};

	const fetchData = async (filterParams = filters) => {
		setLoading(true);
		try {
			// Only include non-"All" values in the filter
			const activeFilters = Object.entries(filterParams).reduce(
				(acc, [key, value]) => {
					if (value && !value.toString().startsWith("All ")) {
						if (value instanceof Array && value[0] && value[1]) {
							// Handle date ranges
							acc[key] = value.map((date) => (date ? dayjs(date).format("YYYY-MM-DD") : null));
						} else {
							acc[key] = value;
						}
					}
					return acc;
				},
				{} as Record<string, any>,
			);

			const response = await maintenanceService.getMaintenanceFiltered({
				...activeFilters,
				page: tableParams.pagination.current,
			});

			setData(response?.results);
			setTableParams({
				...tableParams,
				pagination: {
					...tableParams.pagination,
					total: response.count,
				},
			});
		} catch (error) {
			toast.error("Failed to fetch maintenance records");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchData();
	}, [tableParams.pagination.current]); // Reload when page changes

	return (
		<Card className="min-h-full">
			{/* Header - Responsive */}
			<div className="mb-4 flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
				<h2 className="text-xl font-bold">Work Orders</h2>
				<Space className="flex flex-col xs:flex-row gap-2" wrap>
					<Button
						icon={<FilterOutlined />}
						onClick={() => setShowFilters(!showFilters)}
						type={showFilters ? "primary" : "default"}
					>
						Filters
					</Button>
					<Input placeholder="Search" prefix={<SearchOutlined />} className="min-w-[200px]" />
					<Button type="primary" icon={<PlusOutlined />} onClick={onCreate}>
						Add New
					</Button>
				</Space>
			</div>

			{/* Filters Section - Responsive */}
			{showFilters && (
				<div className="mb-4 bg-gray-50 p-4 rounded-lg">
					<Form layout="vertical">
						<Row gutter={[16, 16]}>
							{/* User Filters */}
							<Col xs={24} sm={24} md={8}>
								<Form.Item label="Created By">
									<Select
										value={filters.createdByUsers}
										onChange={(val) => setFilters((prev) => ({ ...prev, createdByUsers: val }))}
										className="w-full"
										loading={usersLoading}
										allowClear
									>
										<Option value="">All Users</Option>
										{users.map((user) => (
											<Option key={user.id} value={user.id}>
												{user.username}
											</Option>
										))}
									</Select>
								</Form.Item>
								<Form.Item label="Assigned To">
									<Select
										value={filters.assigned_toUsers}
										onChange={(val) => setFilters((prev) => ({ ...prev, assigned_toUsers: val }))}
										className="w-full"
										loading={usersLoading}
										allowClear
									>
										<Option value="">All Users</Option>
										{users.map((user) => (
											<Option key={user.id} value={user.id}>
												{user.username}
											</Option>
										))}
									</Select>
								</Form.Item>
								<Form.Item label="Completed By">
									<Select
										value={filters.completedByUsers}
										onChange={(val) => setFilters((prev) => ({ ...prev, completedByUsers: val }))}
										className="w-full"
										loading={usersLoading}
										allowClear
									>
										<Option value="">All Users</Option>
										{users.map((user) => (
											<Option key={user.id} value={user.id}>
												{user.username}
											</Option>
										))}
									</Select>
								</Form.Item>
							</Col>

							{/* Status and Type Filters */}
							<Col xs={24} sm={24} md={8}>
								<Form.Item label="Status">
									<Select
										value={filters.status}
										onChange={(val) => setFilters((prev) => ({ ...prev, status: val }))}
										className="w-full"
									>
										<Option value="All Status">All Status</Option>
										<Option value="New">New</Option>
										<Option value="Assigned">Assigned</Option>
										<Option value="In Progress">In Progress</Option>
										<Option value="Waiting For Parts">Waiting For Parts</Option>
										<Option value="Complete">Complete</Option>
									</Select>
								</Form.Item>
								<Form.Item label="Priority">
									<Select
										value={filters.priority}
										onChange={(val) => setFilters((prev) => ({ ...prev, priority: val }))}
										className="w-full"
									>
										<Option value="All Priorities">All Priorities</Option>
										<Option value="Low">Low</Option>
										<Option value="Medium">Medium</Option>
										<Option value="High">High</Option>
										<Option value="Critical">Critical</Option>
									</Select>
								</Form.Item>
								<Form.Item label="Type">
									<Select
										value={filters.type}
										onChange={(val) => setFilters((prev) => ({ ...prev, type: val }))}
										className="w-full"
									>
										<Option value="All Types">All Types</Option>
										<Option value="Breakdown">Breakdown</Option>
										<Option value="Corrective">Corrective</Option>
										<Option value="Inspection">Inspection</Option>
										<Option value="Preventive">Preventive</Option>
										<Option value="Movement">Movement</Option>
									</Select>
								</Form.Item>
							</Col>

							{/* Date Filters */}
							<Col xs={24} sm={24} md={8}>
								<Form.Item label="Date Created">
									<DatePicker.RangePicker
										value={filters.dateCreated as any}
										onChange={(dates) => setFilters((prev) => ({ ...prev, dateCreated: dates }))}
										className="w-full"
									/>
								</Form.Item>
								<Form.Item label="Date Due">
									<DatePicker.RangePicker
										value={filters.dateDue as any}
										onChange={(dates) => setFilters((prev) => ({ ...prev, dateDue: dates }))}
										className="w-full"
									/>
								</Form.Item>
								<Form.Item label="Date Completed">
									<DatePicker.RangePicker
										value={filters.dateCompleted as any}
										onChange={(dates) => setFilters((prev) => ({ ...prev, dateCompleted: dates }))}
										className="w-full"
									/>
								</Form.Item>
							</Col>
						</Row>

						{/* Filter Actions */}
						<Row justify="end" gutter={[8, 8]} className="mt-4">
							<Col>
								<Space>
									<Button onClick={handleResetFilters}>Reset</Button>
									<Button type="primary" onClick={handleApplyFilters}>
										Apply
									</Button>
								</Space>
							</Col>
						</Row>
					</Form>
				</div>
			)}

			{/* Table - Responsive */}
			<div className="overflow-x-auto">
				<Table<MaintenanceRecord>
					columns={columns}
					dataSource={data}
					rowKey="id"
					scroll={{ x: "max-content" }}
					pagination={{
						...tableParams.pagination,
						showSizeChanger: true,
						showQuickJumper: true,
						responsive: true,
						className: "px-4",
					}}
					loading={loading}
					onChange={(pagination, filters, sorter) => {
						setTableParams({
							pagination,
						});
					}}
					className="mt-4"
					size="middle"
				/>
			</div>

			{/* Modal */}
			<MaintenanceModal {...maintenanceModalProps} />
		</Card>
	);
};

export default MaintenancePage;
