from rest_framework import viewsets, status, generics, permissions
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model, authenticate
from django.db.models import Q, Count
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json
from .tasks import send_sms, reply_sms

from .models import (
    SMSMessage, Reply, Attachment, TimelineEvent, AssignmentHistory,
    ChecklistItem, ProgressReport, ProgressUpdate, Notification,
    ApprovalRequest, ApprovalHistory, AssignmentStatus, DetailedReport,
    MessageTemplate, SystemSettings, Analytics, Department
)
from .serializers import (
    SMSMessageSerializer, SMSMessageListSerializer, ReplySerializer,
    AttachmentSerializer, TimelineEventSerializer, ChecklistItemSerializer,
    ProgressReportSerializer, NotificationSerializer, ApprovalRequestSerializer,
    DetailedReportSerializer, UserSerializer, MessageTemplateSerializer,
    SystemSettingsSerializer, AnalyticsSerializer, AssignmentStatusSerializer,
    AssignmentHistorySerializer, DepartmentSerializer
)
from .tasks import send_notification_task, update_analytics_task

from django.db.models import Max, F, Case, When, Q

User = get_user_model()
channel_layer = get_channel_layer()


class SMSMessageViewSet(viewsets.ModelViewSet):
    """ViewSet for SMS Messages matching frontend expectations"""
    queryset = SMSMessage.objects.all()
    permission_classes = [IsAuthenticated]
    filterset_fields = ['status', 'category', 'priority', 'assigned_to', 'is_archived']
    search_fields = ['phone_number', 'content', 'case_id']
    ordering_fields = ['timestamp', 'priority', 'status']
    ordering = ['-timestamp']

    def get_serializer_class(self):
        if self.action == 'list':
            return SMSMessageListSerializer
        return SMSMessageSerializer

    def get_queryset(self):
        """Enhanced queryset with advanced filtering"""
        queryset = super().get_queryset()

        # Search functionality
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(phone_number__icontains=search) |
                Q(content__icontains=search) |
                Q(case_id__icontains=search)
            )

        # Status filter
        status_filter = self.request.query_params.get('status')
        if status_filter and status_filter != 'all':
            queryset = queryset.filter(status=status_filter)

        # Category filter
        category = self.request.query_params.get('category')
        if category and category != 'all':
            queryset = queryset.filter(category=category)

        # Priority filter
        priority = self.request.query_params.get('priority')
        if priority and priority != 'all':
            queryset = queryset.filter(priority=priority)

        # Assignee filter
        assignee = self.request.query_params.get('assignee')
        if assignee and assignee != 'all':
            if assignee == 'unassigned':
                queryset = queryset.filter(assigned_to__isnull=True)
            else:
                queryset = queryset.filter(assigned_to_id=assignee)

        # Tags filter
        tags = self.request.query_params.get('tags')
        if tags:
            tag_list = tags.split(',')
            for tag in tag_list:
                queryset = queryset.filter(tags__icontains=tag.strip())

        # Date range filter
        date_start = self.request.query_params.get('date_start')
        date_end = self.request.query_params.get('date_end')
        if date_start:
            try:
                start_date = timezone.datetime.fromisoformat(date_start.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__gte=start_date)
            except ValueError:
                pass
        if date_end:
            try:
                end_date = timezone.datetime.fromisoformat(date_end.replace('Z', '+00:00'))
                queryset = queryset.filter(timestamp__lte=end_date)
            except ValueError:
                pass

        # Archived filter
        archived = self.request.query_params.get('archived')
        if archived is not None:
            is_archived = archived.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_archived=is_archived)

        return queryset

    def get_queryset(self):
        queryset = SMSMessage.objects.all()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by category
        category_filter = self.request.query_params.get('category')
        if category_filter:
            queryset = queryset.filter(category=category_filter)

        # Filter by priority
        priority_filter = self.request.query_params.get('priority')
        if priority_filter:
            queryset = queryset.filter(priority=priority_filter)

        # Filter by assigned user
        assigned_to = self.request.query_params.get('assigned_to')
        if assigned_to:
            queryset = queryset.filter(assigned_to_id=assigned_to)

        # Search functionality
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(content__icontains=search) |
                Q(phone_number__icontains=search) |
                Q(case_id__icontains=search)
            )

        # Filter archived messages
        include_archived = self.request.query_params.get('include_archived', 'false')
        if include_archived.lower() != 'true':
            queryset = queryset.filter(is_archived=False)

        # Filter archived messages
        include_archived = self.request.query_params.get('include_archived', 'false')
        if include_archived.lower() != 'true':
            queryset = queryset.filter(is_archived=False)

        # Annotate with last reply time and sort time
        queryset = queryset.annotate(
            last_reply_time=Max('replies__timestamp')
        ).annotate(
            sort_time=Case(
                When(last_reply_time__isnull=False, then=F('last_reply_time')),
                default=F('timestamp')
            )
        ).order_by('-sort_time')

        return queryset.select_related('assigned_to').prefetch_related(
            'replies__sender', 'attachments', 'timeline_events__user'
        )

    def destroy(self, request, *args, **kwargs):
        """Delete a message and log the action"""
        message = self.get_object()

        # Optional: Add permission check (e.g., only assigned user or admin can delete)
        if message.assigned_to != request.user and not request.user.is_staff:
            return Response(
                {'error': 'You do not have permission to delete this message'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Store message details for logging before deletion
        message_id = str(message.id)
        case_id = message.case_id

        # Create timeline event before deletion
        TimelineEvent.objects.create(
            message=message,
            type='deletion',
            user=request.user,
            description=f"Message deleted by {request.user.get_full_name() or request.user.username}"
        )

        # Send real-time notification before deletion
        self.send_deletion_notification(message)

        # Perform the deletion (soft or hard delete)
        # Option 1: Hard delete (permanently remove the message)
        message.delete()

        # Option 2: Soft delete (if you want to mark as deleted instead)
        # message.is_deleted = True
        # message.deleted_at = timezone.now()
        # message.deleted_by = request.user
        # message.save()

        return Response(
            {'message': 'Message deleted successfully'},
            status=status.HTTP_200_OK
        )

    def send_deletion_notification(self, message):
        """Send real-time deletion notification"""
        channel_layer = get_channel_layer()
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'messages',
                {
                    'type': 'message_deletion',
                    'message_id': str(message.id),
                    'case_id': message.case_id,
                    'deleted_by': message.assigned_to.username if message.assigned_to else None,
                    'timestamp': timezone.now().isoformat()
                }
            )

    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """Assign message to a user"""
        message = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            old_assignee = message.assigned_to
            message.assigned_to = user
            message.save()

            # Create assignment history
            AssignmentHistory.objects.create(
                message=message,
                from_user=old_assignee,
                to_user=user,
                assigned_by=request.user,
                reason=request.data.get('reason', '')
            )

            # Create timeline event
            TimelineEvent.objects.create(
                message=message,
                type='assignment',
                user=request.user,
                description=f"Assigned to {user.get_full_name() or user.username}"
            )

            # Send real-time notification
            self.send_assignment_notification(message, user)

            return Response({'message': 'Assignment successful'})
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update message status"""
        message = self.get_object()
        new_status = request.data.get('status')

        if new_status not in dict(SMSMessage.STATUS_CHOICES):
            return Response({'error': 'Invalid status'}, status=status.HTTP_400_BAD_REQUEST)

        old_status = message.status
        message.status = new_status
        message.save()

        # Create timeline event
        TimelineEvent.objects.create(
            message=message,
            type='status_change',
            user=request.user,
            description=f"Status changed from {old_status} to {new_status}"
        )

        # Send real-time update
        self.send_status_update(message)

        return Response({'message': 'Status updated successfully'})

    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive/unarchive a message"""
        message = self.get_object()
        archive = request.data.get('archive', True)

        message.is_archived = archive
        if archive:
            message.archived_at = timezone.now()
            message.archived_by = request.user
        else:
            message.archived_at = None
            message.archived_by = None
        message.save()

        action_text = "archived" if archive else "unarchived"
        return Response({'message': f'Message {action_text} successfully'})

    @action(detail=True, methods=['post'])
    def add_reply(self, request, pk=None):
        """Add a reply to a message"""
        message = self.get_object()
        content = request.data.get('content')

        print("LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL")

        if not content:
            return Response({'error': 'Content is required'}, status=status.HTTP_400_BAD_REQUEST)

        reply = Reply.objects.create(
            message=message,
            content=content,
            sender=request.user,
            is_from_customer=False
        )

        # Create timeline event
        TimelineEvent.objects.create(
            message=message,
            type='reply',
            user=request.user,
            description=f"Added reply"
        )

        # Send real-time update
        self.send_new_reply(message, reply)

        serializer = ReplySerializer(reply)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def send_assignment_notification(self, message, user):
        """Send real-time assignment notification"""
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'assignments',
                {
                    'type': 'new_assignment',
                    'assignment': {
                        'message_id': str(message.id),
                        'case_id': message.case_id,
                        'assigned_to': user.username,
                        'timestamp': timezone.now().isoformat()
                    }
                }
            )

    def send_status_update(self, message):
        """Send real-time status update"""
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'messages',
                {
                    'type': 'message_status_update',
                    'message_id': str(message.id),
                    'status': message.status,
                    'timestamp': timezone.now().isoformat()
                }
            )

    def send_new_reply(self, message, reply):
        """Send real-time new reply notification"""

        print("MMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMM", reply.id)
        # reply_sms.delay(reply.id)
        reply_sms(reply.id)

        
        if channel_layer:
            async_to_sync(channel_layer.group_send)(
                'messages',
                {
                    'type': 'new_reply_broadcast',
                    'message_id': str(message.id),
                    'reply': {
                        'id': str(reply.id),
                        'content': reply.content,
                        'timestamp': reply.timestamp.isoformat(),
                        'sender': reply.sender.username
                    }
                }
            )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_sms_api(request):
    """API endpoint to send a single SMS"""
    serializer = SMSMessageSerializer(data=request.data)
    if serializer.is_valid():
        sms_message = serializer.save(created_by=request.user)
        send_sms.delay(sms_message.id)
        return Response({
            'message': 'SMS queued for sending',
            'sms_id': sms_message.id
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for Users"""
    queryset = User.objects.filter(is_active=True)
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user info"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)


class ReplyViewSet(viewsets.ModelViewSet):
    """ViewSet for Replies with pagination and filtering"""
    queryset = Reply.objects.all()
    serializer_class = ReplySerializer
    permission_classes = [IsAuthenticated]
    ordering = ['timestamp']  # Oldest replies first for chat-like experience (newest at bottom)

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by message ID
        message_id = self.request.query_params.get("message_id")
        if message_id:
            queryset = queryset.filter(message_id=message_id)

        # Filter by phone number (for filtering replies within a message)
        phone_number = self.request.query_params.get("phone_number")
        if phone_number:
            queryset = queryset.filter(message__phone_number__icontains=phone_number)

        # Search in reply content
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(content__icontains=search)

        # Filter by sender type
        sender_type = self.request.query_params.get('sender_type')
        if sender_type == 'customer':
            queryset = queryset.filter(is_from_customer=True)
        elif sender_type == 'agent':
            queryset = queryset.filter(is_from_customer=False)

        return queryset

    def perform_create(self, serializer):
        reply = serializer.save(sender=self.request.user)

        # Create timeline event
        TimelineEvent.objects.create(
            message=reply.message,
            type='reply',
            user=self.request.user,
            description=f"Added reply"
        )


class NotificationViewSet(viewsets.ModelViewSet):
    """ViewSet for Notifications"""
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user).order_by('-timestamp')

    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """Mark notification as read"""
        notification = self.get_object()
        notification.read = True
        notification.save()
        return Response({'message': 'Notification marked as read'})

    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """Mark all notifications as read"""
        self.get_queryset().update(read=True)
        return Response({'message': 'All notifications marked as read'})


class ApprovalRequestViewSet(viewsets.ModelViewSet):
    """ViewSet for Approval Requests"""
    queryset = ApprovalRequest.objects.all()
    serializer_class = ApprovalRequestSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = ApprovalRequest.objects.all()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by type
        type_filter = self.request.query_params.get('type')
        if type_filter:
            queryset = queryset.filter(type=type_filter)

        return queryset.order_by('-requested_at')

    def perform_create(self, serializer):
        serializer.save(requested_by=self.request.user)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a request"""
        approval_request = self.get_object()
        comments = request.data.get('comments', '')

        approval_request.status = 'approved'
        approval_request.approved_by = request.user
        approval_request.approved_at = timezone.now()
        approval_request.comments = comments
        approval_request.save()

        # Create approval history
        ApprovalHistory.objects.create(
            approval_request=approval_request,
            decision='approved',
            comments=comments,
            approved_by=request.user
        )

        return Response({'message': 'Request approved successfully'})

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a request"""
        approval_request = self.get_object()
        comments = request.data.get('comments', '')

        approval_request.status = 'rejected'
        approval_request.approved_by = request.user
        approval_request.approved_at = timezone.now()
        approval_request.comments = comments
        approval_request.save()

        # Create approval history
        ApprovalHistory.objects.create(
            approval_request=approval_request,
            decision='rejected',
            comments=comments,
            approved_by=request.user
        )

        return Response({'message': 'Request rejected successfully'})


class ProgressReportViewSet(viewsets.ModelViewSet):
    """ViewSet for Progress Reports"""
    queryset = ProgressReport.objects.all()
    serializer_class = ProgressReportSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(reported_by=self.request.user)


class DetailedReportViewSet(viewsets.ModelViewSet):
    """ViewSet for Detailed Reports"""
    queryset = DetailedReport.objects.all()
    serializer_class = DetailedReportSerializer
    permission_classes = [IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)


class MessageTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for Message Templates"""
    queryset = MessageTemplate.objects.filter(is_active=True)
    serializer_class = MessageTemplateSerializer
    permission_classes = [IsAuthenticated]


class DepartmentViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for Departments"""
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_stats(request):
    """Get dashboard statistics matching frontend expectations"""
    # Get basic counts
    total_cases = SMSMessage.objects.count()
    new_cases = SMSMessage.objects.filter(status='new').count()
    in_progress_cases = SMSMessage.objects.filter(status='in-progress').count()
    replied_cases = SMSMessage.objects.filter(status='replied').count()
    closed_cases = SMSMessage.objects.filter(status='closed').count()

    # Get category breakdown
    category_counts = {}
    for category, label in SMSMessage.CATEGORY_CHOICES:
        count = SMSMessage.objects.filter(category=category).count()
        category_counts[category] = count

    # Get priority breakdown
    priority_counts = {}
    for priority, label in SMSMessage.PRIORITY_CHOICES:
        count = SMSMessage.objects.filter(priority=priority).count()
        priority_counts[priority] = count

    # Get recent activity (last 7 days)
    from datetime import timedelta
    week_ago = timezone.now() - timedelta(days=7)
    recent_messages = SMSMessage.objects.filter(timestamp__gte=week_ago).count()

    # Get user assignments
    user_assignments = SMSMessage.objects.filter(
        assigned_to__isnull=False
    ).values('assigned_to__username').annotate(count=Count('id'))

    return Response({
        'total_cases': total_cases,
        'new_cases': new_cases,
        'in_progress_cases': in_progress_cases,
        'replied_cases': replied_cases,
        'closed_cases': closed_cases,
        'category_counts': category_counts,
        'priority_counts': priority_counts,
        'recent_activity': recent_messages,
        'user_assignments': list(user_assignments),
        'response_time_avg': 2.5,  # This would be calculated from actual data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def analytics_data(request):
    """Get analytics data for charts and reports"""
    # Get daily trends for the last 30 days
    from datetime import timedelta
    thirty_days_ago = timezone.now() - timedelta(days=30)

    daily_data = []
    for i in range(30):
        date = thirty_days_ago + timedelta(days=i)
        day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
        day_end = day_start + timedelta(days=1)

        daily_count = SMSMessage.objects.filter(
            timestamp__gte=day_start,
            timestamp__lt=day_end
        ).count()

        daily_data.append({
            'date': day_start.strftime('%Y-%m-%d'),
            'count': daily_count
        })

    return Response({
        'daily_trends': daily_data,
        'category_distribution': dict(SMSMessage.objects.values_list('category').annotate(Count('category'))),
        'status_distribution': dict(SMSMessage.objects.values_list('status').annotate(Count('status'))),
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def bulk_assign(request):
    """Bulk assign messages to users"""
    message_ids = request.data.get('message_ids', [])
    user_id = request.data.get('user_id')

    if not message_ids or not user_id:
        return Response({'error': 'Message IDs and user ID are required'}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(id=user_id)
        messages = SMSMessage.objects.filter(id__in=message_ids)

        for message in messages:
            old_assignee = message.assigned_to
            message.assigned_to = user
            message.save()

            # Create assignment history
            AssignmentHistory.objects.create(
                message=message,
                from_user=old_assignee,
                to_user=user,
                assigned_by=request.user,
                reason=request.data.get('reason', 'Bulk assignment')
            )

        return Response({'message': f'Successfully assigned {len(messages)} messages'})
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)


# Authentication Views
class LogoutAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            request.user.auth_token.delete()
        except:
            pass
        return Response({'message': 'Logged out successfully'})


class CurrentUserAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        return Response(UserSerializer(request.user).data)


# Message specific API views
class AssignMessageAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            message = SMSMessage.objects.get(pk=pk)
            user_id = request.data.get('user_id')
            reason = request.data.get('reason', '')

            if user_id:
                user = User.objects.get(id=user_id)
                old_assignee = message.assigned_to

                message.assigned_to = user
                message.status = 'in-progress'
                message.save()

                # Create assignment history
                AssignmentHistory.objects.create(
                    message=message,
                    from_user=old_assignee,
                    to_user=user,
                    assigned_by=request.user,
                    reason=reason
                )

                # Create timeline event
                TimelineEvent.objects.create(
                    message=message,
                    type='assignment',
                    user=request.user,
                    description=f"Assigned to {user.get_full_name()}"
                )

                # Send notification
                send_notification_task.delay(
                    str(user.id),
                    'assignment',
                    'New Assignment',
                    f'You have been assigned to case {message.case_id}',
                    message.case_id,
                    str(message.id)
                )

                return Response({'message': 'Message assigned successfully'})

            return Response(
                {'error': 'User ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        except SMSMessage.DoesNotExist:
            return Response(
                {'error': 'Message not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class UpdateMessageStatusAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            message = SMSMessage.objects.get(pk=pk)
            new_status = request.data.get('status')

            if new_status in dict(SMSMessage.STATUS_CHOICES):
                old_status = message.status
                message.status = new_status
                message.save()

                # Create timeline event
                TimelineEvent.objects.create(
                    message=message,
                    type='status_change',
                    user=request.user,
                    description=f"Status changed from {old_status} to {new_status}"
                )

                return Response({'message': 'Status updated successfully'})

            return Response(
                {'error': 'Invalid status'},
                status=status.HTTP_400_BAD_REQUEST
            )

        except SMSMessage.DoesNotExist:
            return Response(
                {'error': 'Message not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class ArchiveMessageAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            message = SMSMessage.objects.get(pk=pk)
            archive = request.data.get('archive', True)

            message.is_archived = archive
            if archive:
                message.archived_at = timezone.now()
                message.archived_by = request.user
            else:
                message.archived_at = None
                message.archived_by = None
            message.save()

            action = 'archived' if archive else 'restored'
            TimelineEvent.objects.create(
                message=message,
                type='archive' if archive else 'restore',
                user=request.user,
                description=f"Message {action}"
            )

            return Response({'message': f'Message {action} successfully'})

        except SMSMessage.DoesNotExist:
            return Response(
                {'error': 'Message not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class AddReplyAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    # def post(self, request, pk):
    #     try:
    #         message = SMSMessage.objects.get(pk=pk)
    #         content = request.data.get('content')

    #         if content:
    #             reply = Reply.objects.create(
    #                 message=message,
    #                 content=content,
    #                 sender=request.user,
    #                 is_from_customer=False
    #             )

    #             # Update message status
    #             message.status = 'replied'
    #             message.save()

    #             print("message.id", message.id)

    #             send_sms.delay(message.id)

    #             # Create timeline event
    #             TimelineEvent.objects.create(
    #                 message=message,
    #                 type='reply',
    #                 user=request.user,
    #                 description="Reply sent to customer"
    #             )

    #             return Response(ReplySerializer(reply).data)

    #         return Response(
    #             {'error': 'Content is required'},
    #             status=status.HTTP_400_BAD_REQUEST
    #         )

    #     except SMSMessage.DoesNotExist:
    #         return Response(
    #             {'error': 'Message not found'},
    #             status=status.HTTP_404_NOT_FOUND
    #         )


class BulkAssignAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            message_ids = request.data.get('message_ids', [])
            user_id = request.data.get('user_id')
            reason = request.data.get('reason', 'Bulk assignment')

            if not message_ids or not user_id:
                return Response(
                    {'error': 'Message IDs and user ID are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            user = User.objects.get(id=user_id)
            messages = SMSMessage.objects.filter(id__in=message_ids)

            for message in messages:
                old_assignee = message.assigned_to
                message.assigned_to = user
                message.status = 'in-progress'
                message.save()

                # Create assignment history
                AssignmentHistory.objects.create(
                    message=message,
                    from_user=old_assignee,
                    to_user=user,
                    assigned_by=request.user,
                    reason=reason
                )

            return Response({'message': f'Successfully assigned {len(messages)} messages'})

        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )


# Notification API views
class MarkNotificationReadAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            notification = Notification.objects.get(pk=pk, user=request.user)
            notification.read = True
            notification.save()
            return Response({'message': 'Notification marked as read'})
        except Notification.DoesNotExist:
            return Response(
                {'error': 'Notification not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class MarkAllNotificationsReadAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        Notification.objects.filter(user=request.user, read=False).update(read=True)
        return Response({'message': 'All notifications marked as read'})


# Approval API views
class ApproveRequestAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            approval_request = ApprovalRequest.objects.get(pk=pk)
            comments = request.data.get('comments', '')

            approval_request.status = 'approved'
            approval_request.save()

            # Create approval history
            ApprovalHistory.objects.create(
                approval_request=approval_request,
                approved_by=request.user,
                action='approved',
                comments=comments
            )

            return Response({'message': 'Request approved successfully'})

        except ApprovalRequest.DoesNotExist:
            return Response(
                {'error': 'Approval request not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class RejectRequestAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, pk):
        try:
            approval_request = ApprovalRequest.objects.get(pk=pk)
            comments = request.data.get('comments', '')

            approval_request.status = 'rejected'
            approval_request.save()

            # Create approval history
            ApprovalHistory.objects.create(
                approval_request=approval_request,
                approved_by=request.user,
                action='rejected',
                comments=comments
            )

            return Response({'message': 'Request rejected successfully'})

        except ApprovalRequest.DoesNotExist:
            return Response(
                {'error': 'Approval request not found'},
                status=status.HTTP_404_NOT_FOUND
            )


# Dashboard API views
class DashboardStatsAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        total_cases = SMSMessage.objects.count()
        new_cases = SMSMessage.objects.filter(status='new').count()
        in_progress_cases = SMSMessage.objects.filter(status='in-progress').count()
        replied_cases = SMSMessage.objects.filter(status='replied').count()
        closed_cases = SMSMessage.objects.filter(status='closed').count()

        # Recent activity
        recent_messages = SMSMessage.objects.order_by('-timestamp')[:10]

        # Status distribution
        status_stats = SMSMessage.objects.values('status').annotate(count=Count('status'))

        return Response({
            'total_cases': total_cases,
            'new_cases': new_cases,
            'in_progress_cases': in_progress_cases,
            'replied_cases': replied_cases,
            'closed_cases': closed_cases,
            'recent_messages': SMSMessageListSerializer(recent_messages, many=True).data,
            'status_stats': list(status_stats)
        })


class AnalyticsAPIView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # Get analytics for the last 30 days
        end_date = timezone.now()
        start_date = end_date - timezone.timedelta(days=30)

        # Messages by day
        daily_stats = []
        for i in range(30):
            date = start_date + timezone.timedelta(days=i)
            count = SMSMessage.objects.filter(
                timestamp__date=date.date()
            ).count()
            daily_stats.append({
                'date': date.date().isoformat(),
                'count': count
            })

        # Category distribution
        category_stats = SMSMessage.objects.values('category').annotate(
            count=Count('category')
        )

        # Priority distribution
        priority_stats = SMSMessage.objects.values('priority').annotate(
            count=Count('priority')
        )

        return Response({
            'daily_stats': daily_stats,
            'category_stats': list(category_stats),
            'priority_stats': list(priority_stats)
        })


@api_view(['GET'])
@permission_classes([])  # Allow anonymous access for testing
def websocket_test_view(request):
    """Test endpoint to verify WebSocket configuration"""
    from django.conf import settings

    return Response({
        'message': 'WebSocket test endpoint',
        'asgi_application': getattr(settings, 'ASGI_APPLICATION', 'Not configured'),
        'channel_layers': getattr(settings, 'CHANNEL_LAYERS', 'Not configured'),
        'websocket_urls': [
            '/ws/messages/',
            '/ws/notifications/<user_id>/',
            '/ws/assignments/'
        ],
        'server_info': 'Django server running with ASGI support'
    })