INSERT INTO `regions` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'ADAMA REGION', '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(2, 'AFAR REGION', '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(3, 'AMBO REGION', '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(4, 'ARBAMINCH REGION', '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(5, 'BAHIRDAR REGION', '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(6, 'BALE ROBE REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(7, 'B/GUMZ REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(8, 'CHIRO REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(9, 'D/BIRHAN REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(10, 'D/MARKOS REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(11, 'DESSIE REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(12, 'DIRE DAWA REGION', '2025-03-27 07:14:00', '2025-03-27 07:14:00'),
(13, 'GAMBELLA REGION', '2025-03-27 07:15:00', '2025-03-27 07:15:00'),
(14, 'GONDER REGION', '2025-03-27 07:15:00', '2025-03-27 07:15:00'),
(15, 'HARAR REGION', '2025-03-27 07:15:00', '2025-03-27 07:15:00'),
(16, 'Centeral Ethiopia Region', '2025-03-27 07:15:00', '2025-03-27 07:15:00'),
(17, 'JIMMA REGION', '2025-03-27 07:15:00', '2025-03-27 07:15:00'),
(18, 'MEKELE REGION', '2025-03-27 07:15:00', '2025-03-27 07:15:00'),
(19, 'METU REGION', '2025-03-27 07:16:00', '2025-03-27 07:16:00'),
(20, 'NEKEMT REGION', '2025-03-27 07:16:00', '2025-03-27 07:16:00'),
(21, 'SHASHEMENE REGION', '2025-03-27 07:16:00', '2025-03-27 07:16:00'),
(22, 'FINFINE REGION', '2025-03-27 07:16:00', '2025-03-27 07:16:00'),
(23, 'SHIRE REGION', '2025-03-27 07:16:00', '2025-03-27 07:16:00'),
(24, 'SIDAMA REGION', '2025-03-27 07:17:00', '2025-03-27 07:17:00'),
(25, 'JIJIGA REGION', '2025-03-27 07:17:00', '2025-03-27 07:17:00'),
(26, 'SOUTH WEST REGION', '2025-03-27 07:17:00', '2025-03-27 07:17:00'),
(27, 'WOLDIYA REGION', '2025-03-27 07:18:00', '2025-03-27 07:18:00'),
(28, 'W/SODO REGION', '2025-03-27 07:18:00', '2025-03-27 07:18:00'),
(29, 'EAAR', '2025-03-27 07:18:00', '2025-03-27 07:18:00'),
(30, 'NAAR', '2025-03-27 07:18:00', '2025-03-27 07:18:00'),
(31, 'SAAR', '2025-03-27 07:18:00', '2025-03-27 07:18:00'),
(32, 'WAAR', '2025-03-27 07:18:00', '2025-03-27 07:18:00');

INSERT INTO `substations` (`id`, `name`, `region_id`, `created_at`, `updated_at`) VALUES
(1, 'Adama', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(2, 'Gobesa', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(3, 'Mojo Mobaile', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(4, 'Nuraera', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(5, 'Adamitulu', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(6, 'Wonji', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(7, 'Asela', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(8, 'Metehara', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(9, 'Awash', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(10, 'Mojo New', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(11, 'Adama New', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(12, 'Mojo old', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(13, 'Koka Haydro Power', 1, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(14, 'semera  substation', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(15, 'Awash 7 kilo substation', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(16, 'Dicheto ', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(17, 'Amibara ', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(18, 'Hara mobile ', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(19, 'afdera ', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(20, 'Wukero', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(21, 'Adigrat', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(22, 'Mohonegen', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(23, 'Kombolcha', 2, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(24, 'Weliso', 3, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(25, 'Guder Mobile ', 3, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(26, 'Ginchi', 3, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(27, 'Gedo', 3, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(28, 'Hormat', 3, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(29, 'Bako', 3, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(30, 'A/minch ', 4, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(31, 'H/maryam ', 4, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(32, 'Key Afer ', 4, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(33, 'Sawla  ', 4, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(34, 'Bahir Dar-II', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(35, 'Dangila', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(36, 'Woreta', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(37, 'Bahir Dar-I', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(38, 'Pawie', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(39, 'D/Tabour', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(40, 'Nifas Mewucha', 5, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(41, 'Robe Substation', 6, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(42, 'yadot', 6, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(43, 'Rayitu', 6, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(44, 'Melkawakena', 6, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(45, 'ASSOSA', 7, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(46, 'G/BELES', 7, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(47, 'Hidassie', 7, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(48, 'Mendi', 7, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(49, 'Gimbi', 7, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(50, 'Nekemet', 7, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(51, 'Asebe Teferi -2', 8, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(52, 'Haremaya', 8, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(53, 'Chelenko', 8, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(54, 'Bedesa', 8, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(55, 'Debre_Birhan', 9, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(56, 'Debre_Birhan_2', 9, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(57, 'D/brehan shewarobit', 9, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(58, 'D/brehan  Alemketema', 9, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(59, 'D/brehan tulefa mobayl  substation', 9, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(60, 'Kemise substation ', 9, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(61, 'd/markos', 10, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(62, 'mota', 10, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(63, 'f/selam', 10, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(64, 'bichena', 10, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(65, 'Akesta', 11, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(66, 'Combolcha I', 11, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(67, 'Combolcha II', 11, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(68, 'Dessie', 11, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(69, 'Dire Dawa substation 1', 12, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(70, 'Dire Dawa substation 3', 12, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(71, 'waesela substation ', 12, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(72, 'HURSO substation', 12, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(73, 'adele substation', 12, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(74, 'Gambella1', 13, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(75, 'Gambella2', 13, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(76, 'Mizan_Teferi', 13, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(77, 'Tepi', 13, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(78, 'Azezo', 14, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(79, 'Lideta', 14, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(80, 'Dabat', 14, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(81, 'Metemma', 14, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(82, 'harar substation -III', 15, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(83, 'harar substation -II', 15, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(84, 'Wolkite substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(85, 'Hossana substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(86, 'Butajira substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(87, 'adami tullu substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(88, 'Warabe substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(89, 'Gilgel gibe substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(90, 'Halaba substation', 16, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(91, 'Jimma-1', 17, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(92, 'Gilgel-Gibe-II', 17, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(93, 'Gilgel-Gibe-I', 17, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(94, 'Jimma-2', 17, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(95, 'Agaro', 17, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(96, 'Mekele', 18, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(97, 'ABYI-ADI', 18, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(98, 'WUKRO', 18, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(99, 'Maychew', 18, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(100, 'Mekhoni', 18, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(101, 'Mettu 230kv', 19, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(102, 'Bedelle 132kv', 19, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(103, 'DIDESSA', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(104, 'GIDA', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(105, 'FINCHA', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(106, 'SHAMBU', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(107, 'NEDJO', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(108, 'GIDAMI', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(109, 'GHIMBI', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(110, 'DAMBI DOLO', 20, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(111, 'Shashemene', 21, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(112, 'Shakiso', 21, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(113, 'Bulehora', 21, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(114, 'Yabelo', 21, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(115, 'Bekulu Guma', 21, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(116, 'N/Borena', 21, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(117, 'Gefersa', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(118, 'Shegole', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(119, 'Holota Hidase', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(120, 'Muger', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(121, 'Ginchi', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(122, 'Anfo', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(123, 'Adis Alem', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(124, 'sebeta-I', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(125, 'Sebeta-II', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(126, 'Geja', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(127, 'Bui', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(128, 'Makanisa', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(129, 'Sululta', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(130, 'Bela', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(131, 'Chancho', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(132, 'G/Gurach', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(133, 'Fitche', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(134, 'Alem ketema', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(135, 'Tafo', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(136, 'Arabsa', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(137, 'Tulufa', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(138, 'D/birahane', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(139, 'Gelan', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(140, 'D/Zeit-1', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(141, 'D/Zeit-2', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(142, 'Dukam East', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(143, 'Dukam old', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(144, 'Dukam  New2', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(145, 'Dukam  New3', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(146, 'Dukam  New4', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(147, 'Gafat/Elalageda', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(148, 'Mojo', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(149, 'Kotebe', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(150, 'Kolfe', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(151, 'Abasamuiel', 22, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(152, 'adwa', 23, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(153, 'axum', 23, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(154, 'shire', 23, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(155, 'Hawassa No. 1', 24, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(156, 'Hawassa No. 2', 24, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(157, 'Yirgalem 1', 24, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(158, 'Yirgalem 2 /Wara/', 24, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(159, 'jigjiga', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(160, 'Adigala', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(161, 'dhagahbur', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(162, 'Fik', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(163, 'Gode', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(164, 'Kebridahar', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(165, 'Diridawa', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(166, 'Negele', 25, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(167, 'Bonga', 26, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(168, 'Mizan', 26, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(169, 'Aba', 26, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(170, 'Tum_Solar', 26, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(171, 'Dorogbir', 27, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(172, 'Gashena', 27, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(173, 'Lalibela', 27, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(174, 'Sekota', 27, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(175, 'Woldia Old', 27, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(176, 'Alamata', 27, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(177, 'Wolayita _Sodo_Old', 28, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(178, 'Wolayita_Soddo_New', 28, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(179, 'Dilla', 28, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(180, 'Hagere Mariam', 28, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(181, 'ADDIS EAST', 29, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(182, 'BOLE LEMI', 29, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(183, 'COTOBIE', 29, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(184, 'LEGETAFO', 29, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(185, 'WEREGENU', 29, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(186, 'NIFAS SILK', 29, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(187, 'ADDISU GEBEYA', 30, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(188, 'ADDIS WEST', 30, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(189, 'ADDIS NORTH', 30, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(190, 'BLACK LION', 30, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(191, 'AKAKI', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(192, 'GELAN', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(193, 'GOFA', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(194, 'KALITI', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(195, 'KALITI-II', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(196, 'KALITI-I', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(197, 'KALITI-N', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(198, 'KOYE ABO', 31, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(199, 'ADDIS CENTER', 32, '2025-03-27 07:13:00', '2025-03-27 07:13:00');

INSERT INTO `feeder_lines` (`id`, `name`, `substation_id`, `voltage_level_kv`, `length`, `number_of_transformers`, `number_of_customers`, `peak_load_a`, `peak_load_mw`, `created_at`, `updated_at`) VALUES
(1, 'LINE1', 1, 15, 34.71, 115, 1000, 8.6, 8.6, '2025-03-27 07:13:00', '2025-04-25 22:42:39'),
(2, 'LINE3', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(3, 'LINE4', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(4, 'LINE5', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(5, 'LINE6', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(6, 'LINE7', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(7, 'MOBILE1', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(8, 'MOBILE2', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(9, 'MOBILE3', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(10, 'MOBILE4', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(11, 'MOBILE5', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(12, 'MOBILE6', 1, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(13, 'GOBESA', 2, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(14, 'ROBE', 2, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(15, 'BEKOJI', 2, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(16, 'LINE2', 3, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(17, 'LINE3', 3, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(18, 'LINE4', 3, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(19, 'CHOLE HO2', 4, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(20, 'ISRAEL HO3', 4, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(21, 'ABOMSA JO3', 4, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(22, 'METEHRA JO4', 4, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(23, 'MERTI JO6', 4, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(24, 'ZEWAY', 5, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(25, 'MEKI', 5, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(26, 'BULBULA', 5, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(27, 'COSATIC', 5, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(28, 'INDUSTRY', 5, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(29, 'KORME', 5, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(30, 'MEKI ALEMTENA ', 5, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(31, 'LINE1', 6, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(32, 'LINE2', 6, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(33, 'LINE3', 6, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(34, 'LINE4', 6, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(35, 'LINE5', 6, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(36, 'ETEYA ', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(37, 'SAGURE ', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(38, 'INDUSTRY ', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(39, 'ASELA', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(40, 'ASHEBEKA', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(41, 'CHUPUED FACTORY', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(42, 'ARSI UNIVERISTY', 7, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(43, 'GUMGUMA', 7, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(44, 'MALT FACTORY', 7, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(45, 'AMUDE', 7, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(46, 'METEHARA', 8, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(47, 'METEBLA', 8, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(48, 'AWASH PARK', 8, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(49, 'SUGER FACTORY', 8, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(50, 'F1', 9, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(51, 'F2', 9, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(52, 'F3', 9, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(53, 'F4', 9, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(54, 'F5', 9, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(55, 'ARBOYE', 9, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(56, 'INDUSTRY  SUNSHINE', 9, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(57, 'INDUSTRY  ANTENXS', 9, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(58, 'LINE 1/15KV', 10, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(59, 'LINE 8/15KV', 10, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(60, 'LINE 1/33KV', 10, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(61, 'ANBESA BEER ', 10, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(62, 'K 2', 11, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(63, 'K-1', 11, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(64, 'LINE-2', 12, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(65, 'LINE-3', 12, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(66, 'LINE-4', 12, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(67, 'Argisa', 44, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(68, 'Palsa', 13, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(69, 'Adama Water', 13, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(70, 'Logya', 14, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(71, 'Mille', 14, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(72, 'Semera', 14, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(73, 'Asayta', 14, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(74, 'Dubti', 14, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(75, 'Semera Industry Park', 14, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(76, 'Awash 40', 15, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(77, 'Awash7', 15, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(78, 'factory', 15, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(79, 'Dicheto ', 16, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(80, 'Serdo', 16, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(81, 'Sabure', 17, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(82, 'Melka werer', 17, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(83, 'Melkasedi', 17, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(84, 'Ambash bonta', 17, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(85, 'Gewane', 17, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(86, 'Telalak', 60, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(87, 'Sununta', 18, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(88, 'afdera', 19, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(89, 'lugeda  h4', 20, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(90, 'Kunba/atibe H3', 20, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(91, 'Abeala/ko -2 3', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(92, 'Abeala/delefe -3', 96, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(93, 'Adegerat ', 21, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(94, 'megeale', 22, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(95, 'Bati/Bureka', 23, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(96, 'Alemata/kelewon', 176, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(97, 'Weliso', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(98, 'Tulu Bolo', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(99, 'Ameya', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(100, 'Goro', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(101, 'Acheber', 24, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(102, 'Kota', 24, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(103, 'Busa', 24, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(104, 'Guder Town', 25, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(105, 'Ambo Town', 25, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(106, 'Ambo New', 25, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(107, 'Ambo Water', 25, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(108, 'Dire Inchini', 25, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(109, 'Olonkomi', 26, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(110, 'Ginchi', 26, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(111, 'Jeldu', 26, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(112, 'Animol', 26, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(113, 'Boda', 26, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(114, 'Elfeta', 26, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(115, 'Gedo', 27, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(116, 'Ijaji', 27, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(117, 'Babich', 27, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(118, 'Wayu', 27, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(119, 'Seyo', 27, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(120, 'Mida ', 27, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(121, 'Tulu Nacha', 27, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(122, 'Addis Alem ', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(123, 'Ehud Gebya', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(124, 'Wereda 2&3', 28, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(125, 'Wereda 4&5', 28, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(126, 'Ketema Limat', 28, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(127, 'Factory 2', 28, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(128, 'Shenen', 28, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(129, 'Bako Town', 29, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(130, 'Gudeya Bila', 29, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(131, 'Tibe', 29, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(132, 'Bilo Boshe', 29, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(133, 'Gindeberet', 120, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(134, ' Sikela Feder', 30, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(135, 'universty Feder', 30, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(136, 'Txtail Feder', 30, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(137, 'Secha Feder ', 30, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(138, 'Gidole  Feder', 30, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(139, 'Chencha Feder', 30, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(140, 'Boroda Feder', 30, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(141, 'Kanba Feder', 30, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(142, ' Burji Feder', 31, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(143, 'jinka Feder', 32, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(144, 'dimeka Feder', 32, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(145, 'Woyto Feder', 32, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(146, 'sawula Feder2', 33, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(147, 'Bulke Feder1', 33, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(148, 'Selamber Feder3', 33, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(149, 'Abay_Mado_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(150, 'Addis_Qidam_15kV', 35, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(151, 'Addis_Zemen_15kV', 36, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(152, 'Adet_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(153, 'Air_Force_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(154, 'Alember_33kV', 36, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(155, 'Amhara_Metal_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(156, 'Arsa_Gembeha_33kV', 35, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(157, 'Bata_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(158, 'Bete_mengist_15kV', 37, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(159, 'Chagni_15kV', 38, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(160, 'Dangla_15kV', 35, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(161, 'Mahider Mariam -15kV ', 39, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(162, 'Debre_Tabour_15kV', 39, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(163, 'Debre_Tabour_University_15kV', 39, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(164, 'Durbete_15kV', 35, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(165, 'Ebnat_33kV', 36, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(166, 'Enjibara University-15kV', 35, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(167, 'Gasay_33kV', 40, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(168, 'Ghion_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(169, 'Gish_Abay_33kV', 35, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(170, 'Gonji_33kV', 34, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(171, 'Hamusit_15kV', 37, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(172, 'Industry_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(173, 'Industry_Park_33kV', 34, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(174, 'Jawi_33kV', 38, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(175, 'Kosober_15kV', 35, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(176, 'Kuancha_kuakurta_33kV', 35, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(177, 'Lideta_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(178, 'Merawi_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(179, 'Merawi_Kunzila_33kV', 35, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(180, 'MSA_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(181, 'Nifas_Mewucha_33kV', 40, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(182, 'Papyrus_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(183, 'Rava_Steel_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(184, 'Sebatamit_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(185, 'Tach_Gaint_33kV', 40, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(186, 'Textile_Boiler_15kV', 37, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(187, 'Woreta_15kV', 36, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(188, 'Woreta_International_15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(189, 'Zigem_33kV', 38, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(190, 'Merawi-II 15kV', 34, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(191, 'Goba', 41, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(192, 'university', 41, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(193, 'Agarfa', 41, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(194, 'Sinana', 41, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(195, 'Meliyu', 41, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(196, 'Gassera', 41, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(197, 'line 1', 42, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(198, 'line 2', 42, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(199, 'line 3', 42, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(200, 'Ginnir', 43, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(201, 'Raytu', 43, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(202, 'Bera Dimtu', 43, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(203, 'Adaba Dodola', 44, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(204, 'Hako', 44, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(205, 'Dodola', 44, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(206, 'Assosa university', 45, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(207, 'Assosa ketema feeder', 45, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(208, 'Amba 14', 45, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(209, 'Bambasi Feeder', 45, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(210, 'kurmuk feeder', 45, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(211, 'Abramo', 45, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(212, 'Pawi feeder', 46, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(213, 'Chagni feeder', 46, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(214, 'Dibate feeder', 46, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(215, 'Beles feeder', 46, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(216, 'Jawi feeder', 46, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(217, 'Zigem feeder', 46, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(218, 'Mankush ', 46, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(219, 'Mankush', 47, 20, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(220, 'Dalati', 48, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(221, 'Bikilal/kamashi', 49, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(222, 'Sasiga/Uke', 50, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(223, ' MIEASO/15', 51, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(224, ' ODA BULTUM  /15', 51, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(225, ' CHIRO/15', 51, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(226, ' HIRNA/15', 51, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(227, ' KORA/33', 51, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(228, 'MESELA/33', 51, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(229, 'BATI/15', 52, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(230, 'UNIVERSITY/15', 52, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(231, 'HAREMYA/15', 52, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(232, 'HAKA/33', 52, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(233, 'GIRAWA/33', 52, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(234, 'HARER WATER/33', 52, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(235, 'KERSA/15', 53, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(236, 'DEDER/15', 53, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(237, 'CHELENKO/15', 53, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(238, 'MUTI/33', 53, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(239, 'JAJA/33', 53, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(240, 'CHELENKO LOLA/33', 53, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(241, 'BEDESA/15', 54, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(242, 'GELEMSO/15', 54, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(243, 'MECHARA/33', 54, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(244, 'SORORO/33', 54, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(245, 'KOMBOLCHA/15', 82, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(246, 'AWEDEY/15', 83, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(247, 'Dashen_Beer 33kv', 55, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(248, 'Sheno 15kv', 55, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(249, 'Mendida_15_KV', 55, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(250, 'Ankober_15_KV', 55, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(251, 'Habesha_beer 33kv', 55, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(252, 'seladingay 33kv', 55, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(253, 'Blanket_Factory_15_KV', 55, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(254, 'Rorank 33kv', 55, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(255, 'Aliyu_Amba 33kv', 55, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(256, 'K3_Mendida(deneba)_15KV', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(257, 'K2_Debre_Berhan_15KV', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(258, 'k6 shno 15Kv', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(259, 'K11 Amhara textile 15kv', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(260, 'K13_Zamu pls_15kv', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(261, 'K15_oneway_textile_factory 15kv', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(262, 'H10 data maying xdmt 33kv', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(263, 'K12 date center  hebi 15kv', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(264, 'K14 data center  hebi 15kv', 56, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(265, 'HO2_Debre_Berhan_33KV', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(266, 'HO3_Enewari_33KV', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(267, 'HO4_sheno(Hageremariam) 33KV', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(268, 'HO5_kingminchain plc 33kv', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(269, 'HO6 Dega water 33kv', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(270, 'HO11_Juniper_Fabrica_33KV', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(271, 'HO13  bortmalt plc 33kv', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(272, 'HO14 D/brehan industry park 33kv', 56, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(273, 'Robit 15kv', 57, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(274, 'Merye 15kv', 57, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(275, 'Mehalmeda 33kv', 57, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(276, 'Debresina 15kv', 57, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(277, 'Ataye 33kv', 57, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(278, 'Ataye 15kv', 57, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(279, 'Alemketema 33kv', 58, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(280, 'Fetera 33kv ', 58, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(281, 'Meranga 33kv', 58, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(282, 'Line1 jerre oil 33kv', 59, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(283, 'LIne2  33kv', 59, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(284, 'LIne4  33kv', 59, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(285, 'LIne5  33kv', 59, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(286, 'Line 4  mnjar 33kv', 148, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(287, 'Arerti industri Park 33kv', 148, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(288, 'Metebla 15kv', 8, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(289, 'Mekuye 33kv', 60, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(290, 'Industry', 61, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(291, 'Waber', 61, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(292, 'Guay', 61, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(293, 'M/wash', 61, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(294, 'Amanuael', 61, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(295, 'Lumame', 61, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(296, 'D/markos 3', 61, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(297, 'D/markos 4', 61, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(298, 'Water', 61, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(299, 'Univerity', 61, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(300, 'Motta', 62, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(301, 'Mertolemariam', 62, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(302, 'Asteryo', 62, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(303, 'FelegeBirhan', 62, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(304, 'F/Selam', 63, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(305, 'Burie', 63, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(306, 'Burie industry', 63, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(307, 'Birsheleko', 63, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(308, 'Denebecha', 63, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(309, 'Feresbet', 63, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(310, 'Quarit', 63, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(311, 'Abasem', 63, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(312, 'Bichena', 64, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(313, 'Debrework', 64, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(314, 'Midroc', 64, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(315, 'Dejen', 64, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(316, 'Kuy', 64, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(317, 'Telima', 64, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(318, 'Tik', 64, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(319, 'Mekane Selam /Line-1/ 33kV', 65, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(320, 'Tenta /Line-2/ 33kV', 65, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(321, 'Woreilu /Line-3/33kV', 65, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(322, 'Akasta /Line-4/33kv', 65, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(323, 'Sayint/Line-5/ 33KV', 65, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(324, 'Tuluawulia university/Line6/ 33KV', 65, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(325, 'Kombolicha Line-1 15 kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(326, 'Bati Line-2 15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(327, 'Kombolicha Line-3 15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(328, 'Harbu University Line-4 15KV', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(329, 'BGI Line-5 15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(330, 'Textile Line-6 15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(331, 'Textile Line-7 15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(332, 'Kombolicha Line-8 15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(333, 'ET-wood(Line-10)', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(334, 'Amhatem line-11-15kv', 66, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(335, 'Bati Line-1 33kv', 67, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(336, 'Mekenity /Line-2/ 33kv', 67, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(337, 'Industry Zone Line-3 33kv', 67, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(338, 'Industry Zone Line 4 33kv', 67, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(339, 'Carvico Line -5 33kV', 67, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(340, 'Dessie Line-1 -15kV', 68, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(341, 'Dessie Line-2 -15kV', 68, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(342, 'Robit Line-1 -15kV', 68, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(343, 'Robit Line-2 -15kV', 68, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(344, 'Bokekssa line-5 -33kV', 68, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(345, 'Arera   Line-1', 68, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(346, 'Kemissie Line-1 15kv', 60, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(347, 'Line - 2 -(Afare-33KV)', 60, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(348, 'Chifra Roba Line-3 33kv', 60, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(349, 'Mekoy Line-4 33kv', 60, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(350, 'Kemissie Line-1(Industry village) -15kv', 60, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(351, 'feeder 1', 69, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(352, 'feeder 2', 69, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(353, 'feeder 3', 69, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(354, 'feeder 4', 69, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(355, 'feeder 5', 69, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(356, 'feeder 6', 69, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(357, 'feeder1', 70, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(358, 'LINE 1', 70, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(359, 'line 2', 70, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(360, 'line 3', 70, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(361, 'Dire Dawa water', 71, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(362, 'Dire Dawa motor ', 71, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(363, 'Dire steel', 71, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(364, 'Feeder 1 ', 72, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(365, 'industry line 1', 72, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(366, 'industry line 2', 72, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(367, 'Haka line(feeder 1)', 73, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(368, 'Gebeya_15_KV', 74, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(369, 'Abobo_15_KV', 74, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(370, 'Etang_15_KV', 74, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(371, 'Baro_Mado_15_KV', 74, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(372, 'Gambella_230KV', 75, 230, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(373, 'Gambella_university_33KV', 75, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(374, 'Gnignang', 75, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(375, 'PUGNDO', 75, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(376, 'Dima', 76, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(377, 'Bechefa', 77, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(378, 'Tepi_15_KV', 77, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(379, 'Airport_33 kv', 75, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(380, 'Gambella_66 kv', 75, 66, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(381, 'Dembi dolo_66 kv', 74, 66, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(382, 'Tepi_66 kv', 168, 66, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(383, 'Azezo', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(384, 'Gondar', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(385, 'Angereb', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(386, 'Dmaza', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(387, 'Maksegnit', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(388, 'Tana', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(389, 'Gondar wuha/dedicated', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(390, 'Gondar University/dedicated', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(391, 'Dashen/dedicated ', 78, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(392, 'Chilga', 78, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(393, 'Arbaya', 78, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(394, 'Delgi', 78, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(395, 'Gorgora', 78, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(396, 'Sanja', 78, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(397, 'T/Dingay', 79, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(398, 'Arada', 79, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(399, 'Fasil', 79, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(400, 'Hospital/dedicated', 79, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(401, 'A/giorgis', 80, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(402, 'Dabat', 80, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(403, 'Debark', 80, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(404, 'Janamora', 80, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(405, 'Debark university/dedicated', 80, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(406, 'Shehedi', 81, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(407, 'Shinfa', 81, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(408, 'Negade Bahir', 81, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(409, 'M.Yuhanes', 81, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(410, 'new feeder two (HR3-F2) Bisidimo', 82, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(411, 'new feeder four (HR3-F4) harar beer', 82, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(412, 'new feeder five (HR3-F5)', 82, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(413, 'Industry', 82, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(414, 'Harar water', 82, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(415, 'fedis', 82, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(416, 'Bedassa', 82, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(417, 'Harar old two (HR2-F2)', 83, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(418, 'Harar old Three (HR2-F3)', 83, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(419, 'Harar old four (HR2-F4)', 83, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(420, 'Harar old six (HR2-F6) dire teyara', 83, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(421, 'L1-wolkite feeder', 84, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(422, 'L2-Endbir-15kv', 84, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(423, 'Industry ', 84, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(424, 'L4-UNiVEERSITY Feeder', 84, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(425, 'L3-Agena', 84, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(426, 'L1-gunchire feeder', 84, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(427, 'L2-Endbir-33kv', 84, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(428, 'L4-wayyu natri', 84, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(429, 'L5-zebider feeder', 84, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(430, 'L1-Angacha feeder', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(431, 'L2-Gimbichu feeder', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(432, 'L3-Lera feeder', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(433, 'L4-Hossana 15kv feeder', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(434, 'L5-Fonko 15 feeder', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(435, 'L6-Univeersity', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(436, 'L7-Heto', 85, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(437, 'L1-Balessa feeder', 85, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(438, 'L2-Wasgebeta feeder', 85, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(439, 'L3-Hayse feeder', 85, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(440, 'L1-Butajira 15kv feeder', 86, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(441, 'L2-Industry 15kv feeder ', 86, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(442, 'L3-Kella feeder', 86, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(443, 'L1-Worabe 33kv feeder', 86, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(444, 'L2-Wita 33kv feeder', 86, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(445, 'Butajira 15kv ', 87, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(446, 'Shelewasho', 87, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(447, 'warabe feeder', 88, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(448, 'dalocha feeder', 88, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(449, 'Buee 15kv', 127, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(450, 'kella 15kv', 127, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(451, 'kella 33kv', 127, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(452, 'Tarre 15 kv', 127, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(453, 'tarre 33kv', 127, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(454, 'sokoru', 89, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(455, 'Line-1 (shishincho)', 90, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(456, 'Line-5 (durame wuha) ', 90, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(457, 'LIMMU(33KV)', 91, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(458, 'SHEBE SOMBO(33KV)', 91, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(459, 'INDUSTRY PARK (33KV)', 91, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(460, 'OLD CITY (15KV)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(461, 'KETO FURIDESA15KV)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(462, 'AGRI (R1-G1, 15KV) INCOMING', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(463, 'R1-G3(SEKA)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(464, 'R1-G4(MERKATO)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(465, 'R1-G5(SARIS)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(466, 'CITY (R2-G1 ,15KV)  INCOMING', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(467, 'R2-G2(DEDO)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(468, 'R2-G3(MUZIEM)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(469, 'R2-G4(YETABABARUT)', 91, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(470, 'OMONADA (33KV)', 92, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(471, 'SOKORU (L4) (15KV)', 93, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(472, 'DAM &| INTAKE (L1) (15KV)', 93, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(473, 'DENEBA (15KV)', 93, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(474, 'CAMP (L3)  (15KV)', 93, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(475, 'WATER PUMP  (15KV)', 94, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(476, '(R3-G6) INCOMING (15KV)', 94, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00');
INSERT INTO `feeder_lines` (`id`, `name`, `substation_id`, `voltage_level_kv`, `length`, `number_of_transformers`, `number_of_customers`, `peak_load_a`, `peak_load_mw`, `created_at`, `updated_at`) VALUES
(477, 'R3-G2(KOCI)', 94, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(478, 'R3-L4', 94, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(479, 'SERBO   (15KV)', 94, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(480, 'JU(L4)  (15KV)', 94, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(481, 'TOBA & DAMBI', 95, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(482, 'GERA(33KV)', 95, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(483, 'OMO GURUDE(33KV)', 95, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(484, 'GATIRA (33KV)', 95, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(485, 'AGARO (15KV) ', 95, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(486, 'GEMBE  (15KV)', 95, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(487, 'K000', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(488, 'K02', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(489, 'K03', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(490, 'K04', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(491, 'K05', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(492, 'K06', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(493, 'K08', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(494, 'K09', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(495, 'K10', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(496, 'K011', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(497, 'K012', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(498, 'R2', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(499, 'R3', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(500, 'R4', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(501, 'R5', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(502, 'R6', 96, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(503, 'DLF_1', 96, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(504, 'DLF_2', 96, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(505, 'DLF_3', 96, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(506, 'DLF_4', 96, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(507, 'L2', 97, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(508, 'L3', 97, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(509, 'L4', 97, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(510, 'L6', 97, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(511, 'K01', 98, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(512, 'K02', 98, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(513, 'K03', 98, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(514, 'K04/FEEDER/', 98, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(515, 'K05', 98, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(516, 'K06', 98, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(517, 'H1/FEEDER/', 98, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(518, 'H2', 98, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(519, 'H3', 98, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(520, 'H4', 98, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(521, 'K05', 99, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(522, 'K02', 99, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(523, 'K03', 99, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(524, 'K04', 99, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(525, 'K01', 21, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(526, 'K02', 21, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(527, 'K03', 21, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(528, 'K04', 21, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(529, 'K05', 21, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(530, 'P1', 21, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(531, 'P2', 21, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(532, 'P4', 21, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(533, 'P5', 21, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(534, 'L-1', 100, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(535, 'L-2', 100, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(536, 'L-3', 100, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(537, 'L-4', 100, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(538, 'L-5', 100, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(539, 'L-1', 100, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(540, 'L-2', 100, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(541, 'L-3', 100, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(542, 'L-4', 100, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(543, 'L-5', 100, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(544, 'Mettu Town Feeder ', 101, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(545, 'Gore Feeder', 101, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(546, 'Yayo  Feeder ', 101, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(547, 'Suphe Feeder', 101, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(548, 'Mettu University ', 101, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(549, 'Gobe Feeder', 101, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(550, 'Masha Feeder', 101, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(551, 'Leka Feeder', 101, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(552, 'Bedelle Town Feeder', 102, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(553, 'Gechi', 102, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(554, 'Alhabash Feeder', 102, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(555, 'Bedelle Beer Factory', 102, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(556, 'Chora Feeder', 102, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(557, 'Chewaka Feeder', 102, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(558, 'Degga Feeder', 102, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(559, 'kollo Sirri Feeder', 102, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(560, 'HOSPITAL', 50, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(561, 'BAKE JAMA', 50, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(562, 'TELE', 50, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(563, 'WOLLEGA', 50, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(564, 'ARJO AWURAJA', 50, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(565, 'NUNU', 50, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(566, 'BONEYA', 50, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(567, 'UKE', 50, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(568, 'ARJO GUDETU', 103, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(569, 'TOLE', 103, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(570, 'GUTIN', 104, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(571, 'KIRAMU', 104, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(572, 'GIDA', 104, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(573, 'FINCHA', 105, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(574, 'KOMBOLCHA', 105, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(575, 'DEDU', 105, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(576, 'SHAMBU', 105, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(577, 'HORO', 105, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(578, 'SHAMBU', 106, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(579, 'ALIBO', 106, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(580, 'NEDJO', 107, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(581, 'JARSO', 107, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(582, 'MENDI', 48, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(583, 'BONI', 48, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(584, 'DALATI', 48, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(585, 'KILTU KARA', 48, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(586, 'BEGI', 108, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(587, 'GIDAMI', 108, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(588, 'GHIMBI', 109, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(589, 'INANGO', 109, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(590, 'GUY', 109, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(591, 'CHINA', 109, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(592, 'GENJI', 109, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(593, 'GELEL', 109, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(594, 'GULISO', 109, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(595, 'BIKILAL', 109, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(596, 'DAMBI DOLO', 110, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(597, 'MUGI', 110, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(598, 'ROBGEBIYA', 110, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(599, 'MACHARA', 110, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(600, 'DAMBI DOLO UNIVERSITY', 110, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(601, 'L-1 -Wondo', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(602, 'L-2- Alaba', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(603, 'L-3.1- Old Negele', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(604, 'L-3.2- New Negele', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(605, 'L-4 Water Pump', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(606, 'L-5 Kofele', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(607, 'L-6 -Aje', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(608, 'L-7- Selam Wuha', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(609, 'L-8 Wuha Limat', 111, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(610, 'L-1 Meti', 111, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(611, 'L-2 Ropi', 111, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(612, 'L-3 Worka', 111, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(613, 'L1- Shakiso ', 112, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(614, 'L2- Kibremengist ', 112, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(615, 'L7 - Lagadembi ', 112, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(616, 'L8-  Lagadembi', 112, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(617, 'L9 - Lagadembi', 112, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(618, 'L1 -Water pump', 112, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(619, 'L2 - Sakaru', 112, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(620, 'L3 - Aroresa', 112, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(621, 'L4 - Kenticha', 112, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(622, 'Line 1- Bule Hora university', 113, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(623, 'Line 2- Burji', 113, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(624, 'Line 3- Bule Hora', 113, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(625, 'Line 4 - Chelelektu', 113, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(626, 'Line 1- arero', 114, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(627, 'Line 2 - teltale', 114, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(628, 'Line 3 - yabelo', 114, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(629, 'line 1- mega ', 115, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(630, 'line 2- moyale', 115, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(631, 'line 3- kenya', 115, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(632, 'Line3 N/Borena', 116, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(633, 'Line4 Industry', 116, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(634, 'Line2 Filtu', 116, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(635, 'Line4 Wadera', 116, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(636, 'Line5 Bidre', 116, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(637, 'Line-1', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(638, 'Line-2', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(639, 'Line-3', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(640, 'Line-4', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(641, 'Line-11', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(642, 'Line-12', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(643, 'Line-13', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(644, 'Line-14', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(645, 'Line-8', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(646, 'Line-10', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(647, 'Line-15', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(648, 'Line-16', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(649, 'Line-9', 117, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(650, 'Line-4', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(651, 'Line-5', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(652, 'KO3', 119, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(653, 'KO6', 119, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(654, 'Line-3', 119, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(655, 'G2(Beyo gidi)', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(656, 'F3(Inchine)', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(657, 'Olonkomi', 120, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(658, 'Line-1(Muger Cement Factory)', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(659, 'Line-2(Mekoda)', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(660, 'Bedrock Cement', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(661, 'Habesh', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(662, 'Gendeberet', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(663, 'Line-2(Muger Cement Factory)', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(664, 'Line-3(Muger Cement Factory)', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(665, 'Muger Bus', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(666, 'Bedrock Cement', 120, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(667, 'Animol paper Factory', 121, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(668, 'Boda', 121, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(669, 'Ginchi', 121, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(670, 'Jeldu', 121, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(671, 'Line-1', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(672, 'Line-2', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(673, 'Line-3', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(674, 'Line-4', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(675, 'Line-5', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(676, 'Line-6', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(677, 'Line-1', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(678, 'Line-2', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(679, 'Line-5', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(680, 'Addis Alem', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(681, 'Ehud Gebeya', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(682, 'Flower', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(683, 'Habesha Cement', 123, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(684, 'Line-2', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(685, 'Line 4', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(686, 'Line-5', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(687, 'Line-6', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(688, 'Line-7', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(689, 'Line-9', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(690, 'Line-11', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(691, 'Line-12', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(692, 'Line-13', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(693, 'Line-1', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(694, 'Line-3', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(695, 'Line-14', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(696, 'Line 4', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(697, 'Line-2', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(698, 'Line-5', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(699, 'Line-6', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(700, 'Line-7', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(701, 'Line-1', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(702, 'Line-3', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(703, 'Line-8', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(704, 'Line-9', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(705, 'Line-12', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(706, 'Line-13', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(707, 'Line-15', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(708, 'Line-14', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(709, 'Line-11', 125, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(710, 'Achber', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(711, 'Busa', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(712, 'Goru', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(713, 'Kota', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(714, 'Tulu Bolo', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(715, 'woliso', 24, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(716, 'Awash', 126, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(717, 'Hora', 126, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(718, 'Radio Station', 126, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(719, 'Tere', 127, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(720, 'Tere', 127, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(721, 'Lemen', 127, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(722, 'Line-5', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(723, 'Line-6', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(724, 'K4', 196, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(725, 'K13', 196, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(726, 'Line-1', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(727, 'Line-2', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(728, 'Line-3', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(729, 'Line-4', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(730, 'Line-5', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(731, 'Line-6', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(732, 'Line-7', 129, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(733, 'Line-9', 129, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(734, 'Line-10', 129, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(735, 'Bela-4', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(736, 'Line-1', 131, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(737, 'Line-2', 131, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(738, 'Line-3', 131, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(739, 'Line-4', 131, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(740, 'Line-7', 131, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(741, 'Line-8', 131, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(742, 'Line-1(Mukturi & Ejere)', 132, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(743, 'Line-2(Gebire Gurach)', 132, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(744, 'Line-3(Gebire Gurach)', 132, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(745, 'Line-4(Gohastion)', 132, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(746, 'Line-5(cemente)', 132, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(747, 'Fitche', 132, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(748, 'Fitche', 133, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(749, 'comando', 133, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(750, 'D/Libanoce', 133, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(751, 'Mida Line-1', 134, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(752, 'Line-1', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(753, 'Line-2', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(754, 'Line-3', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(755, 'Line-9', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(756, 'Line-10', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(757, 'Tafo-33kv', 184, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(758, 'Tafo 2', 135, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(759, 'Line 14', 136, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(760, 'Line-14', 182, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(761, 'line-5', 137, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(762, 'HO4 (Sheno)', 138, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(763, 'K6 (Sheno)', 138, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(764, 'Ko3 (Mendida)', 138, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(765, 'Ho3 (Enwary)', 138, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(766, 'Line-1', 139, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(767, 'Line-3', 139, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(768, 'Line-3', 139, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(769, 'Line-5', 139, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(770, 'Line-6', 139, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(771, 'Line-8', 139, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(772, 'Line-9', 139, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(773, 'Line-7 (ARTI)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(774, 'Line-2(kiya water)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(775, 'Line-3(Beny)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(776, 'Line-4(Tadash)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(777, 'Line-5(Tadash)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(778, 'Line-8(kam seramil)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(779, 'Line-9(sent Gell)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(780, 'Line-10(Absyina)', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(781, 'Line-6', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(782, 'Line-7', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(783, 'HO1', 140, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(784, 'HO3', 140, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(785, 'HO5', 140, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(786, 'HO6', 140, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(787, 'HO8', 140, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(788, 'Line-2', 142, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(789, 'Line-4', 142, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(790, 'Line-1 (Industury)', 143, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(791, 'Line-2(Alziar)', 143, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(792, 'Line-3', 143, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(793, 'Dukem', 143, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(794, 'Line-1', 144, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(795, 'Line-2', 145, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(796, 'Line-3(Habesh)', 146, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(797, 'Line-1', 140, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(798, 'Line-2', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(799, 'k7', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(800, 'k8', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(801, 'k9', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(802, 'k14', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(803, 'k15', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(804, 'k12(Absinia steel)', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(805, 'k13(Absinia steel )', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(806, 'k1(steel RMI)', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(807, 'k2(steel RMI)', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(808, 'k6(steel RMI)', 141, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(809, 'Line-1', 147, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(810, 'Line-2', 147, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(811, 'Line-3', 147, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(812, 'Line-4', 147, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(813, 'Line 2', 148, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(814, 'Line 3', 148, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(815, 'Line 3', 149, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(816, 'Addis West', 150, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(817, 'Abasamuiel', 151, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(818, 'P2', 152, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(819, 'P5', 152, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(820, 'Ko8', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(821, 'ko7', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(822, 'L8', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(823, 'ko4', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(824, 'ko2', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(825, 'K04', 153, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(826, 'K06', 153, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(827, 'Ko4', 154, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(828, 'KO3', 154, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(829, 'Ko2', 154, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(830, 'P4', 154, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(831, 'P2', 154, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(832, 'P3', 154, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(833, 'l5', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(834, 'L2', 152, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(835, 'F 1 / R 1', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(836, 'F 3 / R 3', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(837, 'F 4 / R 4', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(838, 'F 5 / R 5', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(839, 'F 7 / R 7', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(840, 'Mesmer 1', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(841, 'Mesmer 7', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(842, 'Mesmer 9', 155, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(843, 'Mesmer 10', 155, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(844, 'Mesmer 11', 155, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(845, 'Mesmer 12', 155, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(846, 'F 3 / R 3', 156, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(847, 'F 6 / R 6', 156, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(848, 'Mesmer 19', 156, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(849, 'Mesmer 20 ', 156, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(850, 'Agansa RMU (F3)', 156, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(851, 'Maryam RMU(F4)', 156, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(852, 'Feeder 2/YIRGALEM & LEKU', 157, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(853, 'Feeder 1/ APOSTO ', 157, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(854, 'Feeder 4/DAYE RMU ', 157, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(855, 'Feeder 5/ FURA CHANCHO ', 157, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(856, 'Feeder 1/ Aleta Wendo & Hagereselam', 158, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(857, 'Feeder 2/ Chuko & Kebado', 158, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(858, 'Feeder 3/ YIRGALEM IPDC ', 158, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(859, 'F- 4 ', 159, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(860, 'F-2', 159, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(861, 'Ayshe,a F-1', 160, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(862, 'F 4 / chineksene', 159, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(863, 'dhagahbur', 161, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(864, 'eglole', 161, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(865, 'Fafen F-1', 159, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(866, 'dendemo', 162, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(867, 'Hamaro', 162, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(868, 'fik town', 162, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(869, 'adadle', 163, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(870, 'Hadawe', 163, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(871, 'Gode', 163, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(872, 'F-3 kebribayah', 159, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(873, 'F-2 kebridahar', 164, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(874, 'F-3 jijiga Kilil', 159, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(875, 'F2 -Kulen', 160, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(876, 'F-4 kulen', 160, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(877, 'F- 5/kulen ', 160, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(878, 'F-3/ kulen ', 160, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(879, 'F-1 1 /jijiga', 159, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(880, 'Line4/ erer ', 72, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(881, 'R4G1', 165, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(882, 'shekosh', 161, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(883, 'Shilabo', 164, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(884, 'F-2 (wechale)', 159, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(885, 'werdher', 164, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(886, 'Filtu', 166, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(887, 'Chiri ( 15 KV )', 167, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(888, 'Wushwush ( 15 KV ', 167, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(889, 'Bonga ( 15 KV )', 167, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(890, 'Gimbo ( 33 KV )', 167, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(891, 'Mera Tello ( 33 KV ', 167, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(892, 'University ( 33 KV ', 167, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(893, 'Sheko ( 15 KV )', 168, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(894, 'Mizan ( 15 KV )', 168, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(895, 'Aman( 15 KV )', 168, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(896, 'Chena 1 ( 33 KV )', 168, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(897, 'Dima ( 33 KV )', 168, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(898, 'Chena 2( 33 KV )', 168, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(899, 'Tepi (15kV)', 77, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(900, 'Gezemeret (33 KV)', 77, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(901, 'Fede  (15kV)', 77, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(902, 'Gecha (15kV)', 77, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(903, 'Esara ( 33 KV )', 169, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(904, 'Chida ( 33 KV )', 169, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(905, 'Tercha ( 33 KV )', 169, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(906, 'Masha(33KV)', 101, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(907, 'Tum(15KV)', 170, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(908, 'Adego Line-1 -15kV', 171, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(909, 'Gondar ber Line-4 -15kV', 171, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(910, 'Piasa Line-3 -15kV', 171, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(911, 'Hara Line -1 -33 kV', 171, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(912, 'Gobiye Line-2-33 kV', 171, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(913, 'Gobiye Line-2-15 kV', 171, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(914, 'Mechare -33Kv', 171, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(915, 'Kone Line-3 33kv', 172, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(916, 'Estaysh Line-1 33kv', 172, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(917, 'Gashena Line-2 33kv', 172, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(918, 'Lalibela /Ayina/Line-2 -15kV', 173, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(919, 'Muja/Line-3 -15kV', 173, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(920, 'Airport /Shimsha/Line-1 -15kV', 173, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(921, 'Dahina -15kV', 174, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(922, 'Sekota /Asketema/woleh/ -15kV', 174, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(923, 'Tsemera -15 Kv', 174, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(924, 'Sahila -33kV', 174, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(925, 'Wuchalie Line-3 -15kV', 175, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(926, 'Mecherie Line-1  -33kV', 175, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(927, 'Universit Line-3 -15kV', 175, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(928, 'Sanka Line-4 -15kV', 175, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(929, 'Ko6 - 15kv', 176, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(930, 'Ko7 -15kv', 176, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(931, 'Q02  -33kv', 176, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(932, 'Gesuba F-1', 177, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(933, 'Sodo F-2', 177, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(934, 'Boditi F-4', 177, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(935, 'Bilate_ F-5', 177, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(936, 'Abala Longena F-2', 177, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(937, 'Bedesa Bilate F-3', 177, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(938, 'Bele_Sorto F-4', 177, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(939, 'Areka F-1', 178, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(940, 'Bele  F-2', 178, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(941, 'Boditi F-4', 178, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(942, 'Sodo F-5', 178, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(943, 'Halaba F-2', 90, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(944, 'Durame F-3', 90, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(945, 'Halaba_Alem Gebeya F-4', 90, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(946, 'Dilla F-1', 179, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(947, 'Dilla F-2', 179, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(948, 'Yirgachfe F-3', 179, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(949, 'Solamo F-4', 179, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(950, 'Chelelektu F-4', 180, 33, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(951, 'ADE-1', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(952, 'ADE-2', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(953, 'ADE-4', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(954, 'ADE-5', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(955, 'ADE-7', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(956, 'BEL-2', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(957, 'BLM-1', 182, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(958, 'BLM-2', 182, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(959, 'BLM-3', 182, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(960, 'BLM-5', 182, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00');
INSERT INTO `feeder_lines` (`id`, `name`, `substation_id`, `voltage_level_kv`, `length`, `number_of_transformers`, `number_of_customers`, `peak_load_a`, `peak_load_mw`, `created_at`, `updated_at`) VALUES
(961, 'BLM-6', 182, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(962, 'BLM-7', 182, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(963, 'COT-1', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(964, 'COT-2', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(965, 'COT-4', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(966, 'COT-5', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(967, 'COT-6', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(968, 'COT-7', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(969, 'COT-8', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(970, 'COT-9', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(971, 'COT-K1', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(972, 'COT-K2', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(973, 'COT-K3', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(974, 'COT-K5', 183, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(975, 'LEG-11', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(976, 'LEG-13', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(977, 'LEG-4', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(978, 'LEG-5', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(979, 'LEG-6', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(980, 'LEG-7', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(981, 'LEG-8', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(982, 'WER-1', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(983, 'WER-10', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(984, 'WER-12', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(985, 'WER-4', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(986, 'WER-5', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(987, 'WER-6', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(988, 'WER-7', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(989, 'WER-8', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(990, 'WER-9', 185, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(991, 'ADE-10', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(992, 'ADE-11', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(993, 'ADE-9', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(994, 'LEG-10', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(995, 'LEG-3', 184, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(996, 'NIF-6', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(997, 'ADE-12', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(998, 'ADG-3', 187, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(999, 'ADG-4', 187, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1000, 'ADW-2', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1001, 'ADW-3', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1002, 'AND-1', 189, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1003, 'AND-2', 189, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1004, 'AND-3', 189, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1005, 'AND-4', 189, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1006, 'AND-6', 189, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1007, 'BEL-1', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1008, 'BEL-3', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1009, 'BEL-4', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1010, 'BEL-5', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1011, 'BEL-6', 130, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1012, 'BLL-14', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1013, 'BLL-2', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1014, 'SHG-1', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1015, 'SHG-2', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1016, 'SHG-4', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1017, 'SHG-6', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1018, 'SHG-7', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1019, 'SHG-9', 118, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1020, 'SUL-1', 129, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1021, 'ADE-8', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1022, 'ADW-1', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1023, 'ADW-11', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1024, 'ADW-5', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1025, 'ADW-6', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1026, 'BLL-1', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1027, 'BLL-10', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1028, 'AKA-1', 191, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1029, 'AKA-2', 191, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1030, 'AKA-3', 191, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1031, 'AKA-4', 191, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1032, 'BLL-6', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1033, 'GEL-4', 192, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1034, 'GOF-2', 193, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1035, 'GOF-3', 193, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1036, 'GOF-4', 193, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1037, 'GOF-5', 193, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1038, 'GOF-6', 193, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1039, 'KAL.GIS-3', 194, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1040, 'KAL.GIS-5', 194, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1041, 'KAL.GIS-6', 194, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1042, 'KAL.II-1', 195, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1043, 'KAL.II-2', 195, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1044, 'KAL.II-3', 195, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1045, 'KAL.II-4', 195, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1046, 'KAL.II-5', 195, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1047, 'KAL.II-6', 195, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1048, 'KAL.I-K13', 196, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1049, 'KAL.I-K2', 196, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1050, 'KAL.I-K3', 196, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1051, 'KAL.I-K4', 196, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1052, 'KAL.N-K2', 197, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1053, 'KAL.N-K4', 197, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1054, 'KAL.N-K5', 197, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1055, 'KAL.N-K6', 197, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1056, 'KAL.N-K7', 197, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1057, 'KOY-4', 198, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1058, 'KOY-5', 198, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1059, 'KOY-6', 198, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1060, 'KOY-7', 198, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1061, 'MEK-1', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1062, 'MEK-2', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1063, 'MEK-3', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1064, 'MEK-4', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1065, 'MEK-5', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1066, 'MEK-6', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1067, 'MEK-7', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1068, 'MEK-8', 128, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1069, 'NIF-10', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1070, 'NIF-8', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1071, 'NIF-9', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1072, 'GEL-6', 192, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1073, 'NIF-4', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1074, 'ADC-11', 199, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1075, 'ADC-15', 199, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1076, 'ADC-4', 199, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1077, 'ADC-7', 199, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1078, 'ADC-8', 199, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1079, 'ADW-12', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1080, 'ADW-4', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1081, 'ADW-8', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1082, 'ANF-3', 122, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1083, 'BLL-12', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1084, 'BLL-3', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1085, 'BLL-7', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1086, 'NIF-3', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1087, 'NIF-7', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1088, 'SEB-1', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1089, 'SEB-10', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1090, 'SEB-14', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1091, 'SEB-15', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1092, 'SEB-3', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1093, 'SEB-4', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1094, 'SEB-8', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1095, 'ADC-5', 199, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1096, 'ADE-10', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1097, 'ADE-11', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1098, 'ADE-9', 181, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1099, 'ADW-1', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1100, 'ADW-11', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1101, 'ADW-5', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1102, 'ADW-6', 188, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1103, 'BLL-1', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1104, 'BLL-10', 190, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1105, 'NIF-1', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1106, 'NIF-4', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1107, 'NIF-6', 186, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00'),
(1108, 'SEB-12', 124, 15, NULL, NULL, 1000, NULL, NULL, '2025-03-27 07:13:00', '2025-03-27 07:13:00');
