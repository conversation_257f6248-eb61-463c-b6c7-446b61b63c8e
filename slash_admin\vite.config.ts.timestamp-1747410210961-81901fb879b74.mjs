// vite.config.ts
import path from "node:path";
import { vanillaExtractPlugin } from "file:///C:/Users/<USER>/Desktop/transformer/New/slash_admin/node_modules/@vanilla-extract/vite-plugin/dist/vanilla-extract-vite-plugin.cjs.js";
import react from "file:///C:/Users/<USER>/Desktop/transformer/New/slash_admin/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { visualizer } from "file:///C:/Users/<USER>/Desktop/transformer/New/slash_admin/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { defineConfig, loadEnv } from "file:///C:/Users/<USER>/Desktop/transformer/New/slash_admin/node_modules/vite/dist/node/index.js";
import { createSvgIconsPlugin } from "file:///C:/Users/<USER>/Desktop/transformer/New/slash_admin/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import tsconfigPaths from "file:///C:/Users/<USER>/Desktop/transformer/New/slash_admin/node_modules/vite-tsconfig-paths/dist/index.js";
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const base = env.VITE_APP_BASE_PATH || "/";
  const isProduction = mode === "production";
  return {
    base,
    plugins: [
      react({
        // 添加 React 插件的优化配置
        babel: {
          parserOpts: {
            plugins: ["decorators-legacy", "classProperties"]
          }
        }
      }),
      vanillaExtractPlugin({
        identifiers: ({ debugId }) => `${debugId}`
      }),
      tsconfigPaths(),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
        symbolId: "icon-[dir]-[name]"
      }),
      isProduction && visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: "treemap"
        // 使用树形图更直观
      })
    ].filter(Boolean),
    server: {
      open: true,
      host: true,
      port: 4e3,
      proxy: {
        "/api": {
          target: env.VITE_APP_BASE_API,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, ""),
        },
        "/auth": {
          target: env.VITE_APP_BASE_API,
          changeOrigin: true
        }
      }
    },
    build: {
      target: "esnext",
      minify: "esbuild",
      sourcemap: !isProduction,
      cssCodeSplit: true,
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          manualChunks: {
            "vendor-core": ["react", "react-dom", "react-router"],
            "vendor-ui": ["antd", "@ant-design/icons", "@ant-design/cssinjs", "framer-motion", "styled-components"],
            "vendor-utils": ["axios", "dayjs", "i18next", "zustand", "@iconify/react"],
            "vendor-charts": ["apexcharts", "react-apexcharts"]
          }
        }
      }
    },
    // 优化依赖预构建
    optimizeDeps: {
      include: ["react", "react-dom", "react-router", "antd", "@ant-design/icons", "axios", "dayjs"],
      exclude: ["@iconify/react"]
      // 排除不需要预构建的依赖
    },
    // esbuild 优化配置
    esbuild: {
      drop: isProduction ? ["console", "debugger"] : [],
      legalComments: "none",
      target: "esnext"
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
