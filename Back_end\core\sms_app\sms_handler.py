import logging
import binascii
import threading
import time
import socket
from django.utils import timezone
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.conf import settings
import smpplib.gsm
import smpplib.client
import smpplib.consts

from .models import SMSMessage, Reply, TimelineEvent
from .tasks import handle_incoming_sms_task, auto_categorize_message, send_auto_reply, send_notification_task

logger = logging.getLogger(__name__)
channel_layer = get_channel_layer()

# Global SMPP client instance
smpp_client = None
smpp_thread = None
smpp_running = False


class SMPPClient:
    """Python SMPP Client - Converted from Node.js implementation"""

    def __init__(self):
        self.client = None
        self.connected = False
        self.running = False
        self.config = getattr(settings, 'SMPP_CONFIG', {
            'HOST': '*************',
            'PORT': 5019,
            'SYSTEM_ID': '908',
            'PASSWORD': 'Eth@908e',
        })

    def connect(self):
        """Connect to SMPP server"""
        try:
            logger.info(f"🔌 Connecting to SMPP server {self.config['HOST']}:{self.config['PORT']}")

            # Create SMPP client
            self.client = smpplib.client.Client(
                self.config['HOST'],
                self.config['PORT']
            )

            # Connect to server
            self.client.connect()

            # Bind as transceiver
            self.client.bind_transceiver(
                system_id=self.config['SYSTEM_ID'],
                password=self.config['PASSWORD'],
                system_type='',
                interface_version=0x34,
                addr_ton=smpplib.consts.SMPP_TON_INTL,
                addr_npi=smpplib.consts.SMPP_NPI_ISDN,
                address_range='908'
            )

            self.connected = True
            logger.info("✅ Bound successfully to SMSC as transceiver")

            return True

        except Exception as e:
            logger.error(f"❌ SMPP connection failed: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """Disconnect from SMPP server"""
        try:
            if self.client and self.connected:
                self.client.unbind()
                self.client.disconnect()
                self.connected = False
                logger.info("🔌 Disconnected from SMPP server")
        except Exception as e:
            logger.error(f"Error disconnecting from SMPP: {e}")

    def send_sms(self, to_number, message):
        """Send SMS message"""
        try:
            if not self.connected:
                logger.error("❌ SMPP client not connected")
                return False

            # Prepare message
            parts, encoding_flag, msg_type_flag = smpplib.gsm.make_parts(message)

            for part in parts:
                pdu = self.client.send_message(
                    source_addr_ton=smpplib.consts.SMPP_TON_INTL,
                    source_addr_npi=smpplib.consts.SMPP_NPI_ISDN,
                    source_addr='908',
                    dest_addr_ton=smpplib.consts.SMPP_TON_INTL,
                    dest_addr_npi=smpplib.consts.SMPP_NPI_ISDN,
                    destination_addr=to_number,
                    short_message=part,
                    data_coding=encoding_flag,
                    esm_class=msg_type_flag,
                    registered_delivery=smpplib.consts.REGISTERED_DELIVERY_SMSC_BOTH
                )

                logger.info(f"📨 SMS sent to {to_number}. Message ID: {pdu.message_id}")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to send SMS: {e}")
            return False

    def listen_for_messages(self):
        """Listen for incoming messages"""
        try:
            while self.running and self.connected:
                try:
                    # Listen for PDUs with timeout
                    pdus = self.client.listen(timeout=30)

                    if pdus:
                        for pdu in pdus:
                            self.handle_pdu(pdu)
                    else:
                        # No PDUs received, continue listening
                        time.sleep(1)

                except Exception as e:
                    logger.error(f"Error listening for messages: {e}")
                    time.sleep(5)  # Wait before retrying

        except Exception as e:
            logger.error(f"Fatal error in message listener: {e}")
        finally:
            self.running = False

    def handle_pdu(self, pdu):
        """Handle incoming PDU"""
        try:
            logger.debug(f"📦 Received PDU: {pdu.command}")

            if pdu.command == 'deliver_sm':
                self.handle_deliver_sm(pdu)
            elif pdu.command == 'delivery_receipt':
                self.handle_delivery_receipt(pdu)

        except Exception as e:
            logger.error(f"Error handling PDU: {e}")

    def handle_deliver_sm(self, pdu):
        """Handle incoming SMS delivery"""
        try:
            # Extract message content
            message = None

            if hasattr(pdu, 'short_message') and pdu.short_message:
                try:
                    # Try UTF-8 first
                    message = pdu.short_message.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        # Try UCS2
                        message = pdu.short_message.decode('utf-16-be')
                    except UnicodeDecodeError:
                        logger.warning("⚠️ Unable to decode message")
                        return

            if not message or not message.strip():
                logger.info("ℹ️ Ignored empty message or delivery receipt")
                return

            from_number = getattr(pdu, 'source_addr', 'Unknown')
            to_number = getattr(pdu, 'destination_addr', 'Unknown')

            logger.info(f"📥 Received SMS from {from_number} to {to_number}: \"{message}\"")

            # Process the incoming SMS
            handle_incoming_sms_task.delay(from_number, to_number, message)

            # Send auto-reply
            auto_reply = "We received your message successfully. We'll get back to you soon."
            self.send_sms(from_number, auto_reply)

        except Exception as e:
            logger.error(f"Error handling deliver_sm: {e}")

    def handle_delivery_receipt(self, pdu):
        """Handle delivery receipt"""
        try:
            message_id = getattr(pdu, 'receipted_message_id', 'Unknown')
            logger.info(f"📬 Delivery receipt received for message ID: {message_id}")

            # You can update message status here if needed
            # process_delivery_receipt(pdu)

        except Exception as e:
            logger.error(f"Error handling delivery receipt: {e}")


def handle_incoming_sms(pdu):
    """Handle incoming SMS messages."""
    try:
        if hasattr(pdu, 'short_message') and pdu.short_message:
            # Decode message (try UTF-8, then UCS2)
            try:
                message_text = pdu.short_message.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    message_text = pdu.short_message.decode('utf-16-be')
                except UnicodeDecodeError:
                    message_text = binascii.hexlify(pdu.short_message).decode()

            from_number = pdu.source_addr.decode() if hasattr(pdu.source_addr, "decode") else pdu.source_addr
            to_number = pdu.destination_addr.decode() if hasattr(pdu.destination_addr, "decode") else pdu.destination_addr

            logging.info(f"📥 Received SMS from {from_number} to {to_number}: \"{message_text}\"")

            # Process the incoming SMS asynchronously
            handle_incoming_sms_task.delay(from_number, to_number, message_text)

            # Send immediate acknowledgment
            return True

    except Exception as e:
        logging.error(f"⚠️ Error processing incoming SMS: {e}")
        return False


def send_sms_reply(phone_number, message_content, case_id=None):
    """Send SMS reply to customer"""
    try:
        # This function would integrate with your SMPP client
        # For now, we'll simulate the sending and create a reply record
        
        if case_id:
            try:
                sms_message = SMSMessage.objects.get(case_id=case_id)
                
                # Create reply record
                Reply.objects.create(
                    message=sms_message,
                    content=message_content,
                    sender=None,  # System or assigned user
                    is_from_customer=False,
                    timestamp=timezone.now()
                )
                
                # Create timeline event
                TimelineEvent.objects.create(
                    message=sms_message,
                    type='reply',
                    user=None,  # System generated
                    description=f"SMS reply sent to {phone_number}"
                )
                
                # Send real-time update
                if channel_layer:
                    async_to_sync(channel_layer.group_send)(
                        'messages',
                        {
                            'type': 'new_reply_broadcast',
                            'message_id': str(sms_message.id),
                            'reply': {
                                'content': message_content,
                                'timestamp': timezone.now().isoformat(),
                                'sender': 'System'
                            }
                        }
                    )
                
                logger.info(f"SMS reply sent to {phone_number} for case {case_id}")
                return True
                
            except SMSMessage.DoesNotExist:
                logger.error(f"Case {case_id} not found")
                return False
        
        # If no case_id, just log the outgoing SMS
        logger.info(f"SMS sent to {phone_number}: {message_content}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to send SMS reply: {str(e)}")
        return False


def process_delivery_receipt(pdu):
    """Process SMS delivery receipts"""
    try:
        if hasattr(pdu, 'receipted_message_id'):
            message_id = pdu.receipted_message_id
            
            # Find the original message by message_id
            # Note: You might need to store the SMPP message_id in your SMSMessage model
            # For now, we'll just log the delivery receipt
            
            logger.info(f"📬 Delivery receipt received for message ID: {message_id}")
            
            # You could update message status here if you track SMPP message IDs
            # sms_message = SMSMessage.objects.filter(smpp_message_id=message_id).first()
            # if sms_message:
            #     sms_message.status = 'delivered'
            #     sms_message.save()
            
            return True
            
    except Exception as e:
        logger.error(f"Error processing delivery receipt: {str(e)}")
        return False


def create_customer_reply(phone_number, message_content):
    """Create a customer reply to an existing case"""
    try:
        # Find the most recent case for this phone number
        existing_message = SMSMessage.objects.filter(
            phone_number=phone_number
        ).order_by('-timestamp').first()
        
        if existing_message:
            # Add as reply to existing case
            Reply.objects.create(
                message=existing_message,
                content=message_content,
                sender=None,  # Customer
                is_from_customer=True,
                timestamp=timezone.now()
            )
            
            # Update message status if it was closed
            if existing_message.status == 'closed':
                existing_message.status = 'replied'
                existing_message.save()
            
            # Create timeline event
            TimelineEvent.objects.create(
                message=existing_message,
                type='reply',
                user=None,  # Customer
                description=f"Customer reply received"
            )
            
            # Notify assigned technician
            if existing_message.assigned_to:
                send_notification_task.delay(
                    str(existing_message.assigned_to.id),
                    'message',
                    'Customer Reply',
                    f'Customer replied to case {existing_message.case_id}',
                    existing_message.case_id,
                    str(existing_message.id)
                )
            
            logger.info(f"Customer reply added to case {existing_message.case_id}")
            return existing_message
        else:
            # Create new case if no existing case found
            return handle_incoming_sms_task(phone_number, '', message_content)
            
    except Exception as e:
        logger.error(f"Failed to create customer reply: {str(e)}")
        raise


# Integration function for SMPP client
def handle_incoming_sm(pdu):
    """Handle incoming SMS messages - integration point for SMPP client."""
    try:
        if hasattr(pdu, 'short_message') and pdu.short_message:
            # Decode message (try UTF-8, then UCS2)
            try:
                message_text = pdu.short_message.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    message_text = pdu.short_message.decode('utf-16-be')
                except UnicodeDecodeError:
                    message_text = binascii.hexlify(pdu.short_message).decode()

            from_number = pdu.source_addr.decode() if hasattr(pdu.source_addr, "decode") else pdu.source_addr
            to_number = pdu.destination_addr.decode() if hasattr(pdu.destination_addr, "decode") else pdu.destination_addr

            logging.info(f"📥 Received SMS from {from_number} to {to_number}: \"{message_text}\"")

            # Process the incoming SMS
            handle_incoming_sms_task.delay(from_number, to_number, message_text)

            # Auto-reply
            auto_reply = "We received your message successfully. We'll get back to you soon."
            # Here you would call your SMS sending function
            # send_sms(from_number, auto_reply)
            
            return True

    except Exception as e:
        logging.error(f"⚠️ Error processing incoming SMS: {e}")
        return False


# SMPP Management Functions
def start_smpp_client():
    """Start the SMPP client and begin listening for messages"""
    global smpp_client, smpp_thread, smpp_running

    try:
        if smpp_running:
            logger.info("SMPP client is already running")
            return True

        logger.info("🚀 Starting SMPP client...")

        # Create SMPP client instance
        smpp_client = SMPPClient()

        # Connect to SMPP server
        if not smpp_client.connect():
            logger.error("Failed to connect to SMPP server")
            return False

        # Start listening in a separate thread
        smpp_running = True
        smpp_client.running = True

        smpp_thread = threading.Thread(
            target=smpp_client.listen_for_messages,
            daemon=True,
            name="SMPP-Listener"
        )
        smpp_thread.start()

        logger.info("✅ SMPP client started successfully")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to start SMPP client: {e}")
        smpp_running = False
        return False


def stop_smpp_client():
    """Stop the SMPP client"""
    global smpp_client, smpp_thread, smpp_running

    try:
        logger.info("🛑 Stopping SMPP client...")

        smpp_running = False

        if smpp_client:
            smpp_client.running = False
            smpp_client.disconnect()

        if smpp_thread and smpp_thread.is_alive():
            smpp_thread.join(timeout=10)

        smpp_client = None
        smpp_thread = None

        logger.info("✅ SMPP client stopped successfully")
        return True

    except Exception as e:
        logger.error(f"❌ Error stopping SMPP client: {e}")
        return False


def restart_smpp_client():
    """Restart the SMPP client"""
    logger.info("🔄 Restarting SMPP client...")
    stop_smpp_client()
    time.sleep(2)
    return start_smpp_client()


def get_smpp_status():
    """Get SMPP client status"""
    global smpp_client, smpp_running

    status = {
        'running': smpp_running,
        'connected': smpp_client.connected if smpp_client else False,
        'thread_alive': smpp_thread.is_alive() if smpp_thread else False,
        'config': getattr(settings, 'SMPP_CONFIG', {})
    }

    return status


def send_sms_via_smpp(to_number, message):
    """Send SMS via SMPP client"""
    global smpp_client

    try:
        if not smpp_client or not smpp_client.connected:
            logger.error("SMPP client not connected")
            return False

        return smpp_client.send_sms(to_number, message)

    except Exception as e:
        logger.error(f"Error sending SMS via SMPP: {e}")
        return False
