#!/usr/bin/env python3
"""
Test WebSocket connection using Python websockets library (alternative to wscat)
"""
import asyncio
import websockets
import json
import sys

async def test_websocket():
    """Test WebSocket connection like wscat"""
    uri = "ws://localhost:8000/ws/messages/"
    
    try:
        print(f"🔌 Connecting to {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected successfully!")
            
            # Wait for connection confirmation
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                print(f"📨 Received: {json.dumps(data, indent=2)}")
                
                # Send a ping
                ping_message = {"type": "ping", "timestamp": "2025-08-19T12:00:00Z"}
                await websocket.send(json.dumps(ping_message))
                print(f"📤 Sent: {json.dumps(ping_message, indent=2)}")
                
                # Wait for pong
                pong = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                pong_data = json.loads(pong)
                print(f"📨 Received: {json.dumps(pong_data, indent=2)}")
                
                print("✅ WebSocket test successful!")
                return True
                
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for server response")
                return False
                
    except ConnectionRefusedError:
        print("❌ Connection refused - Django server not running or not in ASGI mode")
        return False
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ HTTP {e.status_code} - WebSocket endpoint not available")
        print("   This means the server is running in WSGI mode, not ASGI mode")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 WebSocket Connection Test (Python wscat alternative)")
    print("=" * 60)
    
    success = await test_websocket()
    
    if success:
        print("\n🎉 WebSocket connection test PASSED!")
        print("   The Django server is running with ASGI support")
        sys.exit(0)
    else:
        print("\n❌ WebSocket connection test FAILED!")
        print("   Please restart the Django server:")
        print("   1. Stop the current server (Ctrl+C)")
        print("   2. Run: python restart_server.py")
        print("   3. Or run: python manage.py runserver")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
