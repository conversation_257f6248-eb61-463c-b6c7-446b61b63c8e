#!/usr/bin/env python3
"""
Scrip<PERSON> to check Django Channels WebSocket configuration
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.conf import settings
from channels.routing import get_default_application
import importlib

def check_configuration():
    """Check Django Channels configuration"""
    print("🔧 Django Channels Configuration Check")
    print("=" * 50)
    
    # Check ASGI application
    asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
    print(f"ASGI_APPLICATION: {asgi_app}")
    
    if asgi_app:
        try:
            # Try to import the ASGI application
            module_name, app_name = asgi_app.rsplit('.', 1)
            module = importlib.import_module(module_name)
            app = getattr(module, app_name)
            print("✅ ASGI application can be imported successfully")
        except Exception as e:
            print(f"❌ Error importing ASGI application: {e}")
    else:
        print("❌ ASGI_APPLICATION not set")
    
    # Check channel layers
    channel_layers = getattr(settings, 'CHANNEL_LAYERS', None)
    print(f"\nCHANNEL_LAYERS: {channel_layers}")
    
    if channel_layers:
        try:
            from channels.layers import get_channel_layer
            layer = get_channel_layer()
            print(f"✅ Channel layer: {type(layer).__name__}")
        except Exception as e:
            print(f"❌ Error getting channel layer: {e}")
    else:
        print("❌ CHANNEL_LAYERS not configured")
    
    # Check installed apps
    print(f"\nChannels in INSTALLED_APPS: {'channels' in settings.INSTALLED_APPS}")
    
    # Check routing
    print("\nWebSocket Routing:")
    try:
        from sms_app.routing import websocket_urlpatterns
        print(f"✅ WebSocket URL patterns found: {len(websocket_urlpatterns)} patterns")
        for pattern in websocket_urlpatterns:
            print(f"   - {pattern.pattern.pattern}")
    except Exception as e:
        print(f"❌ Error importing WebSocket routing: {e}")
    
    # Check consumers
    print("\nConsumers:")
    try:
        from sms_app.consumers import MessageConsumer
        print("✅ MessageConsumer can be imported")
    except Exception as e:
        print(f"❌ Error importing MessageConsumer: {e}")
    
    # Check middleware
    print("\nMiddleware:")
    try:
        from sms_app.middleware import WebSocketAuthMiddlewareStack
        print("✅ WebSocketAuthMiddlewareStack can be imported")
    except Exception as e:
        print(f"❌ Error importing middleware: {e}")
    
    print("\n" + "=" * 50)
    print("Configuration Summary:")
    
    issues = []
    if not asgi_app:
        issues.append("ASGI_APPLICATION not set")
    if not channel_layers:
        issues.append("CHANNEL_LAYERS not configured")
    if 'channels' not in settings.INSTALLED_APPS:
        issues.append("'channels' not in INSTALLED_APPS")
    
    if issues:
        print("❌ Issues found:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ Configuration looks good!")
    
    print("\nTo test WebSocket connection, run:")
    print("python manage.py test_websocket")

if __name__ == "__main__":
    check_configuration()
