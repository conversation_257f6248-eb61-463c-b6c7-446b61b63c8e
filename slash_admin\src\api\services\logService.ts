import apiClient from "../apiClient";

export interface BaseLog {
    id: number;
    timestamp: string;
    user: string;
    ip_address: string;
}

export interface ActivityLog extends BaseLog {
    action: 'CREATE' | 'UPDATE' | 'DELETE';
    model_name: string;
    record_id: string;
    changes: string;
}

export interface ErrorLog extends BaseLog {
    level: 'ERROR' | 'WARNING' | 'CRITICAL' | 'INFO';
    message: string;
}

export interface DataChangeLog extends BaseLog {
    model_name: string;
    record_id: string;
    field_name: string;
    old_value: string;
    new_value: string;
}

export enum LogApi {
    ActivityLogs = "/api/logs/activity/",
    ErrorLogs = "/api/logs/errors/",
    DataChangeLogs = "/api/logs/changes/",
    DeleteActivityLogs = "/api/logs/activity_clear/",
    DeleteErrorLogs = "/api/logs/errors_clear/",
    DeleteDataChangeLogs = "/api/logs/changes_clear/",
    SpecificChangeLogs = "/api/logs/changes/"
}

export type ModelType = 'Transformer Data' | 'Inspection' | 'LV Feeder' | 'Basestation';

const MODEL_PREFIXES: Record<ModelType, string> = {
    'Transformer Data': 'TR',
    'Inspection': 'IN',
    'LV Feeder': 'LV',
    'Basestation': 'BS'
} as const;

const logService = {
    getActivityLogs: async (params: { 
        page?: number; 
        pageSize?: number;
        start_date?: string;
        end_date?: string;
        user?: string;
        action?: string;
        model_name?: string;
    } = {}) =>
        apiClient.get<{ count: number; results: ActivityLog[] }>({
            url: LogApi.ActivityLogs,
            params
        }),

    getErrorLogs: async (params: {
        page?: number;
        pageSize?: number;
        start_date?: string;
        end_date?: string;
        user?: string;
        level?: string;
        message?: string;
    } = {}) =>
        apiClient.get<{ count: number; results: ErrorLog[] }>({
            url: LogApi.ErrorLogs,
            params
        }),

    getDataChangeLogs: async (params: {
        page?: number;
        pageSize?: number;
        start_date?: string;
        end_date?: string;
        changed_by?: string;
        model_name?: string;
        field_name?: string;
    } = {}) =>
        apiClient.get<{ count: number; results: DataChangeLog[] }>({
            url: LogApi.DataChangeLogs,
            params
        }),

    deleteActivityLogs: async () =>
        apiClient.delete({ url: LogApi.DeleteActivityLogs }),

    deleteErrorLogs: async () =>
        apiClient.delete({ url: LogApi.DeleteErrorLogs }),

    deleteDataChangeLogs: async () =>
        apiClient.delete({ url: LogApi.DeleteDataChangeLogs }),

    getSpecificChangeLogs: async (
        id: string,
        modelType: ModelType,
        params: {
            page?: number;
            pageSize?: number;
            user?: string;
            changed_by?: string;
            field_name?: string;
        } = {}
    ) => {
        const prefix = MODEL_PREFIXES[modelType];
        const record_id = `${prefix}-${id}`;

        try {
            const response = await apiClient.get<{ count: number; results: DataChangeLog[] }>({
                url: LogApi.DataChangeLogs,
                params: {
                    ...params,
                    model_name: modelType,
                    record_id: record_id
                }
            });
            
            return {
                count: response.count,
                results: response.results || []
            };
        } catch (error) {
            console.error('Error in getSpecificChangeLogs:', error);
            return {
                count: 0,
                results: []
            };
        }
    },
};

export default logService;














