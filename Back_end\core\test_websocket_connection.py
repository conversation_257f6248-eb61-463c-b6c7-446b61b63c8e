#!/usr/bin/env python3
"""
Test script to verify WebSocket connection to Django Channels
"""
import asyncio
import websockets
import json
import sys

async def test_websocket_connection():
    """Test WebSocket connection to the Django server"""
    uri = "ws://localhost:8000/ws/messages/"
    
    try:
        print(f"Attempting to connect to {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connection established!")
            
            # Wait for connection confirmation
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                print(f"📨 Received: {data}")
                
                if data.get('type') == 'connection_established':
                    print("✅ Connection confirmed by server")
                    
                    # Send a test ping
                    test_message = {
                        'type': 'ping',
                        'timestamp': '2025-08-19T12:00:00Z'
                    }
                    
                    await websocket.send(json.dumps(test_message))
                    print(f"📤 Sent ping: {test_message}")
                    
                    # Wait for pong response
                    pong_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    pong_data = json.loads(pong_response)
                    print(f"📨 Received pong: {pong_data}")
                    
                    if pong_data.get('type') == 'pong':
                        print("✅ Ping/Pong test successful!")
                        return True
                    else:
                        print("❌ Expected pong response, got something else")
                        return False
                        
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for server response")
                return False
                
    except ConnectionRefusedError:
        print("❌ Connection refused - is the Django server running?")
        return False
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ Invalid status code: {e}")
        print("This usually means the WebSocket endpoint is not configured correctly")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_http_endpoint():
    """Test the HTTP test endpoint"""
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/api/websocket/test/') as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ HTTP test endpoint working:")
                    print(f"   ASGI Application: {data.get('asgi_application')}")
                    print(f"   Channel Layers: {data.get('channel_layers')}")
                    print(f"   WebSocket URLs: {data.get('websocket_urls')}")
                    return True
                else:
                    print(f"❌ HTTP test endpoint returned status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ HTTP test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🔧 Testing Django Channels WebSocket Setup")
    print("=" * 50)
    
    # Test HTTP endpoint first
    print("\n1. Testing HTTP endpoint...")
    http_ok = await test_http_endpoint()
    
    # Test WebSocket connection
    print("\n2. Testing WebSocket connection...")
    ws_ok = await test_websocket_connection()
    
    print("\n" + "=" * 50)
    if http_ok and ws_ok:
        print("✅ All tests passed! WebSocket setup is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the Django server configuration.")
        if not http_ok:
            print("   - HTTP endpoint test failed")
        if not ws_ok:
            print("   - WebSocket connection test failed")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
