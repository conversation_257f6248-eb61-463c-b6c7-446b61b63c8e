from rest_framework import serializers
# from django.contrib.auth import get_user_model
from .models import (
    SMSMessage, Reply, Attachment, TimelineEvent, AssignmentHistory,
    ChecklistItem, ProgressReport, ProgressUpdate, Notification,
    ApprovalRequest, ApprovalHistory, AssignmentStatus, DetailedReport,
    MessageTemplate, SystemSettings, Analytics, Department
)

# User = get_user_model()
from account.models import User


class UserSerializer(serializers.ModelSerializer):
    """User serializer matching frontend User interface"""
    name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'name', 'role', 'department', 'avatar', 'is_active']
        read_only_fields = ['id']

    def get_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip() or obj.username


class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ['id', 'name', 'color']


class AttachmentSerializer(serializers.ModelSerializer):
    url = serializers.ReadOnlyField()

    class Meta:
        model = Attachment
        fields = ['id', 'name', 'url', 'type', 'size', 'uploaded_at']


class ReplySerializer(serializers.ModelSerializer):
    # sender = UserSerializer(read_only=True)
    sender = serializers.SerializerMethodField()
    attachments = AttachmentSerializer(many=True, read_only=True)

    class Meta:
        model = Reply
        fields = ['id', 'content', 'timestamp', 'sender', 'is_from_customer', 'attachments', 'status']
        read_only_fields = ['id', 'timestamp']

    def get_sender(self, obj):
        return (
            UserSerializer(obj.sender).data
            if obj.sender
            else {
                "id": None,
                "username": "Customer",
                "email": None,
                "first_name": "",
                "last_name": "",
                "name": "Customer",
                "role": None,
                "department": None,
                "avatar": None,
                "is_active": True
            }
        )

class TimelineEventSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = TimelineEvent
        fields = ['id', 'type', 'timestamp', 'user', 'description', 'metadata']


class ChecklistItemSerializer(serializers.ModelSerializer):
    completed_by = UserSerializer(read_only=True)

    class Meta:
        model = ChecklistItem
        fields = ['id', 'text', 'completed', 'completed_at', 'completed_by']


class ProgressUpdateSerializer(serializers.ModelSerializer):
    updated_by = UserSerializer(read_only=True)

    class Meta:
        model = ProgressUpdate
        fields = ['id', 'content', 'timestamp', 'updated_by', 'percent_complete']


class ProgressReportSerializer(serializers.ModelSerializer):
    reported_by = UserSerializer(read_only=True)
    updates = ProgressUpdateSerializer(many=True, read_only=True)

    class Meta:
        model = ProgressReport
        fields = ['id', 'content', 'timestamp', 'reported_by', 'updates']


class AssignmentStatusSerializer(serializers.ModelSerializer):
    assigned_to = UserSerializer(read_only=True)
    assigned_by = UserSerializer(read_only=True)

    class Meta:
        model = AssignmentStatus
        fields = [
            'id', 'assigned_to', 'assigned_by', 'assigned_at', 'priority',
            'due_date', 'estimated_hours', 'skills_required', 'status',
            'accepted_at', 'completed_at', 'comments'
        ]


class ApprovalHistorySerializer(serializers.ModelSerializer):
    approved_by = UserSerializer(read_only=True)

    class Meta:
        model = ApprovalHistory
        fields = ['id', 'decision', 'comments', 'approved_by', 'approved_at']


class ApprovalRequestSerializer(serializers.ModelSerializer):
    requested_by = UserSerializer(read_only=True)
    approved_by = UserSerializer(read_only=True)
    approvers = UserSerializer(many=True, read_only=True)
    approval_history = ApprovalHistorySerializer(many=True, read_only=True)

    class Meta:
        model = ApprovalRequest
        fields = [
            'id', 'type', 'description', 'priority', 'requested_by', 'requested_at',
            'approvers', 'status', 'approved_by', 'approved_at', 'comments',
            'approval_history'
        ]


class DetailedReportSerializer(serializers.ModelSerializer):
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = DetailedReport
        fields = [
            'id', 'case_id', 'title', 'summary', 'detailed_description',
            'actions_taken', 'outcome', 'recommendations', 'follow_up_required',
            'follow_up_date', 'follow_up_notes', 'created_at', 'created_by'
        ]


class SMSMessageSerializer(serializers.ModelSerializer):
    """Main SMS Message serializer matching frontend Message interface"""
    assigned_to = UserSerializer(read_only=True)
    replies = ReplySerializer(many=True, read_only=True)
    attachments = AttachmentSerializer(many=True, read_only=True)
    timeline_events = TimelineEventSerializer(many=True, read_only=True)
    checklist_items = ChecklistItemSerializer(many=True, read_only=True)
    progress_reports = ProgressReportSerializer(many=True, read_only=True)
    assignment_status = AssignmentStatusSerializer(read_only=True)
    approval_requests = ApprovalRequestSerializer(many=True, read_only=True)
    detailed_reports = DetailedReportSerializer(many=True, read_only=True)

    class Meta:
        model = SMSMessage
        fields = [
            'id', 'phone_number', 'content', 'timestamp', 'status', 'category',
            'priority', 'case_id', 'assigned_to', 'tags', 'is_archived',
            'replies', 'attachments', 'timeline_events', 'checklist_items',
            'progress_reports', 'assignment_status', 'approval_requests',
            'detailed_reports'
        ]
        read_only_fields = ['id', 'timestamp', 'case_id']


class SMSMessageListSerializer(serializers.ModelSerializer):
    """Simplified serializer for message lists"""
    assigned_to = UserSerializer(read_only=True)
    reply_count = serializers.SerializerMethodField()
    last_reply = serializers.SerializerMethodField()
    # replies = ReplySerializer(many=True, read_only=True)

    class Meta:
        model = SMSMessage
        fields = [
            'id', 'phone_number', 'content', 'timestamp', 'status', 'category',
            'priority', 'case_id', 'assigned_to', 'tags', 'is_archived',
            'reply_count', 'last_reply'
        ]

    def get_reply_count(self, obj):
        return obj.replies.count()

    def get_last_reply(self, obj):
        last_reply = obj.replies.last()
        if last_reply:
            sender_data = (
                UserSerializer(last_reply.sender).data
                if last_reply.sender
                else {
                    "id": None,
                    "username": "Customer",
                    "email": None,
                    "first_name": "",
                    "last_name": "",
                    "name": "Customer",
                    "role": None,
                    "department": None,
                    "avatar": None,
                    "is_active": True
                }
            )
            return {
                'content': last_reply.content[:100],
                'timestamp': last_reply.timestamp,
                'sender': sender_data
            }
        return None


class NotificationSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)

    class Meta:
        model = Notification
        fields = ['id', 'type', 'title', 'message', 'timestamp', 'read', 'user', 'case_id', 'message_id']


class MessageTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = MessageTemplate
        fields = ['id', 'name', 'category', 'content', 'variables', 'is_active', 'created_at', 'updated_at']


class SystemSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SystemSettings
        fields = ['id', 'categories', 'working_hours', 'auto_replies', 'notifications', 'created_at', 'updated_at']


class AnalyticsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Analytics
        fields = [
            'id', 'date', 'total_cases', 'resolved_cases', 'pending_cases',
            'average_response_time', 'category_counts', 'status_counts', 'daily_trends'
        ]


class AssignmentHistorySerializer(serializers.ModelSerializer):
    from_user = UserSerializer(read_only=True)
    to_user = UserSerializer(read_only=True)
    assigned_by = UserSerializer(read_only=True)

    class Meta:
        model = AssignmentHistory
        fields = ['id', 'from_user', 'to_user', 'timestamp', 'reason', 'assigned_by']