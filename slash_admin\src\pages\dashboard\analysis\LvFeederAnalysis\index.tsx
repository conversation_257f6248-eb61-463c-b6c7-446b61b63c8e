import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Table, Tabs } from "antd";
import type { ColumnsType } from "antd/es/table";
import Scrollbar from "@/components/scrollbar";
import axios from "axios";
import { CircleLoading } from "@/components/loading";
import LvFeederGraph from "./LvFeederGraph";
import LvF<PERSON>er<PERSON>ieChart from "./LvFeederPieChart";
import apiClient from "@/api/apiClient";

const { TabPane } = Tabs;

interface LvFeederData {
  key: string;
  region: string;
  csc?: string;
  children?: LvFeederData[];
  [key: string]: any;
}

interface RegionData extends LvFeederData {
  children: LvFeederData[];
}

// Define LvFeeder tabs for dynamic rendering
const LVFEEDER_TABS = {
  // transformer_load: "Transformer Load",
  current_phase_unbalance: "Current Phase Unbalance",
  percentage_of_neutral: "Percentage of Neutral",
} as const;

// Helper function to calculate percentages
const calculatePercentages = (values: Record<string, number>) => {
  const total = Object.values(values).reduce((a, b) => a + b, 0);
  return Object.fromEntries(
    Object.entries(values).map(([key, value]) => [
      key,
      value,
      `${key}Percentage`,
      total ? Number(((value / total) * 100).toFixed(2)) : 0,
    ])
  );
};

// Helper function to get analysis type data
const getAnalysisTypeData = (analysisType: string, data: any, isCSC: boolean) => {
  const calculatePercentage = (value: number, total: number) => (total > 0 ? Math.round((value / total) * 100) : 0);

  switch (analysisType) {
    case "transformer_load": {
      const total = (data.below20 || 0) + (data.load20_50 || 0) + (data.load50_80 || 0) + 
                   (data.load80_100 || 0) + (data.above100 || 0);
      return {
        below20: data.below20 || 0,
        load20_50: data.load20_50 || 0,
        load50_80: data.load50_80 || 0,
        load80_100: data.load80_100 || 0,
        above100: data.above100 || 0,
        below20Percentage: isCSC ? data.below20Percentage : calculatePercentage(data.below20 || 0, total),
        load20_50Percentage: isCSC ? data.load20_50Percentage : calculatePercentage(data.load20_50 || 0, total),
        load50_80Percentage: isCSC ? data.load50_80Percentage : calculatePercentage(data.load50_80 || 0, total),
        load80_100Percentage: isCSC ? data.load80_100Percentage : calculatePercentage(data.load80_100 || 0, total),
        above100Percentage: isCSC ? data.above100Percentage : calculatePercentage(data.above100 || 0, total),
      };
    }
    case "current_phase_unbalance": {
      const total = (data.balanced || 0) + (data.unbalanced || 0);
      return {
        balanced: data.balanced || 0,
        unbalanced: data.unbalanced || 0,
        balancedPercentage: isCSC ? data.balancedPercentage : calculatePercentage(data.balanced || 0, total),
        unbalancedPercentage: isCSC ? data.unbalancedPercentage : calculatePercentage(data.unbalanced || 0, total),
      };
    }
    case "percentage_of_neutral": {
      const total = (data.normal || 0) + (data.high || 0);
      return {
        normal: data.normal || 0,
        high: data.high || 0,
        normalPercentage: isCSC ? data.normalPercentage : calculatePercentage(data.normal || 0, total),
        highPercentage: isCSC ? data.highPercentage : calculatePercentage(data.high || 0, total),
      };
    }
    default:
      return {};
  }
};

function LvFeederAnalysis() {
  const [activeTab, setActiveTab] = useState("current_phase_unbalance");
  const [data, setData] = useState<LvFeederData[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch data dynamically based on active tab
  const fetchLvFeederData = useCallback(async (analysisType: string) => {
    setLoading(true);
    try {
      // const response = await axios.get(`${import.meta.env.VITE_APP_BASE_API}/api/transformer/lvfeeder-analysis/${analysisType}/`);
      const response = await apiClient.get<any>({ url: `/api/transformer/lvfeeder-analysis/${analysisType}/`});
      const transformedData = response.map((region: any) => ({
        key: region.key,
        region: region.region,
        csc: "",
        children: region.children?.map((csc: any) => ({
          key: csc.key,
          region: csc.region,
          csc: csc.csc,
          ...getAnalysisTypeData(analysisType, csc, true),
        })),
        ...getAnalysisTypeData(analysisType, region, false),
      }));
      setData(transformedData);
    } catch (error) {
      console.error("Error fetching LvFeeder data:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchLvFeederData(activeTab);
  }, [activeTab, fetchLvFeederData]);

  // Define columns based on active tab
  const columns = useMemo(() => {
    const baseColumns: ColumnsType<LvFeederData> = [
      {
        title: "Region",
        dataIndex: "region",
        key: "region",
        width: 200,
      },
      {
        title: "CSC",
        dataIndex: "csc",
        key: "csc",
        width: 200,
      },
    ];

    const getMetricColumns = () => {
      const metricColumns = [];
      switch (activeTab) {
        case "transformer_load":
          metricColumns.push(
            {
              title: "Below 20%",
              children: [
                { title: "Count", dataIndex: "below20", width: 100 },
                { title: "%", dataIndex: "below20Percentage", width: 80, render: (value: number) => `${value}%` },
              ],
            },
            {
              title: "20-50%",
              children: [
                { title: "Count", dataIndex: "load20_50", width: 100 },
                { title: "%", dataIndex: "load20_50Percentage", width: 80, render: (value: number) => `${value}%` },
              ],
            },
            {
              title: "50-80%",
              children: [
                { title: "Count", dataIndex: "load50_80", width: 100 },
                { title: "%", dataIndex: "load50_80Percentage", width: 80, render: (value: number) => `${value}%` },
              ],
            },
            {
              title: "80-100%",
              children: [
                { title: "Count", dataIndex: "load80_100", width: 100 },
                { title: "%", dataIndex: "load80_100Percentage", width: 80, render: (value: number) => `${value}%` },
              ],
            },
            {
              title: "Above 100%",
              children: [
                { title: "Count", dataIndex: "above100", width: 100 },
                { title: "%", dataIndex: "above100Percentage", width: 80, render: (value: number) => `${value}%` },
              ],
            }
          );
          break;
        case "current_phase_unbalance":
          metricColumns.push(
            {
              title: "Balanced (≤10%)",
              children: [
                { title: "Count", dataIndex: "balanced", width: 100 },
                { title: "%", dataIndex: "balancedPercentage", width: 80, render: (value: number) => `${value}%` },
              ],
            },
            {
              title: "Unbalanced (>10%)",
              children: [
                { title: "Count", dataIndex: "unbalanced", width: 100 },
                { title: "%", dataIndex: "unbalancedPercentage", width: 80, render: (value: number) => `${value}%` },
              ],
            }
          );
          break;
        case "percentage_of_neutral":
          metricColumns.push(
            {
              title: "Normal (≤20%)",
              children: [
                { title: "Count", dataIndex: "normal", width: 100 },
                { title: "%", dataIndex: "normalPercentage", width: 80, render: (value: number) => `${value}%` },
              ],
            },
            {
              title: "High  (>20%)",
              children: [
                { title: "Count", dataIndex: "high", width: 100 },
                { title: "%", dataIndex: "highPercentage", width: 80, render: (value: number) => `${value}%` },
              ],
            }
          );
          break;
      }

      // Add Total column
      metricColumns.push({
        title: "Total",
        children: [
          {
            title: "Count",
            key: "total",
            width: 100,
            render: (_, record) => {
              switch (activeTab) {
                case "transformer_load":
                  return (record.below20 || 0) + (record.load20_50 || 0) + 
                         (record.load50_80 || 0) + (record.load80_100 || 0) + 
                         (record.above100 || 0);
                case "current_phase_unbalance":
                  return (record.balanced || 0) + (record.unbalanced || 0);
                case "percentage_of_neutral":
                  return (record.normal || 0) + (record.high || 0);
                default:
                  return 0;
              }
            }
          },
          {
            title: "%",
            key: "totalPercentage",
            width: 80,
            render: () => "100%"
          }
        ]
      });

      return metricColumns;
    };

    return [...baseColumns, ...getMetricColumns()];
  }, [activeTab]);

  // Add summary row calculation
  const renderSummary = (currentData: readonly LvFeederData[]) => {
    // Filter out CSC entries to get only region-level data
    const regionData = currentData.filter(item => !item.csc);
    
    const calculateTotals = () => {
      switch (activeTab) {
        case "transformer_load":
          return regionData.reduce((acc, curr) => ({
            below20: (acc.below20 || 0) + (curr.below20 || 0),
            load20_50: (acc.load20_50 || 0) + (curr.load20_50 || 0),
            load50_80: (acc.load50_80 || 0) + (curr.load50_80 || 0),
            load80_100: (acc.load80_100 || 0) + (curr.load80_100 || 0),
            above100: (acc.above100 || 0) + (curr.above100 || 0)
          }), {});
        case "current_phase_unbalance":
          return regionData.reduce((acc, curr) => ({
            balanced: (acc.balanced || 0) + (curr.balanced || 0),
            unbalanced: (acc.unbalanced || 0) + (curr.unbalanced || 0)
          }), {});
        case "percentage_of_neutral":
          return regionData.reduce((acc, curr) => ({
            normal: (acc.normal || 0) + (curr.normal || 0),
            high: (acc.high || 0) + (curr.high || 0)
          }), {});
        default:
          return {};
      }
    };

    const totals = calculateTotals();
    const grandTotal = Object.values(totals).reduce((sum, val) => sum + (val || 0), 0);

    const calculatePercentages = (value: number) => 
      grandTotal > 0 ? ((value / grandTotal) * 100).toFixed(1) : '0';

    const renderSummaryCells = () => {
      const cells = [
        <Table.Summary.Cell index={0} key="title">Grand Total</Table.Summary.Cell>,
        <Table.Summary.Cell index={1} key="empty"></Table.Summary.Cell>
      ];

      switch (activeTab) {
        case "transformer_load":
          cells.push(
            <Table.Summary.Cell key="below20">{totals.below20}</Table.Summary.Cell>,
            <Table.Summary.Cell key="below20p">{calculatePercentages(totals.below20)}%</Table.Summary.Cell>,
            <Table.Summary.Cell key="20-50">{totals.load20_50}</Table.Summary.Cell>,
            <Table.Summary.Cell key="20-50p">{calculatePercentages(totals.load20_50)}%</Table.Summary.Cell>,
            <Table.Summary.Cell key="50-80">{totals.load50_80}</Table.Summary.Cell>,
            <Table.Summary.Cell key="50-80p">{calculatePercentages(totals.load50_80)}%</Table.Summary.Cell>,
            <Table.Summary.Cell key="80-100">{totals.load80_100}</Table.Summary.Cell>,
            <Table.Summary.Cell key="80-100p">{calculatePercentages(totals.load80_100)}%</Table.Summary.Cell>,
            <Table.Summary.Cell key="above100">{totals.above100}</Table.Summary.Cell>,
            <Table.Summary.Cell key="above100p">{calculatePercentages(totals.above100)}%</Table.Summary.Cell>
          );
          break;
        case "current_phase_unbalance":
          cells.push(
            <Table.Summary.Cell key="balanced">{totals.balanced}</Table.Summary.Cell>,
            <Table.Summary.Cell key="balancedp">{calculatePercentages(totals.balanced)}%</Table.Summary.Cell>,
            <Table.Summary.Cell key="unbalanced">{totals.unbalanced}</Table.Summary.Cell>,
            <Table.Summary.Cell key="unbalancedp">{calculatePercentages(totals.unbalanced)}%</Table.Summary.Cell>
          );
          break;
        case "percentage_of_neutral":
          cells.push(
            <Table.Summary.Cell key="normal">{totals.normal}</Table.Summary.Cell>,
            <Table.Summary.Cell key="normalp">{calculatePercentages(totals.normal)}%</Table.Summary.Cell>,
            <Table.Summary.Cell key="high">{totals.high}</Table.Summary.Cell>,
            <Table.Summary.Cell key="highp">{calculatePercentages(totals.high)}%</Table.Summary.Cell>
          );
          break;
      }

      // Add total cells
      cells.push(
        <Table.Summary.Cell key="total">{grandTotal}</Table.Summary.Cell>,
        <Table.Summary.Cell key="totalp">100%</Table.Summary.Cell>
      );

      return cells;
    };

    return (
      <Table.Summary.Row>
        {renderSummaryCells()}
      </Table.Summary.Row>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {Object.entries(LVFEEDER_TABS).map(([key, label]) => (
          <TabPane tab={label} key={key}>
            {loading ? (
              <div className="flex justify-center items-center h-[400px]">
                <CircleLoading />
              </div>
            ) : (
              <>
                <Scrollbar>
                  <Table
                    columns={columns}
                    dataSource={data}
                    bordered
                    size="small"
                    pagination={false}
                    summary={renderSummary}
                  />
                </Scrollbar>
                <div className="flex flex-wrap justify-center gap-4 mt-4">
                  <LvFeederGraph tabKey={key} data={data} />
                  <LvFeederPieChart tabKey={key} data={data} />
                </div>
              </>
            )}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
}

export default LvFeederAnalysis
