# Dependencies
node_modules/
/.pnp
.pnp.js
venv/
__pycache__/
*.py[cod]

# Testing
/coverage
.coverage
htmlcov/
.pytest_cache/
.tox/

# Production
/build
/dist
dist-ssr
*.local

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Django
*.sqlite3
media/
static/
db.sqlite3

# Python
*.pyc
*.pyo
*.pyd
.Python
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Vite
stats.html

# Docker
.docker/
docker-compose.override.yml

# Cache
.cache/
.eslintcache
.stylelintcache

# Misc
.DS_Store
Thumbs.db
