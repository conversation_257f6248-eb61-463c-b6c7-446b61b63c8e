import { useEffect, useState } from 'react';
import { Tabs, type TabsProps, Card } from "antd";
import { Iconify } from "@/components/icon";
import { useParams } from "@/router/hooks";
import userService from "@/api/services/userService";
import { toast } from "sonner";
import type { UserInfo } from "#/entity";
import GeneralTab from './user_account/general-tab';
import SecurityTab from './user_account/security-tab';

// // Create separate tab components
// const GeneralTab = ({ user, onUpdate }: { user: UserInfo; onUpdate: () => void }) => {
//     const onFinish = async (values: any) => {
//         try {
//             await userService.updateUser(user.id, values);
//             toast.success("User updated successfully");
//             onUpdate();
//         } catch (error) {
//             toast.error("Failed to update user");
//         }
//     };

//     return (
//         <Card>
//             {/* Add your form components here similar to general-tab.tsx */}
//         </Card>
//     );
// };

// const SecurityTab = ({ user, onUpdate }: { user: UserInfo; onUpdate: () => void }) => {
//     const onFinish = async (values: any) => {
//         try {
//             await userService.updateUserPassword(user.id, values);
//             toast.success("Password updated successfully");
//             onUpdate();
//         } catch (error) {
//             toast.error("Failed to update password");
//         }
//     };

//     return (
//         <Card>
//             {/* Add your password update form here similar to security-tab.tsx */}
//         </Card>
//     );
// };


export default function UserDetail() {
    const { id } = useParams();
    const [user, setUser] = useState<UserInfo | null>(null);

    const fetchUserData = async () => {
        try {
            const response = await userService.getUser(id);
            setUser(response);
        } catch (error) {
            toast.error("Failed to fetch user details");
        }
    };

    useEffect(() => {
        fetchUserData();
    }, [id]);

    if (!user) {
        return <div>Loading...</div>;
    }

    const items: TabsProps["items"] = [
        {
            key: "1",
            label: (
                <div className="flex items-center">
                    <Iconify icon="solar:user-id-bold" size={24} className="mr-2" />
                    <span>General Information</span>
                </div>
            ),
            children: <GeneralTab user={user} onUpdate={fetchUserData} />,
        },
        {
            key: "2",
            label: (
                <div className="flex items-center">
                    <Iconify icon="solar:key-minimalistic-square-3-bold-duotone" size={24} className="mr-2" />
                    <span>Security Settings</span>
                </div>
            ),
            children: <SecurityTab user={user} onUpdate={fetchUserData} />,
        },
    ];

    return (
        <Card>
            <div className="mb-4">
                <h2 className="text-2xl font-bold">User Details - {user.username}</h2>
                <p className="text-gray-500">Manage user information and security settings</p>
            </div>
            <Tabs defaultActiveKey="1" items={items} />
        </Card>
    );
}




