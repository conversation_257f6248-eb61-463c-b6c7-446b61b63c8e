from django.contrib import admin
from .models import (
    SMSMessage, Reply, Attachment, TimelineEvent, AssignmentHistory,
    ChecklistItem, ProgressReport, ProgressUpdate, Notification,
    ApprovalRequest, ApprovalHistory, AssignmentStatus, DetailedReport,
    MessageTemplate, SystemSettings, Analytics, Department
)

# User is already registered in the account app, so we don't register it here


@admin.register(SMSMessage)
class SMSMessageAdmin(admin.ModelAdmin):
    list_display = ['case_id', 'phone_number', 'status', 'category', 'priority', 'assigned_to', 'timestamp']
    list_filter = ['status', 'category', 'priority', 'timestamp', 'is_archived']
    search_fields = ['phone_number', 'content', 'case_id']
    readonly_fields = ['id', 'timestamp', 'case_id']

    fieldsets = (
        ('Message Details', {
            'fields': ('phone_number', 'content', 'case_id')
        }),
        ('Classification', {
            'fields': ('status', 'category', 'priority', 'tags')
        }),
        ('Assignment', {
            'fields': ('assigned_to',)
        }),
        ('Archive', {
            'fields': ('is_archived', 'archived_at', 'archived_by')
        }),
        ('Timestamps', {
            'fields': ('timestamp',)
        }),
    )


@admin.register(Reply)
class ReplyAdmin(admin.ModelAdmin):
    list_display = ['message', 'sender', 'timestamp', 'is_from_customer']
    list_filter = ['timestamp', 'is_from_customer']
    search_fields = ['content', 'message__case_id']


@admin.register(Attachment)
class AttachmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'size', 'uploaded_by', 'uploaded_at']
    list_filter = ['type', 'uploaded_at']
    search_fields = ['name']


@admin.register(TimelineEvent)
class TimelineEventAdmin(admin.ModelAdmin):
    list_display = ['message', 'type', 'user', 'timestamp']
    list_filter = ['type', 'timestamp']
    search_fields = ['message__case_id', 'description']


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'type', 'user', 'timestamp', 'read']
    list_filter = ['type', 'read', 'timestamp']
    search_fields = ['title', 'message', 'case_id']


@admin.register(ApprovalRequest)
class ApprovalRequestAdmin(admin.ModelAdmin):
    list_display = ['message', 'type', 'priority', 'status', 'requested_by', 'requested_at']
    list_filter = ['type', 'priority', 'status', 'requested_at']
    search_fields = ['description', 'message__case_id']


@admin.register(ProgressReport)
class ProgressReportAdmin(admin.ModelAdmin):
    list_display = ['message', 'reported_by', 'timestamp']
    list_filter = ['timestamp']
    search_fields = ['content', 'message__case_id']


@admin.register(DetailedReport)
class DetailedReportAdmin(admin.ModelAdmin):
    list_display = ['case_id', 'title', 'created_by', 'created_at', 'follow_up_required']
    list_filter = ['follow_up_required', 'created_at']
    search_fields = ['case_id', 'title', 'summary']


@admin.register(MessageTemplate)
class MessageTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'is_active', 'created_at']
    list_filter = ['category', 'is_active', 'created_at']
    search_fields = ['name', 'content']


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'color']
    search_fields = ['name']


@admin.register(Analytics)
class AnalyticsAdmin(admin.ModelAdmin):
    list_display = ['date', 'total_cases', 'resolved_cases', 'pending_cases', 'average_response_time']
    list_filter = ['date']
    readonly_fields = ['date']