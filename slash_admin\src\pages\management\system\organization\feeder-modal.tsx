import { Form, Input, Modal, Select, InputNumber } from "antd";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON> } from "@/types/entity";
import orgService from "@/api/services/orgService";
import { toast } from "sonner";

type FeederModalProps = {
    show: boolean;
    feeder: Feeder | null;
    selectedSubstation?: number;
    onCancel: () => void;
    onOk: () => void;
};

export function FeederModal({ show, feeder, selectedSubstation, onCancel, onOk }: FeederModalProps) {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    // Set form values when editing
    useEffect(() => {
        if (feeder) {
            form.setFieldsValue({
                feeder_name: feeder.feeder_name,
                voltage_level: feeder.voltage_level,
                peak_load: feeder.peak_load,
                length: feeder.length,
                number_of_transformer: feeder.number_of_transformer,
                substation_id: feeder.substation_id // Store the substation ID
            });
        } else {
            form.resetFields();
        }
    }, [feeder, form]);

    const handleOk = () => {
        form.validateFields()
            .then(async (values) => {
                setLoading(true);
                try {
                    if (feeder) {
                        // Include the substation ID when updating
                        await orgService.updateFeeder(feeder.id, {
                            ...values,
                            substation_id: feeder.substation_id // Pass the substation ID
                        });
                        toast.success("Feeder updated successfully");
                    } else {
                        if (!selectedSubstation) {
                            toast.error("No substation selected");
                            return;
                        }
                        await orgService.addFeeder(selectedSubstation, values);
                        toast.success("Feeder created successfully");
                    }
                    onOk();
                    form.resetFields();
                } catch (error: any) {
                    console.error('Feeder Modal Error:', error);
                    toast.error(error.message || "Failed to save feeder");
                } finally {
                    setLoading(false);
                }
            })
            .catch((info) => {
                console.log('Validate Failed:', info);
            });
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={feeder ? "Edit Feeder" : "Add Feeder"}
            open={show}
            onOk={handleOk}
            onCancel={handleCancel}
            confirmLoading={loading}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="feeder_name"
                    label="Feeder Name"
                    rules={[{ required: true, message: 'Please enter feeder name' }]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="voltage_level"
                    label="Voltage Level"
                    rules={[{ required: true, message: 'Please enter Voltage Level' }]}
                >
                    <Select
                        options={[
                            { value: '33KV', label: '33KV' },
                            { value: '15KV', label: '15KV' },
                        ]}
                    />
                </Form.Item>
                <Form.Item
                    name="peak_load"
                    label="Peak Load"
                >
                    <InputNumber 
                        style={{ width: '100%' }}
                        min={0}
                        step={0.01}
                        placeholder="Enter peak load"
                    />
                </Form.Item>
                <Form.Item
                    name="length"
                    label="Length"
                >
                    <InputNumber 
                        style={{ width: '100%' }}
                        min={0}
                        step={0.01}
                        placeholder="Enter length"
                    />
                </Form.Item>
                <Form.Item
                    name="number_of_transformer"
                    label="Number of Transformers"
                >
                    <InputNumber 
                        style={{ width: '100%' }}
                        min={0}
                        placeholder="Enter number of transformers"
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
}







