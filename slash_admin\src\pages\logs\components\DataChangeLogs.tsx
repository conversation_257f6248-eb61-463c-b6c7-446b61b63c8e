import React from 'react';
import { Table, Typography, Tag } from 'antd';
import dayjs from 'dayjs';

const getModelColor = (modelName: string): string => {
    const colors: { [key: string]: string } = {
        'Basestation': 'blue',
        'Transformer Data': 'green',
        'Inspection': 'orange',
        'LV Feeder': 'purple'
    };
    return colors[modelName] || 'default';
};

interface DataChangeLog {
    id: number;
    field_name: string;
    old_value: string;
    new_value: string;
    change_type: string;
    timestamp: string;
    changed_by: string;
    ip_address: string;
    model_name: string;
    record_id: string;
}

interface DataChangeLogsProps {
    loading: boolean;
    logs: DataChangeLog[];
}

const DataChangeLogs: React.FC<DataChangeLogsProps> = ({ loading, logs }) => {
    const columns = [
        {
            title: 'Timestamp',
            dataIndex: 'timestamp',
            render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: 'Model',
            dataIndex: 'model_name',
            render: (text: string) => (
                <Tag color={getModelColor(text)} style={{ minWidth: '100px', textAlign: 'center' }}>
                    {text}
                </Tag>
            ),
        },
        {
            title: 'Record ID',
            dataIndex: 'record_id',
            render: (text: string) => (
                <Typography.Text copyable ellipsis={{ tooltip: text }}>
                    {text}
                </Typography.Text>
            ),
        },
        {
            title: 'Field',
            dataIndex: 'field_name',
            ellipsis: true,
        },
        {
            title: 'Old Value',
            dataIndex: 'old_value',
            render: (value: string) => (
                <Typography.Text delete ellipsis={{ tooltip: value }}>
                    {value || '-'}
                </Typography.Text>
            ),
        },
        {
            title: 'New Value',
            dataIndex: 'new_value',
            render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                    {value || '-'}
                </Typography.Text>
            ),
        },
        {
            title: 'Changed By',
            dataIndex: 'changed_by',
        },
        {
            title: 'IP Address',
            dataIndex: 'ip_address',
        }
    ];

    return (
        <Table
            loading={loading}
            columns={columns}
            dataSource={logs}
            rowKey="id"
            pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
            }}
            className="logs-table"
            bordered
            size="middle"
            scroll={{ x: 1480 }}
        />
    );
};

export default DataChangeLogs;


