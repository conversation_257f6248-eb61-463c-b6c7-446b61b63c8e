#!/usr/bin/env python
"""
Test script to verify batch processing functionality in BasestationsFilteredAPIView.
"""

import os
import sys
import django
import json
import time

# Add the core directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from transformer.models import Basestation

User = get_user_model()


def test_batch_processing():
    """Test the batch processing functionality"""
    
    # Create a test client
    client = Client()
    
    # Create or get a test user
    user, created = User.objects.get_or_create(
        username='testuser_batch',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Login the user
    client.force_login(user)
    print("✅ User authenticated")
    
    # Check how many basestations we have
    total_basestations = Basestation.objects.count()
    print(f"📊 Total basestations in database: {total_basestations}")
    
    if total_basestations == 0:
        print("❌ No basestations found. Please add some test data first.")
        return
    
    # Test 1: Normal request (should work normally)
    print("\n🧪 Test 1: Normal request (pageSize=50)")
    start_time = time.time()
    response = client.get('/api/transformer/basestationsFiltered/?page=1&pageSize=50&searchType=BaseStation')
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Time: {end_time - start_time:.2f} seconds")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Returned {len(data.get('results', []))} items")
    else:
        print(f"❌ Error: {response.content.decode()}")
    
    # Test 2: Large request (should trigger batch processing)
    print("\n🧪 Test 2: Large request (pageSize=1000)")
    start_time = time.time()
    response = client.get('/api/transformer/basestationsFiltered/?page=1&pageSize=1000&searchType=BaseStation')
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Time: {end_time - start_time:.2f} seconds")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Returned {len(data.get('results', []))} items")
        print(f"✅ Total count: {data.get('count', 0)}")
        print(f"✅ Has next: {data.get('next') is not None}")
        print(f"✅ Has previous: {data.get('previous') is not None}")
    else:
        print(f"❌ Error: {response.content.decode()}")
    
    # Test 3: Very large request (should trigger batch processing)
    print("\n🧪 Test 3: Very large request (pageSize=2000)")
    start_time = time.time()
    response = client.get('/api/transformer/basestationsFiltered/?page=1&pageSize=2000&searchType=BaseStation')
    end_time = time.time()
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Time: {end_time - start_time:.2f} seconds")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Returned {len(data.get('results', []))} items")
        print(f"✅ Total count: {data.get('count', 0)}")
    else:
        print(f"❌ Error: {response.content.decode()}")
    
    # Test 4: Test cache locking (concurrent request)
    print("\n🧪 Test 4: Testing cache locking with concurrent requests")
    
    # Start first request (this should set the cache lock)
    import threading
    import queue
    
    results_queue = queue.Queue()
    
    def make_request(client, url, result_queue, request_id):
        start_time = time.time()
        response = client.get(url)
        end_time = time.time()
        result_queue.put({
            'id': request_id,
            'status_code': response.status_code,
            'time': end_time - start_time,
            'content': response.content.decode()[:200] + '...' if len(response.content.decode()) > 200 else response.content.decode()
        })
    
    # Create two threads to make concurrent requests
    url = '/api/transformer/basestationsFiltered/?page=1&pageSize=1500&searchType=BaseStation'
    
    thread1 = threading.Thread(target=make_request, args=(client, url, results_queue, 1))
    thread2 = threading.Thread(target=make_request, args=(client, url, results_queue, 2))
    
    # Start both threads almost simultaneously
    thread1.start()
    time.sleep(0.1)  # Small delay to ensure first request starts first
    thread2.start()
    
    # Wait for both to complete
    thread1.join()
    thread2.join()
    
    # Check results
    results = []
    while not results_queue.empty():
        results.append(results_queue.get())
    
    results.sort(key=lambda x: x['id'])
    
    for result in results:
        print(f"Request {result['id']}: Status {result['status_code']}, Time: {result['time']:.2f}s")
        if result['status_code'] == 429:
            print(f"✅ Request {result['id']} was properly blocked (429 Too Many Requests)")
        elif result['status_code'] == 200:
            print(f"✅ Request {result['id']} completed successfully")
        else:
            print(f"❌ Request {result['id']} failed: {result['content']}")
    
    print("\n✅ All batch processing tests completed!")


if __name__ == '__main__':
    test_batch_processing()
