import { useState, useEffect } from 'react';
import { SMSMessage } from '../types';
import { exportCaseToPDF } from '../utils/pdfExport';
import messageService from '@/api/services/messageService';
import { toast } from 'sonner';

interface PaginatedMessages {
  count: number;
  next: string | null;
  previous: string | null;
  results: SMSMessage[];
}

export function useMessages() {
  const [messages, setMessages] = useState<SMSMessage[]>([]);
  const [paginationInfo, setPaginationInfo] = useState({
    count: 0,
    next: null as string | null,
    previous: null as string | null,
    currentPage: 1,
    totalPages: 1
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  const fetchMessages = async (params?: {
    status?: string;
    category?: string;
    priority?: string;
    assigned_to?: string;
    search?: string;
    page?: number;
    page_size?: number;
    tags?: string;
    date_start?: string;
    date_end?: string;
    archived?: boolean;
    ordering?: string;
  }) => {
    // Prevent rapid successive calls (circuit breaker)
    const now = Date.now();
    if (now - lastFetchTime < 1000) { // Minimum 1 second between calls
      console.log('Skipping fetch - too soon after last call');
      return;
    }
    setLastFetchTime(now);

    try {
      setLoading(true);
      setError(null);

      console.log('Fetching messages with params:', params);
      const response = await messageService.getMessages(params);
      console.log('API Response:', response);

      // Handle different response structures
      let messagesData, paginationData;

      if (response.results) {
        // Paginated response
        messagesData = response.results;
        paginationData = {
          count: response.count || 0,
          next: response.next,
          previous: response.previous,
          currentPage: params?.page || 1,
          totalPages: Math.ceil((response.count || 0) / (params?.page_size || 20))
        };
      } else if (Array.isArray(response)) {
        // Direct array response
        messagesData = response;
        paginationData = {
          count: response.length,
          next: null,
          previous: null,
          currentPage: 1,
          totalPages: 1
        };
      } else {
        throw new Error('Unexpected response format');
      }

      // Update pagination info
      setPaginationInfo(paginationData);

      // Normalize messages from API response
      const normalizedMessages = messagesData.map((msg: any) => ({
        id: msg.id,
        caseId: msg.case_id || `CASE-${msg.id}`,
        category: msg.category || 'general',
        content: msg.content || '',
        status: msg.status || 'new',
        priority: msg.priority || 'medium',
        phone_number: msg.phone_number || '',
        tags: msg.tags || [],
        isArchived: msg.is_archived || false,
        timestamp: new Date(msg.timestamp || Date.now()),
        assigned_to: msg.assigned_to ? {
          id: msg.assigned_to.id,
          name: msg.assigned_to.name || msg.assigned_to.username || 'Unknown',
          email: msg.assigned_to.email || '',
          avatar: msg.assigned_to.avatar || null,
          role: msg.assigned_to.role || 'technician',
          department: msg.assigned_to.department || 'Operations',
          isActive: msg.assigned_to.is_active !== false
        } : undefined,
        attachments: msg.attachments || [],
        replyCount: msg.reply_count || 0,
        lastReply: msg.last_reply || null,
        replies: msg.replies || [],
      }));

      setMessages(normalizedMessages);
      console.log('Normalized messages:', normalizedMessages);
    } catch (err: any) {
      console.error('Error fetching messages:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to fetch messages';
      setError(errorMessage);

      // Don't clear messages on error, keep existing ones
      if (messages.length === 0) {
        setMessages([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch on mount - only once
  useEffect(() => {
    fetchMessages({
      page: 1,
      page_size: 20,
      archived: false,
      ordering: '-timestamp'
    });
  }, []); // Empty dependency array - only run once

  const updateMessageStatus = (messageId: string, status: SMSMessage['status']) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId ? { ...msg, status } : msg
      )
    );
  };

  const getReplies = async (messageId: string) => {
    try {
      const replies = await messageService.getReplies({ message_id: messageId });
      console.log('replies AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', replies);
      // setMessages(prev => 
      //   prev.map(msg => 
      //     msg.id === messageId ? { ...msg, replies } : msg
      //   )
      // );
      return replies;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch replies');
      console.error('Error fetching replies:', err);
      throw err;
    }
  };

  const addReply = async (messageId: string, content: string, sender: any) => {

    try {
      const reply = await messageService.addReply(messageId, content);;
      setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              replies: [
                ...msg.replies,
                {
                  id: `reply-${Date.now()}`,
                  content,
                  timestamp: new Date(),
                  sender,
                  is_from_customer: false,
                  attachments: []
                }
              ],
              status: 'replied' as const
            }
          : msg
      )
    );
      return reply;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add reply');
      console.error('Error adding reply:', err);
      throw err;
    }
    
    
  };

  const assignMessage = (messageId: string, user: any) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, assigned_to: user, status: 'in-progress' as const }
          : msg
      )
    );
  };

  const addTag = (messageId: string, tag: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, tags: [...msg.tags, tag] }
          : msg
      )
    );
  };

  const archiveMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: true, archivedAt: new Date() }
          : msg
      )
    );
  };

  const restoreMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: false, archivedAt: undefined }
          : msg
      )
    );
  };

  const deleteMessage = (messageId: string) => {
    // setMessages(prev => prev.filter(msg => msg.id !== messageId));
    console.log('messageId', messageId);
    try {
      // Note: You might want to implement a soft delete in the backend
      messageService.deleteMessage(messageId);
      toast.success('Message deleted successfully');
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete message');
      console.error('Error deleting message:', err);
    }
  };

  const exportToPDF = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message) {
      exportCaseToPDF(message);
    }
  };
  return {
    messages,
    loading,
    error,
    paginationInfo,
    fetchMessages,
    updateMessageStatus,
    addReply,
    assignMessage,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF,
    getReplies
  };
}