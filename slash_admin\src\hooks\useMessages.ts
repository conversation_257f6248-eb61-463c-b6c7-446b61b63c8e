import { useState, useEffect } from 'react';
import { SMSMessage } from '../types';
import { mockMessages } from '../data/mockData';
import { exportCaseToPDF } from '../utils/pdfExport';
import messageService from '@/api/services/messageService';
import { at } from 'lodash';
import { toast } from 'sonner';

export function useMessages() {
  // const [messages, setMessages] = useState<SMSMessage[]>([]);
  const [messages, setMessages] = useState<SMSMessage[]>(mockMessages);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchMessages();
  }, []);

  console.log('messages***********************', messages);

  const fetchMessages = async (params?: {
    status?: string;
    category?: string;
    priority?: string;
    assigned_to?: string;
    search?: string;
    page?: number;
  }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await messageService.getMessages(params);
      // console.log('response ***********************', response);
      const normalizedMessages = response.map((msg: any) => ({
      id: msg.id,
      caseId: msg.case_id ?? "CASE-2024-001",
      category: msg.category ?? "power-outage",
      content: msg.content ?? "Power is out in my entire neighborhood on Oak Street. Been without electricity for 3 hours now.",
      status:  "new",
      priority: msg.priority ?? "high",
      phone_number: msg.phone_number ?? "+****************",
      tags: msg.tags ?? [],
      isArchived: false,
      timestamp: msg.timestamp ?? new Date("2025-08-14T09:59:58Z"), // corresponds to Aug 14 2025 12:59:58 GMT+0300
      assigned_to: msg.assigned_to ?? null,
      attachments: [],
      replyCount: msg.reply_count ?? 0,
      lastReply: msg.last_reply ?? null,
      replies: msg.replies ?? [],
      }));
    
      setMessages(normalizedMessages);

    // setMessages((prevMessages) => [newMessage]);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch messages');
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  };

  // Simulate real-time message updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly update message statuses or add new messages
      if (Math.random() > 0.8) {
        setMessages(prev => prev.map(msg => {
          if (msg.status === 'new' && Math.random() > 0.7) {
            return { ...msg, status: 'in-progress' as const };
          }
          if (msg.status === 'in-progress' && Math.random() > 0.8) {
            return { ...msg, status: 'replied' as const };
          }
          return msg;
        }));
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const updateMessageStatus = (messageId: string, status: SMSMessage['status']) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId ? { ...msg, status } : msg
      )
    );
  };

  const getReplies = async (messageId: string) => {
    try {
      const replies = await messageService.getReplies(messageId);
      console.log('replies AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', replies);
      // setMessages(prev => 
      //   prev.map(msg => 
      //     msg.id === messageId ? { ...msg, replies } : msg
      //   )
      // );
      return replies;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch replies');
      console.error('Error fetching replies:', err);
      throw err;
    }
  };

  const addReply = async (messageId: string, content: string, sender: any) => {

    try {
      const reply = await messageService.addReply(messageId, content);;
      setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              replies: [
                ...msg.replies,
                {
                  id: `reply-${Date.now()}`,
                  content,
                  timestamp: new Date(),
                  sender,
                  is_from_customer: false,
                  attachments: []
                }
              ],
              status: 'replied' as const
            }
          : msg
      )
    );
      return reply;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add reply');
      console.error('Error adding reply:', err);
      throw err;
    }
    
    
  };

  const assignMessage = (messageId: string, user: any) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, assigned_to: user, status: 'in-progress' as const }
          : msg
      )
    );
  };

  const addTag = (messageId: string, tag: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, tags: [...msg.tags, tag] }
          : msg
      )
    );
  };

  const archiveMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: true, archivedAt: new Date() }
          : msg
      )
    );
  };

  const restoreMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: false, archivedAt: undefined }
          : msg
      )
    );
  };

  const deleteMessage = (messageId: string) => {
    // setMessages(prev => prev.filter(msg => msg.id !== messageId));
    console.log('messageId', messageId);
    try {
      // Note: You might want to implement a soft delete in the backend
      messageService.deleteMessage(messageId);
      toast.success('Message deleted successfully');
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete message');
      console.error('Error deleting message:', err);
    }
  };

  const exportToPDF = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message) {
      exportCaseToPDF(message);
    }
  };
  return {
    messages,
    loading,
    updateMessageStatus,
    addReply,
    assignMessage,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF,
    getReplies
  };
}