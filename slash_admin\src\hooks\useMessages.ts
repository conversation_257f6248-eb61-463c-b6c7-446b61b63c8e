import { useState, useEffect } from 'react';
import { SMSMessage } from '../types';
import { exportCaseToPDF } from '../utils/pdfExport';
import messageService from '@/api/services/messageService';
import { toast } from 'sonner';

interface PaginatedMessages {
  count: number;
  next: string | null;
  previous: string | null;
  results: SMSMessage[];
}

export function useMessages() {
  const [messages, setMessages] = useState<SMSMessage[]>([]);
  const [paginationInfo, setPaginationInfo] = useState({
    count: 0,
    next: null as string | null,
    previous: null as string | null,
    currentPage: 1,
    totalPages: 1
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMessages = async (params?: {
    status?: string;
    category?: string;
    priority?: string;
    assigned_to?: string;
    search?: string;
    page?: number;
    page_size?: number;
    tags?: string;
    date_start?: string;
    date_end?: string;
    archived?: boolean;
    ordering?: string;
  }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await messageService.getMessages(params);

      // Update pagination info
      setPaginationInfo({
        count: response.count,
        next: response.next,
        previous: response.previous,
        currentPage: params?.page || 1,
        totalPages: Math.ceil(response.count / (params?.page_size || 20))
      });

      // Normalize messages from API response
      const normalizedMessages = response.results.map((msg: any) => ({
        id: msg.id,
        caseId: msg.case_id,
        category: msg.category,
        content: msg.content,
        status: msg.status,
        priority: msg.priority,
        phone_number: msg.phone_number,
        tags: msg.tags || [],
        isArchived: msg.is_archived || false,
        timestamp: new Date(msg.timestamp),
        assigned_to: msg.assigned_to ? {
          id: msg.assigned_to.id,
          name: msg.assigned_to.name || msg.assigned_to.username,
          email: msg.assigned_to.email,
          avatar: msg.assigned_to.avatar
        } : null,
        attachments: msg.attachments || [],
        replyCount: msg.reply_count || 0,
        lastReply: msg.last_reply || null,
        replies: msg.replies || [],
      }));

      setMessages(normalizedMessages);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch messages');
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch on mount
  useEffect(() => {
    fetchMessages();
  }, []);

  const updateMessageStatus = (messageId: string, status: SMSMessage['status']) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId ? { ...msg, status } : msg
      )
    );
  };

  const getReplies = async (messageId: string) => {
    try {
      const replies = await messageService.getReplies(messageId);
      console.log('replies AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA', replies);
      // setMessages(prev => 
      //   prev.map(msg => 
      //     msg.id === messageId ? { ...msg, replies } : msg
      //   )
      // );
      return replies;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch replies');
      console.error('Error fetching replies:', err);
      throw err;
    }
  };

  const addReply = async (messageId: string, content: string, sender: any) => {

    try {
      const reply = await messageService.addReply(messageId, content);;
      setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              replies: [
                ...msg.replies,
                {
                  id: `reply-${Date.now()}`,
                  content,
                  timestamp: new Date(),
                  sender,
                  is_from_customer: false,
                  attachments: []
                }
              ],
              status: 'replied' as const
            }
          : msg
      )
    );
      return reply;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add reply');
      console.error('Error adding reply:', err);
      throw err;
    }
    
    
  };

  const assignMessage = (messageId: string, user: any) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, assigned_to: user, status: 'in-progress' as const }
          : msg
      )
    );
  };

  const addTag = (messageId: string, tag: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, tags: [...msg.tags, tag] }
          : msg
      )
    );
  };

  const archiveMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: true, archivedAt: new Date() }
          : msg
      )
    );
  };

  const restoreMessage = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId
          ? { ...msg, isArchived: false, archivedAt: undefined }
          : msg
      )
    );
  };

  const deleteMessage = (messageId: string) => {
    // setMessages(prev => prev.filter(msg => msg.id !== messageId));
    console.log('messageId', messageId);
    try {
      // Note: You might want to implement a soft delete in the backend
      messageService.deleteMessage(messageId);
      toast.success('Message deleted successfully');
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete message');
      console.error('Error deleting message:', err);
    }
  };

  const exportToPDF = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message) {
      exportCaseToPDF(message);
    }
  };
  return {
    messages,
    loading,
    error,
    paginationInfo,
    fetchMessages,
    updateMessageStatus,
    addReply,
    assignMessage,
    addTag,
    archiveMessage,
    restoreMessage,
    deleteMessage,
    exportToPDF,
    getReplies
  };
}