import jsPDF from 'jspdf';
import { SMSMessage, Case } from '../types';
import { format } from 'date-fns';

export const exportCaseToPDF = async (message: SMSMessage, caseData?: Case) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const margin = 20;
  let yPosition = margin;

  // Helper function to add text with word wrapping
  const addText = (text: string, fontSize = 12, isBold = false) => {
    pdf.setFontSize(fontSize);
    if (isBold) {
      pdf.setFont('helvetica', 'bold');
    } else {
      pdf.setFont('helvetica', 'normal');
    }
    
    const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin);
    pdf.text(lines, margin, yPosition);
    yPosition += lines.length * (fontSize * 0.4) + 5;
    
    // Check if we need a new page
    if (yPosition > pdf.internal.pageSize.getHeight() - margin) {
      pdf.addPage();
      yPosition = margin;
    }
  };

  // Header
  addText('ElectriCare SMS Support - Case Report', 16, true);
  yPosition += 10;

  // Case Information
  addText('CASE INFORMATION', 14, true);
  addText(`Case ID: ${message.caseId}`);
  addText(`Phone Number: ${message.phone_number}`);
  addText(`Category: ${message.category.replace('-', ' ').toUpperCase()}`);
  addText(`Priority: ${message.priority.toUpperCase()}`);
  addText(`Status: ${message.status.replace('-', ' ').toUpperCase()}`);
  addText(`Created: ${format(message.timestamp, 'PPpp')}`);
  
  if (message.assigned_to) {
    addText(`Assigned to: ${message.assigned_to.name} (${message.assigned_to.department})`);
  }
  
  yPosition += 10;

  // Original Message
  addText('ORIGINAL MESSAGE', 14, true);
  addText(`Received: ${format(message.timestamp, 'PPpp')}`);
  addText(`Content: ${message.content}`);
  yPosition += 10;

  // Replies
  if (message.replies.length > 0) {
    addText('CONVERSATION HISTORY', 14, true);
    message.replies.forEach((reply, index) => {
      addText(`${index + 1}. ${format(reply.timestamp, 'PPpp')} - ${reply.sender.name}:`);
      addText(reply.content);
      yPosition += 5;
    });
    yPosition += 10;
  }

  // Tags
  if (message.tags.length > 0) {
    addText('TAGS', 14, true);
    addText(message.tags.join(', '));
    yPosition += 10;
  }

  // Case Timeline (if available)
  if (caseData?.timeline && caseData.timeline.length > 0) {
    addText('CASE TIMELINE', 14, true);
    caseData.timeline.forEach((event, index) => {
      addText(`${index + 1}. ${format(event.timestamp, 'PPpp')} - ${event.user.name}: ${event.description}`);
    });
    yPosition += 10;
  }

  // Footer
  const pageCount = pdf.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    pdf.setPage(i);
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.text(
      `Generated on ${format(new Date(), 'PPpp')} - Page ${i} of ${pageCount}`,
      margin,
      pdf.internal.pageSize.getHeight() - 10
    );
  }

  // Save the PDF
  pdf.save(`case-${message.caseId}-${format(new Date(), 'yyyy-MM-dd')}.pdf`);
};

export const exportMessageListToPDF = async (messages: SMSMessage[]) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const margin = 20;
  let yPosition = margin;

  // Helper function to add text with word wrapping
  const addText = (text: string, fontSize = 12, isBold = false) => {
    pdf.setFontSize(fontSize);
    if (isBold) {
      pdf.setFont('helvetica', 'bold');
    } else {
      pdf.setFont('helvetica', 'normal');
    }
    
    const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin);
    pdf.text(lines, margin, yPosition);
    yPosition += lines.length * (fontSize * 0.4) + 5;
    
    // Check if we need a new page
    if (yPosition > pdf.internal.pageSize.getHeight() - margin) {
      pdf.addPage();
      yPosition = margin;
    }
  };

  // Header
  addText('ElectriCare SMS Support - Messages Report', 16, true);
  addText(`Generated: ${format(new Date(), 'PPpp')}`);
  addText(`Total Messages: ${messages.length}`);
  yPosition += 10;

  // Messages
  messages.forEach((message, index) => {
    addText(`${index + 1}. ${message.caseId} - ${message.phone_number}`, 12, true);
    addText(`Category: ${message.category} | Priority: ${message.priority} | Status: ${message.status}`);
    addText(`Date: ${format(message.timestamp, 'PPpp')}`);
    addText(`Message: ${message.content.substring(0, 200)}${message.content.length > 200 ? '...' : ''}`);
    
    if (message.assigned_to) {
      addText(`Assigned to: ${message.assigned_to.name}`);
    }
    
    yPosition += 10;
  });

  // Footer
  const pageCount = pdf.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    pdf.setPage(i);
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.text(
      `Page ${i} of ${pageCount}`,
      margin,
      pdf.internal.pageSize.getHeight() - 10
    );
  }

  // Save the PDF
  pdf.save(`messages-report-${format(new Date(), 'yyyy-MM-dd')}.pdf`);
};