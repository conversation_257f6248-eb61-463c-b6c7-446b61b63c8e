import { themeVars } from "@/theme/theme.css";
import { fBytes } from "@/utils/format-number";
import { Typography, Upload, Button } from "antd";
import type { UploadChangeParam, UploadFile, UploadProps } from "antd/es/upload";
import { useState } from "react";
import { Iconify } from "../icon";
import { StyledUploadAvatar } from "./styles";
import { beforeAvatarUpload, getBlobUrl } from "./utils";
import { useUserInfo } from "@/store/userStore";
import { toast } from "sonner";
import userService from "@/api/services/userService";
import { getFullAvatarUrl } from "@/utils/url";
import userStore from "@/store/userStore";
import { StorageEnum } from "#/enum";

interface Props extends UploadProps {
	defaultAvatar?: string;
	helperText?: React.ReactElement | string;
}
export function UploadAvatar({ helperText, defaultAvatar = "", ...other }: Props) {
	const [imageUrl, setImageUrl] = useState<string>(getFullAvatarUrl(defaultAvatar));
	const [isHover, setIsHover] = useState(false);
	const [uploading, setUploading] = useState(false);
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const { id } = useUserInfo();

	const handelHover = (hover: boolean) => {
		setIsHover(hover);
	};

	const handleChange: UploadProps["onChange"] = (info: UploadChangeParam<UploadFile>) => {
		if (info.file.status === "uploading") {
			return;
		}
		if (info.file.originFileObj) {
			setSelectedFile(info.file.originFileObj);
			setImageUrl(getBlobUrl(info.file.originFileObj));
		}
	};

	const handleUpload = async () => {
		if (!selectedFile || !id) {
			toast.error("Please select an image first");
			return;
		}

		try {
			setUploading(true);
			const newAvatarUrl = await userService.updateAvatar(id, selectedFile);

			// Update the userStore with the new avatar
			const updatedUserInfo = {
				...userStore.getState().userInfo,
				avatar: newAvatarUrl,
			};
			userStore.setState({ userInfo: updatedUserInfo });

			// Persist the updated userStore into localStorage
			const storageData = {
				[StorageEnum.UserInfo]: updatedUserInfo,
				[StorageEnum.UserToken]: userStore.getState().userToken,
			};
			localStorage.setItem("userStore", JSON.stringify(storageData));

			setImageUrl(newAvatarUrl);
			toast.success("Avatar updated successfully");
			setSelectedFile(null);
		} catch (error) {
			toast.error("Failed to update avatar");
			console.error("Avatar update failed:", error);
		} finally {
			setUploading(false);
		}
	};

	const renderUploadButton = selectedFile && (
		<Button type="primary" onClick={handleUpload} loading={uploading} className="mt-4">
			Upload Avatar
		</Button>
	);

	console.log("getFullAvatarUrl(imageUrl)", getFullAvatarUrl(imageUrl), "imageUrl", imageUrl);

	const renderPreview = (
		<img src={selectedFile ? imageUrl : getFullAvatarUrl(imageUrl)} alt="" className="absolute rounded-full" />
	);

	const renderPlaceholder = (
		<div
			style={{
				backgroundColor: !imageUrl || isHover ? themeVars.colors.background.neutral : "transparent",
			}}
			className="absolute z-10 flex h-full w-full flex-col items-center justify-center"
		>
			<Iconify icon="solar:camera-add-bold" size={32} />
			<div className="mt-1 text-xs">Upload Photo</div>
		</div>
	);

	const renderContent = (
		<div
			className="relative flex h-full w-full items-center justify-center overflow-hidden rounded-full"
			onMouseEnter={() => handelHover(true)}
			onMouseLeave={() => handelHover(false)}
		>
			{imageUrl ? renderPreview : null}
			{!imageUrl || isHover ? renderPlaceholder : null}
		</div>
	);

	const defaultHelperText = (
		<Typography.Text type="secondary" style={{ fontSize: 12 }}>
			Allowed *.jpeg, *.jpg, *.png, *.gif
			<br /> max size of {fBytes(3145728)}
		</Typography.Text>
	);
	const renderHelpText = <div className="text-center">{helperText || defaultHelperText}</div>;

	return (
		<StyledUploadAvatar>
			<div className="flex flex-col items-center">
				<Upload
					name="avatar"
					showUploadList={false}
					listType="picture-circle"
					className="avatar-uploader !flex items-center justify-center"
					{...other}
					beforeUpload={beforeAvatarUpload}
					onChange={handleChange}
				>
					{renderContent}
				</Upload>
				{renderHelpText}
				{renderUploadButton}
			</div>
		</StyledUploadAvatar>
	);
}
