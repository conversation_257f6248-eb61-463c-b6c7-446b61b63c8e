#!/usr/bin/env python
"""
Test script for sending SMS messages via SMPP.
Usage: python test_sms_send.py
"""

import os
import sys
import django
import time

# Add the core directory to Python path
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from sms_app.sms_handler import SMPPClient, send_sms_via_smpp, start_smpp_client, get_smpp_status


def test_direct_smpp_send():
    """Test sending SMS directly via SMPP client"""
    print("🧪 Testing Direct SMPP SMS Sending")
    print("=" * 40)
    
    # Create SMPP client
    client = SMPPClient()
    
    try:
        # Connect
        print("🔌 Connecting to SMPP server...")
        if not client.connect():
            print("❌ Failed to connect to SMPP server")
            return False
        
        print("✅ Connected successfully")
        
        # Test phone number (replace with actual test number)
        test_number = input("📱 Enter test phone number (e.g., 251910118088): ").strip()
        if not test_number:
            test_number = "251910118088"  # Default test number
        
        # Test message
        test_message = input("💬 Enter test message (or press Enter for default): ").strip()
        if not test_message:
            test_message = "Hello! This is a test SMS from the Python SMPP client."
        
        print(f"📤 Sending SMS to {test_number}...")
        print(f"📝 Message: {test_message}")
        
        # Send SMS
        success = client.send_sms(test_number, test_message)
        
        if success:
            print("✅ SMS sent successfully!")
        else:
            print("❌ Failed to send SMS")
        
        return success
        
    except Exception as e:
        print(f"❌ Error during SMS send test: {e}")
        return False
    finally:
        # Disconnect
        client.disconnect()
        print("🔌 Disconnected from SMPP server")


def test_integrated_sms_send():
    """Test sending SMS via integrated handler"""
    print("\n🧪 Testing Integrated SMS Sending")
    print("=" * 40)
    
    try:
        # Start SMPP client
        print("🚀 Starting SMPP client...")
        if not start_smpp_client():
            print("❌ Failed to start SMPP client")
            return False
        
        # Wait a moment for connection
        time.sleep(2)
        
        # Check status
        status = get_smpp_status()
        print(f"📊 SMPP Status: {status}")
        
        if not status['connected']:
            print("❌ SMPP client not connected")
            return False
        
        # Test phone number
        test_number = input("📱 Enter test phone number (e.g., 251910118088): ").strip()
        if not test_number:
            test_number = "251910118088"
        
        # Test message
        test_message = input("💬 Enter test message (or press Enter for default): ").strip()
        if not test_message:
            test_message = "Hello! This is a test SMS from the integrated handler."
        
        print(f"📤 Sending SMS to {test_number}...")
        print(f"📝 Message: {test_message}")
        
        # Send SMS via integrated handler
        success = send_sms_via_smpp(test_number, test_message)
        
        if success:
            print("✅ SMS sent successfully via integrated handler!")
        else:
            print("❌ Failed to send SMS via integrated handler")
        
        return success
        
    except Exception as e:
        print(f"❌ Error during integrated SMS send test: {e}")
        return False


def test_multiple_sms():
    """Test sending multiple SMS messages"""
    print("\n🧪 Testing Multiple SMS Sending")
    print("=" * 40)
    
    try:
        # Get test numbers
        numbers_input = input("📱 Enter phone numbers (comma-separated): ").strip()
        if not numbers_input:
            numbers = ["251910118088", "251910118088"]  # Default test numbers
        else:
            numbers = [num.strip() for num in numbers_input.split(',')]
        
        message = input("💬 Enter message for all numbers: ").strip()
        if not message:
            message = "Bulk test message from Python SMPP client"
        
        print(f"📤 Sending SMS to {len(numbers)} numbers...")
        
        success_count = 0
        for i, number in enumerate(numbers, 1):
            print(f"📱 Sending to {number} ({i}/{len(numbers)})...")
            
            if send_sms_via_smpp(number, f"{message} (Message {i})"):
                print(f"  ✅ Sent to {number}")
                success_count += 1
            else:
                print(f"  ❌ Failed to send to {number}")
            
            # Small delay between messages
            time.sleep(1)
        
        print(f"📊 Results: {success_count}/{len(numbers)} messages sent successfully")
        return success_count == len(numbers)
        
    except Exception as e:
        print(f"❌ Error during multiple SMS test: {e}")
        return False


def main():
    """Run SMS sending tests"""
    print("📱 SMS Sending Test Suite")
    print("=" * 50)
    
    tests = []
    
    print("\nSelect test to run:")
    print("1. Direct SMPP Send Test")
    print("2. Integrated Handler Send Test")
    print("3. Multiple SMS Send Test")
    print("4. Run All Tests")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        tests = [test_direct_smpp_send]
    elif choice == "2":
        tests = [test_integrated_sms_send]
    elif choice == "3":
        tests = [test_multiple_sms]
    elif choice == "4":
        tests = [test_direct_smpp_send, test_integrated_sms_send, test_multiple_sms]
    else:
        print("❌ Invalid choice")
        return
    
    print(f"\n🚀 Running {len(tests)} test(s)...")
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All SMS sending tests passed!")
    else:
        print(f"⚠️ {len(tests) - passed} test(s) failed")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
