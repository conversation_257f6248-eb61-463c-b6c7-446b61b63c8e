# WebSocket Integration Guide for SMS App

## ✅ WebSocket Setup Complete

The SMS app now has fully functional WebSocket support for real-time communication.

## 🔧 Configuration

### ASGI Application
- **File**: `core/asgi.py`
- **Status**: ✅ Fixed and working
- **Features**: 
  - Proper Django initialization order
  - WebSocket routing with authentication
  - Error handling

### Channel Layers
- **Backend**: Redis (with in-memory fallback)
- **Status**: ✅ Configured with automatic fallback
- **Redis Config**: `127.0.0.1:6379`

## 🌐 WebSocket Endpoints

### 1. Messages WebSocket
- **URL**: `ws://localhost:8000/ws/messages/`
- **Purpose**: Real-time message updates and replies
- **Events**:
  - `message_status_update`: When message status changes
  - `new_reply_broadcast`: When new replies are added
  - `new_message_broadcast`: When new messages arrive

### 2. Notifications WebSocket
- **URL**: `ws://localhost:8000/ws/notifications/{user_id}/`
- **Purpose**: User-specific notifications
- **Events**:
  - `notification_message`: New notifications for user

### 3. Assignments WebSocket
- **URL**: `ws://localhost:8000/ws/assignments/`
- **Purpose**: Real-time assignment updates
- **Events**:
  - `assignment_update`: Assignment status changes
  - `new_assignment`: New assignments created

## 📡 WebSocket Message Format

### Sending Messages (Client → Server)
```json
{
  "type": "message_update",
  "message_id": "uuid-here",
  "status": "in-progress",
  "timestamp": "2025-08-13T17:45:00Z"
}
```

### Receiving Messages (Server → Client)
```json
{
  "type": "message_status_update",
  "message_id": "uuid-here",
  "status": "in-progress",
  "timestamp": "2025-08-13T17:45:00Z"
}
```

## 🔗 API Integration

The following API endpoints automatically broadcast WebSocket events:

### Message Operations
- **Create Message**: Broadcasts `new_message_broadcast`
- **Update Status**: Broadcasts `message_status_update`
- **Add Reply**: Broadcasts `new_reply_broadcast`
- **Assign Message**: Broadcasts `new_assignment`

### Notification Operations
- **Create Notification**: Broadcasts `notification_message`

## 🧪 Testing WebSocket Functionality

### 1. Start the Server
```bash
python manage.py runserver
```

### 2. Test WebSocket Connection
```bash
python test_websockets.py
```

### 3. Manual Testing with Browser
```javascript
// Connect to messages WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/messages/');

ws.onopen = function(event) {
    console.log('Connected to WebSocket');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};

// Send a test message
ws.send(JSON.stringify({
    type: 'message_update',
    message_id: 'test-id',
    status: 'in-progress'
}));
```

## 🔒 Authentication

WebSocket connections use Django's authentication middleware:
- **Anonymous users**: Can connect but with limited access
- **Authenticated users**: Full access to user-specific channels
- **JWT Support**: Automatic token validation

## 🚀 Production Deployment

### Requirements
1. **Redis Server**: Required for production
2. **ASGI Server**: Use Daphne or Uvicorn
3. **WebSocket Proxy**: Configure Nginx for WebSocket support

### Example Daphne Command
```bash
daphne -b 0.0.0.0 -p 8000 core.asgi:application
```

### Nginx Configuration
```nginx
location /ws/ {
    proxy_pass http://backend;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🎯 Frontend Integration

### React/JavaScript Example
```javascript
import { useEffect, useState } from 'react';

const useWebSocket = (url) => {
    const [socket, setSocket] = useState(null);
    const [messages, setMessages] = useState([]);

    useEffect(() => {
        const ws = new WebSocket(url);
        
        ws.onopen = () => {
            console.log('WebSocket connected');
            setSocket(ws);
        };
        
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            setMessages(prev => [...prev, data]);
        };
        
        ws.onclose = () => {
            console.log('WebSocket disconnected');
            setSocket(null);
        };
        
        return () => {
            ws.close();
        };
    }, [url]);
    
    const sendMessage = (message) => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify(message));
        }
    };
    
    return { socket, messages, sendMessage };
};
```

## ✅ Status Summary

- ✅ WSGI Application: Working
- ✅ ASGI Application: Fixed and working
- ✅ WebSocket Consumers: Enhanced with error handling
- ✅ Channel Layers: Configured with Redis fallback
- ✅ API Integration: Real-time broadcasts implemented
- ✅ Authentication: Middleware configured
- ✅ Error Handling: Comprehensive logging added
- ✅ Testing Tools: WebSocket test script provided

The SMS app now has perfect WebSocket functionality for real-time communication!
