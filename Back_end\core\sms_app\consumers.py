import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, <PERSON>, F, Case, When
from .models import SMSMessage, Notification, Reply
from .serializers import SMSMessageSerializer, NotificationSerializer

User = get_user_model()
logger = logging.getLogger(__name__)


class MessageConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time message updates"""

    async def connect(self):
        logger.info("🔌 MessageConsumer.connect() called")
        try:
            logger.info("=== WebSocket Connection Attempt ===")
            logger.info(f"Scope type: {self.scope.get('type')}")
            logger.info(f"Path: {self.scope.get('path')}")
            logger.info(f"Headers: {dict(self.scope.get('headers', []))}")
            logger.info(f"Query string: {self.scope.get('query_string', b'').decode()}")

            # Get user from scope (set by middleware)
            self.user = self.scope.get('user')
            self.room_group_name = 'messages'

            logger.info(f"WebSocket connection attempt from {self.scope.get('client', ['unknown', 'unknown'])[0]}")
            logger.info(f"User from scope: {self.user}")

            # Join room group
            logger.info(f"Adding to channel group: {self.room_group_name}")
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )

            logger.info("Accepting WebSocket connection...")
            await self.accept()
            logger.info("✅ WebSocket connection accepted")

            # Log connection with user info
            user_info = f"user: {self.user.username}" if self.user and not self.user.is_anonymous else "anonymous"
            logger.info(f"WebSocket connected to {self.room_group_name} ({user_info})")

            # Send connection confirmation
            confirmation_message = {
                'type': 'connection_established',
                'message': 'Connected to messages WebSocket',
                'timestamp': timezone.now().isoformat(),
                'user': user_info
            }
            logger.info(f"Sending confirmation: {confirmation_message}")
            await self.send(text_data=json.dumps(confirmation_message))
            logger.info("✅ Connection confirmation sent")

        except Exception as e:
            logger.error(f"❌ Error connecting to WebSocket: {e}")
            import traceback
            logger.error(f"WebSocket connection traceback: {traceback.format_exc()}")
            await self.close()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')

            if message_type == 'message_update':
                # Handle message status updates
                await self.handle_message_update(text_data_json)
            elif message_type == 'new_reply':
                # Handle new replies
                await self.handle_new_reply(text_data_json)
            elif message_type == 'get_messages':
                # Handle paginated message requests
                await self.handle_get_messages(text_data_json)
            elif message_type == 'get_replies':
                # Handle paginated reply requests
                await self.handle_get_replies(text_data_json)
            elif message_type == 'add_reply':
                # Handle adding new reply
                await self.handle_add_reply(text_data_json)
            elif message_type == 'update_status':
                # Handle status updates
                await self.handle_update_status(text_data_json)
            elif message_type == 'ping':
                # Handle ping requests
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                }))
            else:
                logger.warning(f"Unknown message type: {message_type}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received: {e}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")
    
    async def handle_message_update(self, data):
        """Handle message status updates"""
        message_id = data.get('message_id')
        status = data.get('status')
        
        # Update message in database
        await self.update_message_status(message_id, status)
        
        # Broadcast to all connected clients
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'message_status_update',
                'message_id': message_id,
                'status': status,
                'timestamp': data.get('timestamp')
            }
        )
    
    async def handle_new_reply(self, data):
        """Handle new replies to messages"""
        message_id = data.get('message_id')
        content = data.get('content')
        user_id = data.get('user_id')
        
        # Save reply to database
        reply = await self.create_reply(message_id, content, user_id)
        
        # Broadcast to all connected clients
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'new_reply_broadcast',
                'message_id': message_id,
                'reply': {
                    'id': str(reply.id),
                    'content': reply.content,
                    'timestamp': reply.timestamp.isoformat(),
                    'sender': reply.sender.username
                }
            }
        )
    
    async def message_status_update(self, event):
        """Send message status update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'message_status_update',
            'message_id': event['message_id'],
            'status': event['status'],
            'timestamp': event['timestamp']
        }))
    
    async def new_reply_broadcast(self, event):
        """Send new reply to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'new_reply',
            'message_id': event['message_id'],
            'reply': event['reply']
        }))
    
    async def new_message_broadcast(self, event):
        """Send new message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'message': event['message']
        }))
    
    @database_sync_to_async
    def update_message_status(self, message_id, status):
        try:
            message = SMSMessage.objects.get(id=message_id)
            message.status = status
            message.save()
            logger.info(f"Updated message {message_id} status to {status}")
            return message
        except SMSMessage.DoesNotExist:
            logger.error(f"Message {message_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error updating message status: {e}")
            return None
    
    async def handle_get_messages(self, data):
        """Handle paginated message requests"""
        try:
            request_id = data.get('request_id')
            params = data.get('params', {})

            # Get paginated messages
            result = await self.get_paginated_messages(params)

            # Send response back to client
            await self.send(text_data=json.dumps({
                'type': 'messages_response',
                'request_id': request_id,
                'data': result
            }))
        except Exception as e:
            logger.error(f"Error handling get_messages: {e}")
            await self.send(text_data=json.dumps({
                'type': 'messages_response',
                'request_id': data.get('request_id'),
                'error': str(e)
            }))

    async def handle_get_replies(self, data):
        """Handle paginated reply requests"""
        try:
            request_id = data.get('request_id')
            params = data.get('params', {})

            # Get paginated replies
            result = await self.get_paginated_replies(params)

            # Send response back to client
            await self.send(text_data=json.dumps({
                'type': 'replies_response',
                'request_id': request_id,
                'data': result
            }))
        except Exception as e:
            logger.error(f"Error handling get_replies: {e}")
            await self.send(text_data=json.dumps({
                'type': 'replies_response',
                'request_id': data.get('request_id'),
                'error': str(e)
            }))

    async def handle_add_reply(self, data):
        """Handle adding new reply"""
        try:
            request_id = data.get('request_id')
            message_id = data.get('message_id')
            content = data.get('content')
            user = self.scope.get('user')

            if not user or not user.is_authenticated:
                await self.send(text_data=json.dumps({
                    'type': 'add_reply_response',
                    'request_id': request_id,
                    'error': 'Authentication required'
                }))
                return

            # Add reply
            reply = await self.create_reply_authenticated(message_id, content, user)

            if reply:
                # Broadcast new reply to all connected clients
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'new_reply_broadcast',
                        'message_id': message_id,
                        'reply': {
                            'id': str(reply.id),
                            'content': reply.content,
                            'timestamp': reply.timestamp.isoformat(),
                            'sender': {
                                'id': reply.sender.id,
                                'name': reply.sender.get_full_name() or reply.sender.username
                            },
                            'is_from_customer': reply.is_from_customer
                        }
                    }
                )

                # Send success response
                await self.send(text_data=json.dumps({
                    'type': 'add_reply_response',
                    'request_id': request_id,
                    'data': {'success': True, 'reply_id': str(reply.id)}
                }))
            else:
                await self.send(text_data=json.dumps({
                    'type': 'add_reply_response',
                    'request_id': request_id,
                    'error': 'Failed to create reply'
                }))

        except Exception as e:
            logger.error(f"Error handling add_reply: {e}")
            await self.send(text_data=json.dumps({
                'type': 'add_reply_response',
                'request_id': data.get('request_id'),
                'error': str(e)
            }))

    async def handle_update_status(self, data):
        """Handle message status updates"""
        try:
            request_id = data.get('request_id')
            message_id = data.get('message_id')
            status = data.get('status')

            # Update message status
            success = await self.update_message_status_ws(message_id, status)

            if success:
                # Broadcast status update to all connected clients
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'message_status_update',
                        'message_id': message_id,
                        'status': status,
                        'timestamp': timezone.now().isoformat()
                    }
                )

                # Send success response
                await self.send(text_data=json.dumps({
                    'type': 'update_status_response',
                    'request_id': request_id,
                    'data': {'success': True}
                }))
            else:
                await self.send(text_data=json.dumps({
                    'type': 'update_status_response',
                    'request_id': request_id,
                    'error': 'Failed to update status'
                }))

        except Exception as e:
            logger.error(f"Error handling update_status: {e}")
            await self.send(text_data=json.dumps({
                'type': 'update_status_response',
                'request_id': data.get('request_id'),
                'error': str(e)
            }))

    @database_sync_to_async
    def create_reply(self, message_id, content, user_id):
        try:
            message = SMSMessage.objects.get(id=message_id)
            user = User.objects.get(id=user_id)
            reply = Reply.objects.create(
                message=message,
                content=content,
                sender=user,
                is_from_customer=False
            )
            logger.info(f"Created reply for message {message_id} by user {user_id}")
            return reply
        except SMSMessage.DoesNotExist:
            logger.error(f"Message {message_id} not found")
            return None
        except User.DoesNotExist:
            logger.error(f"User {user_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error creating reply: {e}")
            return None

    @database_sync_to_async
    def create_reply_authenticated(self, message_id, content, user):
        try:
            message = SMSMessage.objects.get(id=message_id)
            reply = Reply.objects.create(
                message=message,
                content=content,
                sender=user,
                is_from_customer=False
            )
            logger.info(f"Created reply for message {message_id} by user {user.id}")
            return reply
        except SMSMessage.DoesNotExist:
            logger.error(f"Message {message_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error creating reply: {e}")
            return None

    @database_sync_to_async
    def update_message_status_ws(self, message_id, status):
        try:
            message = SMSMessage.objects.get(id=message_id)
            message.status = status
            message.save()
            logger.info(f"Updated message {message_id} status to {status}")
            return True
        except SMSMessage.DoesNotExist:
            logger.error(f"Message {message_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error updating message status: {e}")
            return False

    @database_sync_to_async
    def get_paginated_messages(self, params):
        try:
            from django.core.paginator import Paginator
            from django.db.models import Q, Max, F, Case, When
            from .serializers import SMSMessageListSerializer

            # Build queryset with filters
            queryset = SMSMessage.objects.all()

            # Apply filters
            if params.get('status'):
                queryset = queryset.filter(status=params['status'])
            if params.get('category'):
                queryset = queryset.filter(category=params['category'])
            if params.get('priority'):
                queryset = queryset.filter(priority=params['priority'])
            if params.get('assigned_to'):
                queryset = queryset.filter(assigned_to_id=params['assigned_to'])
            if params.get('search'):
                search = params['search']
                queryset = queryset.filter(
                    Q(content__icontains=search) |
                    Q(phone_number__icontains=search) |
                    Q(case_id__icontains=search)
                )

            # Handle archived filter
            archived = params.get('archived', False)
            if not archived:
                queryset = queryset.filter(is_archived=False)

            # Annotate and order
            queryset = queryset.annotate(
                last_reply_time=Max('replies__timestamp')
            ).annotate(
                sort_time=Case(
                    When(last_reply_time__isnull=False, then=F('last_reply_time')),
                    default=F('timestamp')
                )
            ).order_by('-sort_time')

            # Pagination
            page = params.get('page', 1)
            page_size = params.get('page_size', 20)
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)

            # Serialize data
            serializer = SMSMessageListSerializer(page_obj.object_list, many=True)

            return {
                'results': serializer.data,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': page_obj.number,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None
            }
        except Exception as e:
            logger.error(f"Error getting paginated messages: {e}")
            return {'error': str(e)}

    @database_sync_to_async
    def get_paginated_replies(self, params):
        try:
            from django.core.paginator import Paginator
            from django.db.models import Q
            from .serializers import ReplySerializer

            message_id = params.get('message_id')
            if not message_id:
                return {'error': 'message_id is required'}

            # Build queryset
            queryset = Reply.objects.filter(message_id=message_id)

            # Apply filters
            if params.get('search'):
                search = params['search']
                queryset = queryset.filter(content__icontains=search)

            # Order by timestamp (oldest first for chat-like behavior)
            queryset = queryset.order_by('timestamp')

            # Pagination
            page = params.get('page', 1)
            page_size = params.get('page_size', 50)
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page)

            # Serialize data
            serializer = ReplySerializer(page_obj.object_list, many=True)

            return {
                'results': serializer.data,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': page_obj.number,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None
            }
        except Exception as e:
            logger.error(f"Error getting paginated replies: {e}")
            return {'error': str(e)}


class NotificationConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time notifications"""
    
    async def connect(self):
        try:
            # Get user from scope and URL parameters
            self.user = self.scope.get('user')
            self.user_id = self.scope['url_route']['kwargs']['user_id']
            self.room_group_name = f'notifications_{self.user_id}'

            # Join room group
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )

            await self.accept()

            # Log connection with user info
            user_info = f"user: {self.user.username}" if self.user and not self.user.is_anonymous else "anonymous"
            logger.info(f"Notification WebSocket connected for user {self.user_id} ({user_info})")

            # Send connection confirmation
            await self.send(text_data=json.dumps({
                'type': 'connection_established',
                'message': f'Connected to notifications WebSocket for user {self.user_id}',
                'timestamp': timezone.now().isoformat()
            }))

        except Exception as e:
            logger.error(f"Error connecting notification WebSocket: {e}")
            await self.close()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')
        
        if message_type == 'mark_read':
            notification_id = text_data_json.get('notification_id')
            await self.mark_notification_read(notification_id)
    
    async def notification_message(self, event):
        """Send notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': event['notification']
        }))
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        try:
            notification = Notification.objects.get(id=notification_id)
            notification.read = True
            notification.save()
            return notification
        except Notification.DoesNotExist:
            return None


class AssignmentConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time assignment updates"""
    
    async def connect(self):
        try:
            # Get user from scope
            self.user = self.scope.get('user')
            self.room_group_name = 'assignments'

            # Join room group
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )

            await self.accept()

            # Log connection with user info
            user_info = f"user: {self.user.username}" if self.user and not self.user.is_anonymous else "anonymous"
            logger.info(f"Assignment WebSocket connected ({user_info})")

        except Exception as e:
            logger.error(f"Error connecting assignment WebSocket: {e}")
            await self.close()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def assignment_update(self, event):
        """Send assignment update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'assignment_update',
            'assignment': event['assignment']
        }))
    
    async def new_assignment(self, event):
        """Send new assignment to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'new_assignment',
            'assignment': event['assignment']
        }))
