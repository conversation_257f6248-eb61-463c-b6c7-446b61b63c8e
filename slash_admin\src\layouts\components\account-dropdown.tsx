import { Divider, type MenuProps } from "antd";
import Dropdown, { type DropdownProps } from "antd/es/dropdown/dropdown";
import React from "react";
import { useTranslation } from "react-i18next";
import { NavLink } from "react-router";
import { useMutation } from "@tanstack/react-query";
import userService from "@/api/services/userService";
import { toast } from "sonner";

import { IconButton } from "@/components/icon";
import { useLoginStateContext } from "@/pages/sys/login/providers/LoginStateProvider";
import { useRouter } from "@/router/hooks";
import { useUserActions, useUserInfo } from "@/store/userStore";
import { useTheme } from "@/theme/hooks";
import { getFullAvatarUrl } from "@/utils/url";

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

/**
 * Account Dropdown
 */
export default function AccountDropdown() {
	const { replace } = useRouter();
	const { username, email, avatar } = useUserInfo();
	const { clearUserInfoAndToken } = useUserActions();
	const { backToLogin } = useLoginStateContext();
	const { t } = useTranslation();

	const logoutMutation = useMutation({
		mutationFn: userService.logout,
		onSuccess: () => {
			clearUserInfoAndToken();
			backToLogin();
			replace("/login");
		},
		onError: (error) => {
			console.error('Logout error:', error);
			toast.error(t("sys.login.logout.error"));
		}
	});

	const logout = async () => {
		try {
			await logoutMutation.mutateAsync();
		} catch (error) {
			console.error('Logout error:', error);
		}
	};

	const {
		themeVars: { colors, borderRadius, shadows },
	} = useTheme();

	const contentStyle: React.CSSProperties = {
		backgroundColor: colors.background.default,
		borderRadius: borderRadius.lg,
		boxShadow: shadows.dropdown,
	};

	const menuStyle: React.CSSProperties = {
		boxShadow: "none",
	};

	const dropdownRender: DropdownProps["dropdownRender"] = (menu) => (
		<div style={contentStyle}>
			<div className="flex flex-col items-start p-4">
				<div>{username}</div>
				<div className="text-gray">{email}</div>
			</div>
			<Divider style={{ margin: 0 }} />
			{React.cloneElement(menu as React.ReactElement, { style: menuStyle })}
		</div>
	);

	const items: MenuProps["items"] = [
		// {
		// 	label: (
		// 		<NavLink to="https://docs-admin.slashspaces.com/" target="_blank">
		// 			{t("sys.docs")}
		// 		</NavLink>
		// 	),
		// 	key: "0",
		// },
		{
			label: <NavLink to={HOMEPAGE}>{t("sys.menu.dashboard")}</NavLink>,
			key: "1",
		},
		// {
		// 	label: <NavLink to="/management/user/profile">{t("sys.menu.user.profile")}</NavLink>,
		// 	key: "2",
		// },
		{
			// label: <NavLink to="/management/user/account">{t("sys.menu.user.account")}</NavLink>,
			label: <NavLink to="/account">{t("sys.menu.user.account")}</NavLink>,
			key: "3",
		},
		{ type: "divider" },
		{
			label: (
				<button 
					className="font-bold text-warning" 
					type="button"
					onClick={logout}
				>
					{t("sys.login.logout")}
				</button>
			),
			key: "4",
		},
	];

	return (
		<Dropdown menu={{ items }} trigger={["click"]} dropdownRender={dropdownRender}>
			<IconButton className="h-10 w-10 transform-none px-0 hover:scale-105">
				<img className="h-8 w-8 rounded-full" src={getFullAvatarUrl(avatar)} alt="" />
			</IconButton>
		</Dropdown>
	);
}

