import React, { useEffect, useState } from 'react';
import { Tabs, Typography } from 'antd';
import InspectionStatus from './InspectionStatus';
import GeneralStatus from './GeneralStatus';
import { Button} from "antd";
import { useUserInfo } from '@/store/userStore';
import axios from 'axios';
import InspectionDashboard from './inspectionDashbord';
import transformerService from '@/api/services/transformerService';

const { TabPane } = Tabs;

const EXPORT_WAIT_MS = 3 * 60 * 60 * 1000; 

const Stats: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const { role } = useUserInfo();
  const [exporting, setExporting] = useState(false); 
  const [lastExport, setLastExport] = useState<number | null>(null);
  const [waitText, setWaitText] = useState<string>("");

  // Load last export time from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem("lastExportTime");
    if (stored) setLastExport(Number(stored));
  }, []);

  // Timer to update wait text if needed
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (lastExport) {
      const update = () => {
        const now = Date.now();
        const diff = lastExport + EXPORT_WAIT_MS - now;
        if (diff > 0) {
          const hours = Math.floor(diff / (60 * 60 * 1000));
          const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));
          const seconds = Math.floor((diff % (60 * 1000)) / 1000);
          setWaitText(
            `You can export again in ${hours}h ${minutes}m ${seconds}s.`
          );
        } else {
          setWaitText("");
        }
      };
      update();
      timer = setInterval(update, 1000);
    }
    return () => clearInterval(timer);
  }, [lastExport]);

  const handleExportExcel = async () => {
    if (lastExport && Date.now() - lastExport < EXPORT_WAIT_MS) return;
    setExporting(true);
    try {
      const response = await transformerService.exportExcel()
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "transformers.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      const now = Date.now();
      setLastExport(now);
      localStorage.setItem("lastExportTime", String(now));
    } catch (error) {
      console.error("Export failed", error);
    } finally {
      setExporting(false);
    }
  };


 const handleExportBackUp = async () => {
  setExporting(true);
  try {
    const response = await transformerService.exportBackup();

    const blob = new Blob([response], { type: "application/octet-stream" });
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url; // Optional: you can extract filename from headers
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-'); // e.g., 2025-07-31T14-55-32-123Z
    const filename = `thms_${timestamp}.backup`;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();

    // Clean up
    link.remove();
    window.URL.revokeObjectURL(url);

  } catch (error) {
    console.error("Export failed", error);
  } finally {
    setExporting(false);
  }
};


  const canExport = !lastExport || (Date.now() - lastExport >= EXPORT_WAIT_MS);

  return (
    <div className="w-full p-4">
      <Typography.Title level={2}>Hi, Welcome back 👋</Typography.Title>
       {/* {role?.name === "SuperAdmin" && <Button onClick={handleExportExcel}>
        Export to Excel
      </Button>} */}
      <div className="flex justify-end gap-5">
      {/* {role?.name === "SuperAdmin" && (
        <div>
          <Button
            onClick={handleExportExcel}
            disabled={exporting || !canExport}
          >
            Export to Excel
          </Button>
          {exporting && (
            <div style={{ marginTop: 8, color: "red" }}>Wait...</div>
          )}
          {!canExport && !exporting && (
            <div style={{ marginTop: 8, color: "orange" }}>{waitText}</div>
          )}
        </div>
      )} */}

      {role?.name === "SuperAdmin" && (
        <div>
          <Button
            onClick={handleExportBackUp}
            disabled={exporting || !canExport}
          >
            Export backup
          </Button>
          {exporting && (
            <div style={{ marginTop: 8, color: "red" }}>Wait...</div>
          )}
          {!canExport && !exporting && (
            <div style={{ marginTop: 8, color: "orange" }}>{waitText}</div>
          )}
        </div>
      )}
      </div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="General" key="general">
          <GeneralStatus />
        </TabPane>
        <TabPane tab="Inspection Dashboard" key="innspection_dashboard">
          <InspectionDashboard/>
        </TabPane>
        <TabPane tab="Inspection Status" key="inspection">
          <InspectionStatus />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Stats;
