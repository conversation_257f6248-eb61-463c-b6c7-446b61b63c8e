import React from 'react';
import { Pie } from '@ant-design/plots';

interface LvFeederPieChartProps {
  tabKey: string;
  data: any[];
}

const LvFeederPieChart: React.FC<LvFeederPieChartProps> = ({ tabKey, data }) => {
  // Transform data based on tabKey
  const getPieData = () => {
    // Implement data transformation logic based on tabKey
    // Return data in format required by Pie chart
  };

  const config = {
    data: getPieData(),
    // Configure other Pie chart properties
  };

  return <Pie {...config} />;
};

export default LvFeederPieChart;