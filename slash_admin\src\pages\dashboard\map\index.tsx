import React, { useMemo } from "react";
import FiltersBar from "./FiltersBar";
import FiltersFull from "./FiltersFull";
import MyMap from "./Map";
import { useDashboardStore } from "@/store/dashboardStore";
import { useFilteredStore } from "@/store/filteredStore";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function cleanParams(params: Record<string, any>): Record<string, any> {
  return Object.fromEntries(
    Object.entries(params).filter(
      ([_, value]) =>
        value !== undefined &&
        value !== "any" &&
        value !== "" &&
        (Array.isArray(value) ? value.some((v) => v !== null) : value !== null)
    )
  );
}

// Constants
const CONTAINER_STYLE = {
  height: `calc(100vh - 52px)`,
};

const SearchPage: React.FC = () => {
  const { isFiltersFullOpen, viewMode } = useDashboardStore();
  const { missingBasestations, missingGPSStations } = useFilteredStore();

  // Memoize the sidebar class to prevent unnecessary recalculations
  const sidebarClassName = useMemo(() => {
    const baseClass = "h-full overflow-auto transition-all duration-300 ease-in-out";
    return isFiltersFullOpen
      ? `${baseClass} w-3/12 opacity-100 visible`
      : `${baseClass} w-0 opacity-0 invisible`;
  }, [isFiltersFullOpen]);

  // Memoize the filter messages to prevent unnecessary re-renders
  const filterMessages = useMemo(() => {
    const messages = [];

    if (missingBasestations.length > 0) {
      messages.push(
        <div key="missing-basestation">
          Filtered out {missingBasestations.length} entries without basestation
        </div>
      );
    }

    if (missingGPSStations.length > 0) {
      messages.push(
        <div key="missing-gps">
          Filtered out {missingGPSStations.length} entries without GPS
        </div>
      );
    }

    return messages.length > 0 ? <div>{messages}</div> : null;
  }, [missingBasestations.length, missingGPSStations.length]);

  return (
    <div
      className="w-full mx-auto px-5 flex flex-col"
      style={CONTAINER_STYLE}
    >
      <FiltersBar />

      <div className="flex flex-1 overflow-hidden gap-3 mb-5">
        {viewMode !== "grid" && (
          <div className={sidebarClassName}>
            <FiltersFull />
          </div>
        )}

        <div className="flex-1 flex justify-center h-full">
          <MyMap />
        </div>
      </div>

      {filterMessages}
    </div>
  );
};

export default React.memo(SearchPage);
