import React, { useState, useEffect } from 'react';
import { Form, Row, Col, Select, Button, Input, DatePicker } from 'antd';
import type { Moment } from 'moment';
import orgService from "@/api/services/orgService";
import moment from 'moment';

interface FilterFormProps {
  filters: Record<string, any>;
  onFilterChange: (values: Record<string, any>) => void;
  onReset: () => void;
  onApply: () => void;
}

const { RangePicker } = DatePicker;

// Define the choice constants
const DISTRIBUTION_BOX_CHOICES = [
  { value: 'Box 1', label: 'Box 1' },
  { value: 'Box 2', label: 'Box 2' },
  { value: 'Box 3', label: 'Box 3' },
  { value: 'Box 4', label: 'Box 4' },
  { value: 'Box 5', label: 'Box 5' },
];

const TRANSFORMER_LOAD_CHOICES = [
  { value: 'below20', label: 'Below 20%' },
  { value: 'load20_50', label: '20% - 50%' },
  { value: 'load50_80', label: '50% - 80%' },
  { value: 'load80_100', label: '80% - 100%' },
  { value: 'above100', label: 'Above 100%' },
];

const CURRENT_PHASE_UNBALANCE_CHOICES = [
  { value: 'balanced', label: 'Balanced (≤ 10%)' },
  { value: 'unbalanced', label: 'Unbalanced (> 10%)' },
];

const PERCENTAGE_OF_NEUTRAL_CHOICES = [
  { value: 'normal', label: 'Normal (≤ 20%)' },
  { value: 'high', label: 'High (> 20%)' },
];



const FilterForm: React.FC<FilterFormProps> = ({
  filters,
  onFilterChange,
  onReset,
  onApply,
}) => {
  const [form] = Form.useForm();
  // Add state for regions and CSCs
  const [regions, setRegions] = useState<any[]>([]);
  const [selectedCSCs, setSelectedCSCs] = useState<any[]>([]);

  // Load regions when component mounts
  useEffect(() => {
    const loadRegions = async () => {
      try {
        const data = await orgService.getOrgList();
        setRegions(data);
      } catch (error) {
        console.error("Error fetching regions:", error);
      }
    };
    loadRegions();
  }, []);

  const handleValuesChange = (_: any, allValues: any) => {
    onFilterChange(allValues);
  };

  const handleReset = () => {
    // Reset all form fields to undefined
    const emptyValues = Object.keys(form.getFieldsValue()).reduce((acc, key) => {
      acc[key] = undefined;
      return acc;
    }, {} as Record<string, undefined>);
    
    // Set all form values to undefined
    form.setFieldsValue(emptyValues);
    
    // Clear local states
    setSelectedCSCs([]);
    
    // Update parent component
    onFilterChange({});
    onReset();
  };

  // Sync form with external filters
  useEffect(() => {
    if (!filters || Object.keys(filters).length === 0) {
      form.resetFields();
    } else {
      form.setFieldsValue(filters);
    }
  }, [filters, form]);

  return (
    <Form
      form={form}
      onValuesChange={handleValuesChange}
      layout="vertical"
    >
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Form.Item name="station_code" label="Station Code">
            <Input placeholder="Station Code" />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Form.Item name="TransformerID" label="Transformer ID">
            <Input placeholder="Transformer ID" />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Form.Item name="region" label="Region">
            <Select
              placeholder="Select Region"
              allowClear
              showSearch
              optionFilterProp="children"
              onChange={(value) => {
                const selectedRegion = regions.find(region => region.csc_code === value);
                if (selectedRegion) {
                  setSelectedCSCs(selectedRegion.csc_centers);
                  form.setFieldsValue({ csc: undefined }); // Clear CSC when region changes
                }
              }}
            >
              {regions.map(region => (
                <Select.Option key={region.csc_code} value={region.csc_code}>
                  {region.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Form.Item name="csc" label="CSC">
            <Select
              placeholder="Select CSC"
              allowClear
              showSearch
              optionFilterProp="children"
              disabled={!form.getFieldValue('region')}
            >
              {selectedCSCs.map(csc => (
                <Select.Option key={csc.csc_code} value={csc.csc_code}>
                  {csc.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>


        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="distribution_box_name" label="Distribution Box Name">
            <Select allowClear placeholder="Select Distribution Box Name" options={DISTRIBUTION_BOX_CHOICES} />
          </Form.Item>
        </Col>

        {/* <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="transformer_load" label="Transformer Load">
            <Select 
              allowClear 
              placeholder="Select transformer load status" 
              options={TRANSFORMER_LOAD_CHOICES}
            />
          </Form.Item>
        </Col> */}

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="current_phase_unbalance" label="Current Phase Unbalance">
            <Select 
              allowClear 
              placeholder="Select current phase unbalance status" 
              options={CURRENT_PHASE_UNBALANCE_CHOICES}
            />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Form.Item name="percentage_of_neutral" label="Percentage of Neutral">
            <Select 
              allowClear 
              placeholder="Select percentage of neutral status" 
              options={PERCENTAGE_OF_NEUTRAL_CHOICES}
            />
          </Form.Item>
        </Col>

        <Col xs={24} sm={12} md={8}>
          <Form.Item label="Created Date Range">
            <DatePicker.RangePicker
              value={
                filters.created_date_range && filters.created_date_range[0] && filters.created_date_range[1]
                  ? [moment(filters.created_date_range[0]), moment(filters.created_date_range[1])]
                  : undefined
              }
              onChange={(dates) => {
                let formattedDates = undefined;
                if (dates && dates[0] && dates[1]) {
                  formattedDates = [
                    dates[0].format("YYYY-MM-DD"),
                    dates[1].format("YYYY-MM-DD")
                  ];
                }
                onFilterChange({
                  ...filters,
                  created_date_range: formattedDates
                });
              }}
              style={{ width: "100%" }}
              allowClear
            />
          </Form.Item>
        </Col>
      </Row>

      <Row justify="end" gutter={[16, 16]} style={{ marginTop: '20px' }}>
        <Col>
          <Button type="default" onClick={handleReset}>Reset</Button>
        </Col>
        <Col>
          <Button type="primary" onClick={onApply}>
            Apply Filters
          </Button>
        </Col>
      </Row>
    </Form>
  );
};

export default FilterForm;










