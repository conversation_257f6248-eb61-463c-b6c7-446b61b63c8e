import React from 'react';
import { Table, Tag, Typography } from 'antd';
import dayjs from 'dayjs';

interface ActivityLog {
    id: number;
    user: string;
    action: 'CREATE' | 'UPDATE' | 'DELETE';
    model_name: string;
    record_id: string;
    changes: string;
    timestamp: string;
    ip_address: string;
}

interface ActivityLogsProps {
    loading: boolean;
    logs: ActivityLog[];
}

const ActivityLogs: React.FC<ActivityLogsProps> = ({ loading, logs }) => {
    const columns = [
        {
            title: 'Timestamp',
            dataIndex: 'timestamp',
            render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: 'User',
            dataIndex: 'user',
        },
        {
            title: 'Action',
            dataIndex: 'action',
            render: (action: string) => (
                <Tag color={
                    action === 'CREATE' ? 'green' :
                    action === 'UPDATE' ? 'blue' :
                    'red'
                }>
                    {action}
                </Tag>
            ),
        },
        {
            title: 'Model',
            dataIndex: 'model_name',
        },
        {
            title: 'Record ID',
            dataIndex: 'record_id',
        },
        {
            title: 'Changes',
            dataIndex: 'changes',
            render: (changes: string) => (
                <Typography.Text ellipsis={{ tooltip: changes }}>
                    {changes}
                </Typography.Text>
            ),
        },
        {
            title: 'IP Address',
            dataIndex: 'ip_address',
        },
    ];

    return (
        <Table
            columns={columns}
            dataSource={logs}
            loading={loading}
            rowKey="id"
            pagination={{ pageSize: 10 }}
        />
    );
};

export default ActivityLogs;
