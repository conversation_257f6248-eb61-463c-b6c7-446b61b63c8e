import React, { useEffect, useState } from 'react';
import MessageList from './components/Messages/MessageList';
import MessageDetail from './components/Messages/MessageDetail';
import { SMSMessage } from '../../types';
import { useMessages } from '../../hooks/useMessages';
import { ChatBubbleLeftRightIcon, InboxIcon } from '@heroicons/react/24/outline';

export default function Messages() {
  const { messages, getReplies } = useMessages();
  const [selectedMessage, setSelectedMessage] = useState<SMSMessage | null>(
    messages.length > 0 ? messages[0] : null
  );

  console.log("selectedMessage______________________________", selectedMessage);


  // useEffect(() => {
  //   if (selectedMessage) {
  //     const replies = getReplies(selectedMessage.id);
  //     messages.map((message) => {
  //       if (message.id === selectedMessage.id) {
  //         message?.replies = replies;
  //       }
  //       return message;
  //     });
  //     selectedMessage.replies = replies;
  //   }
  // }, [selectedMessage?.id]);

  useEffect(() => {
    const fetchReplies = async () => {
      if (selectedMessage) {
        try {
          const replies = await getReplies(selectedMessage.id);

          // ✅ Update selectedMessage with new replies (without mutating state directly)
          setSelectedMessage(prev =>
            prev ? { ...prev, replies } : null
          );
        } catch (err) {
          console.error("Failed to fetch replies:", err);
        }
      }
    };

    fetchReplies();
  }, [selectedMessage?.id]);



  return (
    <div className="h-full flex bg-red-900 rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Messages List Sidebar */}
      <div className="w-1/3 border-r border-gray-200 bg-gray-50">
        <MessageList
          onSelectMessage={setSelectedMessage}
          selectedMessageId={selectedMessage?.id}
        />
      </div>

      {/* Message Detail View */}
      <div className="flex-1 flex flex-col bg-yellow-200">
        {selectedMessage ? (
          <MessageDetail message={selectedMessage} />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md">
              <div className="mx-auto mb-6 p-4 bg-gray-100 rounded-full w-fit">
                <InboxIcon className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                Select a message to view
              </h3>
              <p className="text-gray-500">
                Choose a message from the list to see details, conversation history, and response options.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
