// import { Form, Input, Modal } from "antd";
// import { useEffect, useState } from "react";
// import type { Basestation } from "#/entity"; // Import the Basestation type
// import { useMutation } from "@tanstack/react-query";
// import transformerService from "@/api/services/transformerService";
// import { toast } from "sonner";
// import { TreeSelect } from 'antd';
// import type { TreeSelectProps } from 'antd';

// // Define the props for the BasestationModal
// export type BasestationModalProps = {
//     formValue: Basestation;
//     title: string;
//     show: boolean;
//     onOk: VoidFunction;
//     onCancel: VoidFunction;
// };

// const treeData = [

//     {
//       value: 'parent 1',
//       title: 'parent 1',
//       children: [
//         {
//           value: 'parent 1-0',
//           title: 'parent 1-0',
//           children: [
//             {
//               value: 'leaf1',
//               title: 'leaf1',
//             },
//             {
//               value: 'leaf2',
//               title: 'leaf2',
//             },
//             {
//               value: 'leaf3',
//               title: 'leaf3',
//             },
//             {
//               value: 'leaf4',
//               title: 'leaf4',
//             },
//             {
//               value: 'leaf5',
//               title: 'leaf5',
//             },
//             {
//               value: 'leaf6',
//               title: 'leaf6',
//             },
//           ],
//         },
//         {
//           value: 'parent 1-1',
//           title: 'parent 1-1',
//           children: [
//             {
//               value: 'leaf11',
//               title: <b style={{ color: '#08c' }}>leaf11</b>,
//             },
//           ],
//         },
//       ],
//     },
//   ];

// // BasestationModal component
// export function BasestationModal({ title, show, formValue, onOk, onCancel }: BasestationModalProps) {

//     const [form] = Form.useForm();
//     const [loading, setLoading] = useState(false);

//     // Update form values when formValue changes
//     useEffect(() => {
//         form.setFieldsValue({ ...formValue });
//     }, [formValue, form]);

//     // Update form values when formValue changes
//     useEffect(() => {
//         form.setFieldsValue({ ...formValue });
//     }, [formValue, form]);

//     const handleOk = () => {
//         form.validateFields() // Validate all fields
//             .then(async (values: any) => {
//                 console.log("values", values);
//                 setLoading(true);

//                 if (title === "Create New Basestation") {
//                     await basestationMutation.mutateAsync(values);
//                     toast.success("Basestation created successfully!");
//                 } else if (title === "Edit Basestation") {
//                     // Extract the updated form values
//                     const updatedValues = form.getFieldsValue(); // Get the latest form values

//                     // Call the updateBasestation function with the updated values
//                     await transformerService.updateBasestation(formValue.id, updatedValues as Partial<Basestation>);
//                     toast.success("Basestation updated successfully");
//                 }

//                 onOk(); // Call the parent's onOk handler
//                 setLoading(false); // Reset loading state
//             })
//             .catch((errorInfo) => {
//                 console.log("Validation failed:", errorInfo);
//                 // Handle validation errors if needed
//             });
//     };

//     const basestationMutation = useMutation({
//         mutationFn: transformerService.createBasestation,
//     });

//     const [value, setValue] = useState<string>();

//   const onChange = (newValue: string) => {
//     setValue(newValue);
//   };

//   const onPopupScroll: TreeSelectProps['onPopupScroll'] = (e) => {
//     console.log('onPopupScroll', e);
//   };

//     return (
//         <Modal title={title} open={show}  onOk={handleOk} onCancel={onCancel} confirmLoading={loading}>
//             <Form
//                 initialValues={formValue}
//                 form={form}
//                 labelCol={{ span: 6 }}
//                 wrapperCol={{ span: 18 }}
//                 layout="horizontal"
//             >
//                 {/* Station Code */}
//                 <Form.Item<Basestation> label="Station Code" name="station_code" required>
//                     <Input />
//                 </Form.Item>

//                 {/* Region */}
//                 <Form.Item<Basestation> label="Region" name="region" required>
//                     {/* <Input /> */}
//                     <TreeSelect
//                       showSearch
//                       style={{ width: '100%' }}
//                       value={value}
//                       dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
//                       placeholder="Please select"
//                       allowClear
//                       treeDefaultExpandAll
//                       onChange={onChange}
//                       treeData={treeData}
//                       onPopupScroll={onPopupScroll}
//                     />
//                 </Form.Item>

//                 {/* CSC */}
//                 <Form.Item<Basestation> label="CSC" name="csc" required>
//                     <Input />
//                 </Form.Item>

//                 {/* Substation */}
//                 <Form.Item<Basestation> label="Substation" name="substation" required>
//                     <Input />
//                 </Form.Item>

//                 {/* Feeder */}
//                 <Form.Item<Basestation> label="Feeder" name="feeder" required>
//                     <Input />
//                 </Form.Item>

//                 {/* Address */}
//                 <Form.Item<Basestation> label="Address" name="address" required>
//                     <Input.TextArea />
//                 </Form.Item>

//                 {/* GPS Location */}
//                 <Form.Item<Basestation> label="GPS Location" name="gps_location" required>
//                     <Input />
//                 </Form.Item>
//             </Form>
//         </Modal>
//     );
// }

import { Form, Input, Modal, Select } from "antd";
import { useEffect, useState } from "react";
import type { Organization } from "#/entity"; // Import the Basestation type
import { useMutation } from "@tanstack/react-query";
import transformerService from "@/api/services/transformerService";
import { toast } from "sonner";
import { TreeSelect } from "antd";
import type { TreeSelectProps } from "antd";
import orgService from "@/api/services/orgService";

export type OrganizationModalProps = {
	formValue: Organization;
	title: string;
	show: boolean;
	onOk: (title: string) => void;
	onCancel: () => void;
};

export function OrganizationModal({ title, show, formValue, onOk, onCancel }: OrganizationModalProps) {
	const [form] = Form.useForm();
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		form.setFieldsValue(formValue); // Initialize form with formValue
	}, [formValue, form]);

	const handleOk = () => {
		form
			.validateFields() // Validate all fields
			.then(async (values: any) => {
				console.log("values", values);
				setLoading(true);

				if (title === "Create Region") {
					console.log("Creating new region:", values);
					const data = {
						csc_code: values.name,
						name: values.name,
					};
					await orgService.createRegion(data);
				} else if (title === "Create CSC") {
					console.log("Creating new CSC:", values);
					await orgService.createCSC(values);
				} else if (title === "Edit region") {
					await orgService.updateRegion(formValue.csc_code, values);
				} else if (title === "Edit csc") {
					console.log("Editing CSC:", values);
					await orgService.updateCSC(formValue.csc_code, values);
				}

				onOk(title); // Call the parent's onOk handler
				setLoading(false); // Reset loading state
			})
			.catch((errorInfo) => {
				console.log("Validation failed:", errorInfo);
				// Handle validation errors if needed
			});
	};

	const [regions, setRegions] = useState<{ key: string; name: string }[]>([]);
	const [loading1, setLoading1] = useState(true);

	// Fetch regions when the component mounts
	useEffect(() => {
		const loadRegions = async () => {
			try {
				const data = await orgService.getRegionOnly();
				setRegions(data.map((region: any) => ({ key: region.csc_code, name: region.name })));
			} catch (error) {
				console.error("Error fetching regions:", error);
			} finally {
				setLoading1(false);
			}
		};
		loadRegions();
	}, []);

	return (
		<Modal title={title} open={show} onOk={handleOk} onCancel={onCancel} confirmLoading={loading}>
			<Form form={form} initialValues={formValue} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout="horizontal">
				{title === "Create CSC" || title === "CSC Edit" ? (
					<>
						{" "}
						<Form.Item
							name="region"
							label="Select Region"
							rules={[{ required: true, message: "Please select a region!" }]}
						>
							<Select
								showSearch
								placeholder="Select a region"
								optionFilterProp="children"
								loading={loading1}
								filterOption={(input, option) =>
									String(option?.children ?? "")
										.toLowerCase()
										.includes(input.toLowerCase())
								}
							>
								{regions.map((region) => (
									<Select.Option key={region.key} value={region.key}>
										{region.name}
									</Select.Option>
								))}
							</Select>
						</Form.Item>
						<Form.Item name="csc_code" label="CSC Code" rules={[{ required: true }]}>
							<Input />
						</Form.Item>
						<Form.Item name="name" label="CSC Name" rules={[{ required: true }]}>
							<Input />
						</Form.Item>
					</>
				) : (
					<Form.Item name="name" label="Region Name" rules={[{ required: true }]}>
						<Input />
					</Form.Item>
				)}
			</Form>
		</Modal>
	);
}
