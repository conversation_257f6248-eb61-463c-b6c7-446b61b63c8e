import React, { useRef, useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, MagnifyingGlassIcon, FunnelIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { SMSMessage } from '../../../../types';
import MessageCard from './MessageCard';
import AdvancedFilters from './AdvancedFilters';
import { useMessages } from '../../../../hooks/useMessages';

interface MessageListProps {
  onSelectMessage: (message: SMSMessage) => void;
  selectedMessageId?: string;
}

export default function MessageList({ onSelectMessage, selectedMessageId }: MessageListProps) {
  const { messages } = useMessages();
  const [currentPage, setCurrentPage] = useState(1);
  const [messagesPerPage] = useState(10);
  const [filters, setFilters] = useState({
    status: 'all',
    category: 'all',
    priority: 'all',
    assignee: 'all',
    tags: [] as string[],
    dateRange: { start: '', end: '' },
    search: '',
    archived: false
  });
  const [filtersExpanded, setFiltersExpanded] = useState(false);

  const paginationRef = useRef(null);

  const filteredMessages = messages.filter(message => {
    // Archive filter
    if (!filters.archived && message.isArchived) return false;
    if (filters.archived && !message.isArchived) return false;

    // Basic filters
    if (filters.status !== 'all' && message.status !== filters.status) return false;
    if (filters.category !== 'all' && message.category !== filters.category) return false;
    if (filters.priority !== 'all' && message.priority !== filters.priority) return false;
    
    // Assignee filter
    if (filters.assignee !== 'all') {
      if (filters.assignee === 'unassigned' && message.assigned_to) return false;
      if (filters.assignee !== 'unassigned' && message.assigned_to?.id !== filters.assignee) return false;
    }
    
    // Tags filter
    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag => message.tags.includes(tag));
      if (!hasMatchingTag) return false;
    }
    
    // Date range filter
    if (filters.dateRange.start) {
      const startDate = new Date(filters.dateRange.start);
      if (message.timestamp < startDate) return false;
    }
    if (filters.dateRange.end) {
      const endDate = new Date(filters.dateRange.end);
      endDate.setHours(23, 59, 59, 999);
      if (message.timestamp > endDate) return false;
    }
    
    // Search filter
    if (filters.search && !message.content.toLowerCase().includes(filters.search.toLowerCase()) && 
        !message.phone_number.includes(filters.search) &&
        !message.caseId.toLowerCase().includes(filters.search.toLowerCase())) return false;
        
    return true;
  });

  // Pagination calculations
  const totalMessages = filteredMessages.length;
  const totalPages = Math.ceil(totalMessages / messagesPerPage);
  const startIndex = (currentPage - 1) * messagesPerPage;
  const endIndex = startIndex + messagesPerPage;
  const currentMessages = filteredMessages.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePrevious = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages));
  };

  const hasActiveFilters = filters.status !== 'all' || filters.category !== 'all' || 
    filters.priority !== 'all' || filters.assignee !== 'all' || 
    filters.tags.length > 0 || filters.search || filters.archived;

  return (
    <div className="h-full flex flex-col">

      <div className="p-6 border-b border-gray-200 bg-white">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">Messages</h1>
              <p className="text-sm text-gray-500">{messages.length} total messages</p>
            </div>
          </div>
        </div>
      {/* Search Bar */}
      <div className=" bg-white">
      </div>

      {/* Advanced Filters */}
      <AdvancedFilters 
        filters={filters} 
        onFiltersChange={setFilters}
        isExpanded={filtersExpanded}
        totalMessages={totalMessages}
        onToggleExpanded={() => setFiltersExpanded(!filtersExpanded)}
      />

      {/* Messages List */}
      <div className="flex-1 overflow-y-auto">
        {totalMessages === 0 ? (
          <div className="p-8 text-center">
            <div className="mx-auto mb-4 p-3 bg-gray-100 rounded-full w-fit">
              <MagnifyingGlassIcon className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
            <p className="text-gray-500">Try adjusting your search terms or filters</p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {currentMessages.map((message) => (
              <MessageCard
                key={message.id}
                message={message}
                isSelected={selectedMessageId === message.id} // Adjust based on your selection logic
                onClick={() => onSelectMessage(message)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div ref={paginationRef} className="sticky bottom-0 border-t border-gray-200 px-4 py-3 bg-white z-10">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Showing {startIndex + 1}-{Math.min(endIndex, totalMessages)} of {totalMessages}
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className="p-2 rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronLeftIcon className="h-4 w-4" />
              </button>
              <div className="flex gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`min-w-[36px] h-9 px-3 rounded-lg text-sm font-medium transition-colors ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white shadow-sm'
                          : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className="p-2 rounded-lg border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
