#!/usr/bin/env python
"""
Comprehensive test for SMPP integration.
Tests all components without requiring actual SMPP connection.
"""

import os
import sys
import django

# Add the core directory to Python path
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.conf import settings
from sms_app.sms_handler import SMPPClient, get_smpp_status
from sms_app.models import SMSMessage
from account.models import User


def test_smpp_config():
    """Test SMPP configuration"""
    print("🧪 Testing SMPP Configuration")
    print("=" * 40)
    
    try:
        config = getattr(settings, 'SMPP_CONFIG', {})
        
        if not config:
            print("❌ SMPP_CONFIG not found in settings")
            return False
        
        required_keys = ['HOST', 'PORT', 'SYSTEM_ID', 'PASSWORD']
        for key in required_keys:
            if key not in config:
                print(f"❌ Missing required config key: {key}")
                return False
            else:
                value = config[key]
                if key == 'PASSWORD':
                    value = '*' * len(str(value))
                print(f"✅ {key}: {value}")
        
        print("✅ SMPP configuration is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error testing SMPP config: {e}")
        return False


def test_smpp_client_creation():
    """Test SMPP client creation"""
    print("\n🧪 Testing SMPP Client Creation")
    print("=" * 40)
    
    try:
        client = SMPPClient()
        
        print(f"✅ SMPP client created successfully")
        print(f"✅ Host: {client.config['HOST']}")
        print(f"✅ Port: {client.config['PORT']}")
        print(f"✅ System ID: {client.config['SYSTEM_ID']}")
        print(f"✅ Connected: {client.connected}")
        print(f"✅ Running: {client.running}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating SMPP client: {e}")
        return False


def test_smpp_imports():
    """Test SMPP library imports"""
    print("\n🧪 Testing SMPP Library Imports")
    print("=" * 40)
    
    try:
        import smpplib
        print("✅ smpplib imported successfully")
        
        import smpplib.client
        print("✅ smpplib.client imported successfully")
        
        import smpplib.consts
        print("✅ smpplib.consts imported successfully")
        
        import smpplib.gsm
        print("✅ smpplib.gsm imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error importing SMPP libraries: {e}")
        return False


def test_sms_handler_functions():
    """Test SMS handler functions"""
    print("\n🧪 Testing SMS Handler Functions")
    print("=" * 40)
    
    try:
        from sms_app.sms_handler import (
            start_smpp_client, stop_smpp_client, 
            get_smpp_status, send_sms_via_smpp
        )
        
        print("✅ start_smpp_client imported")
        print("✅ stop_smpp_client imported")
        print("✅ get_smpp_status imported")
        print("✅ send_sms_via_smpp imported")
        
        # Test status function
        status = get_smpp_status()
        print(f"✅ get_smpp_status() returned: {type(status)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing SMS handler functions: {e}")
        return False


def test_management_command():
    """Test management command"""
    print("\n🧪 Testing Management Command")
    print("=" * 40)
    
    try:
        from django.core.management import call_command
        from io import StringIO
        
        # Capture command output
        out = StringIO()
        call_command('start_smpp', '--status', stdout=out)
        output = out.getvalue()
        
        if 'SMPP Client Status' in output:
            print("✅ Management command executed successfully")
            print("✅ Status command working")
            return True
        else:
            print("❌ Management command output unexpected")
            return False
        
    except Exception as e:
        print(f"❌ Error testing management command: {e}")
        return False


def test_django_models():
    """Test Django models integration"""
    print("\n🧪 Testing Django Models Integration")
    print("=" * 40)
    
    try:
        # Test SMSMessage model
        message_count = SMSMessage.objects.count()
        print(f"✅ SMSMessage model accessible - Count: {message_count}")
        
        # Test User model
        user_count = User.objects.count()
        print(f"✅ User model accessible - Count: {user_count}")
        
        # Test model fields
        sms_fields = [field.name for field in SMSMessage._meta.fields]
        expected_fields = ['phone_number', 'content', 'status', 'category']
        
        for field in expected_fields:
            if field in sms_fields:
                print(f"✅ SMSMessage.{field} field exists")
            else:
                print(f"❌ SMSMessage.{field} field missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Django models: {e}")
        return False


def test_celery_tasks():
    """Test Celery tasks"""
    print("\n🧪 Testing Celery Tasks")
    print("=" * 40)
    
    try:
        from sms_app.tasks import (
            handle_incoming_sms_task, auto_categorize_message, 
            send_auto_reply, send_notification_task
        )
        
        print("✅ handle_incoming_sms_task imported")
        print("✅ auto_categorize_message imported")
        print("✅ send_auto_reply imported")
        print("✅ send_notification_task imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Celery tasks: {e}")
        return False


def test_websocket_integration():
    """Test WebSocket integration"""
    print("\n🧪 Testing WebSocket Integration")
    print("=" * 40)
    
    try:
        from channels.layers import get_channel_layer
        
        channel_layer = get_channel_layer()
        if channel_layer:
            print(f"✅ Channel layer available: {channel_layer.__class__.__name__}")
        else:
            print("❌ No channel layer configured")
            return False
        
        # Test WebSocket consumers
        from sms_app.consumers import MessageConsumer, NotificationConsumer
        print("✅ WebSocket consumers imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing WebSocket integration: {e}")
        return False


def main():
    """Run all SMPP integration tests"""
    print("🚀 SMPP Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_smpp_config,
        test_smpp_imports,
        test_smpp_client_creation,
        test_sms_handler_functions,
        test_management_command,
        test_django_models,
        test_celery_tasks,
        test_websocket_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All SMPP integration tests passed!")
        print()
        print("✅ SMPP Configuration: Valid")
        print("✅ SMPP Libraries: Imported")
        print("✅ SMPP Client: Created")
        print("✅ Handler Functions: Working")
        print("✅ Management Command: Working")
        print("✅ Django Models: Integrated")
        print("✅ Celery Tasks: Available")
        print("✅ WebSocket: Integrated")
        print()
        print("🚀 Ready to use SMPP functionality:")
        print("  📱 Send SMS: python test_sms_send.py")
        print("  📥 Receive SMS: python test_sms_receive.py")
        print("  🔧 Manage SMPP: python manage.py start_smpp")
    else:
        print(f"⚠️ {total - passed} test(s) failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
        sys.exit(1)
