import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Table, Tabs } from "antd";
import type { ColumnsType } from "antd/es/table";
import Scrollbar from "@/components/scrollbar";
import axios from "axios";
import { CircleLoading } from "@/components/loading";
import InspectionGraph from "./InspectionGraph";
import InspectionPieChart from "./InspectionPieChart";
import apiClient from "@/api/apiClient";

const { TabPane } = Tabs;

interface InspectionData {
  key: string;
  region: string;
  csc?: string;
  children?: InspectionData[];
  [key: string]: any;
}

interface RegionData extends InspectionData {
  children: InspectionData[];
}

// Define inspection tabs for dynamic rendering
const INSPECTION_TABS = {
  total_transformer_load: "Total Transformer Load",
  voltage_phase_unbalance: "Voltage Phase Unbalance",
  average_voltage: "Average Voltage",
  body_condition: "Body Condition",
  arrester: "Arrester",
  drop_out: "Drop Out",
  fuse_link: "Fuse Link",
  mv_bushing: "MV Bushing",
  mv_cable_lug: "MV Cable Lug",
  lv_bushing: "LV Bushing",
  lv_cable_lug: "LV Cable Lug",
  oil_level: "Oil Level",
  insulation_level: "Insulation Level",
  horn_gap: "Horn Gap",
  silica_gel: "Silica Gel",
  has_linkage: "Has Linkage",
  arrester_body_ground: "Arrester Body Ground",
  neutral_ground: "Neutral Ground",
  status_of_mounting: "Status of Mounting",
  mounting_condition: "Mounting Condition",
} as const;

// Helper function to calculate percentages
const calculatePercentages = (values: Record<string, number>) => {
  const total = Object.values(values).reduce((a, b) => a + b, 0);
  return Object.fromEntries(
    Object.entries(values).map(([key, value]) => [
      key,
      value,
      `${key}Percentage`,
      total ? Number(((value / total) * 100).toFixed(2)) : 0,
    ])
  );
};

// Helper function to get analysis type data
const getAnalysisTypeData = (analysisType: string, data: any, isCSC: boolean) => {
  const calculatePercentage = (value: number, total: number) => (total > 0 ? Math.round((value / total) * 100) : 0);

  switch (analysisType) {
    case "body_condition": {
      const total = (data.good || 0) + (data.fair || 0) + (data.poor || 0);
      return {
        good: data.good || 0,
        fair: data.fair || 0,
        poor: data.poor || 0,
        goodPercentage: isCSC ? data.goodPercentage : calculatePercentage(data.good || 0, total),
        fairPercentage: isCSC ? data.fairPercentage : calculatePercentage(data.fair || 0, total),
        poorPercentage: isCSC ? data.poorPercentage : calculatePercentage(data.poor || 0, total),
      };
    }
    case "arrester":
    case "drop_out":
    case "fuse_link":
    case "mv_bushing":
    case "mv_cable_lug":
    case "lv_bushing":
    case "lv_cable_lug": {
      const total = (data.ok || 0) + (data.oneMissed || 0) + (data.twoMissed || 0) + (data.allMissed || 0);
      return {
        ok: data.ok || 0,
        oneMissed: data.oneMissed || 0,
        twoMissed: data.twoMissed || 0,
        allMissed: data.allMissed || 0,
        okPercentage: isCSC ? data.okPercentage : calculatePercentage(data.ok || 0, total),
        oneMissedPercentage: isCSC ? data.oneMissedPercentage : calculatePercentage(data.oneMissed || 0, total),
        twoMissedPercentage: isCSC ? data.twoMissedPercentage : calculatePercentage(data.twoMissed || 0, total),
        allMissedPercentage: isCSC ? data.allMissedPercentage : calculatePercentage(data.allMissed || 0, total),
      };
    }
    case "oil_level": {
      const total = (data.full || 0) + (data.threeFourths || 0) + (data.half || 0) + (data.oneFourth || 0);
      return {
        full: data.full || 0,
        threeFourths: data.threeFourths || 0,
        half: data.half || 0,
        oneFourth: data.oneFourth || 0,
        fullPercentage: isCSC ? data.fullPercentage : calculatePercentage(data.full || 0, total),
        threeFourthsPercentage: isCSC ? data.threeFourthsPercentage : calculatePercentage(data.threeFourths || 0, total),
        halfPercentage: isCSC ? data.halfPercentage : calculatePercentage(data.half || 0, total),
        oneFourthPercentage: isCSC ? data.oneFourthPercentage : calculatePercentage(data.oneFourth || 0, total),
      };
    }
    case "insulation_level": {
      const total = (data.acceptable || 0) + (data.notAcceptable || 0);
      return {
        acceptable: data.acceptable || 0,
        notAcceptable: data.notAcceptable || 0,
        acceptablePercentage: isCSC ? data.acceptablePercentage : calculatePercentage(data.acceptable || 0, total),
        notAcceptablePercentage: isCSC ? data.notAcceptablePercentage : calculatePercentage(data.notAcceptable || 0, total),
      };
    }
    case "horn_gap":
    case "silica_gel":
    case "status_of_mounting":
    case "mounting_condition": {
      const total = (data.good || 0) + (data.fair || 0) + (data.poor || 0);
      return {
        good: data.good || 0,
        fair: data.fair || 0,
        poor: data.poor || 0,
        goodPercentage: isCSC ? data.goodPercentage : calculatePercentage(data.good || 0, total),
        fairPercentage: isCSC ? data.fairPercentage : calculatePercentage(data.fair || 0, total),
        poorPercentage: isCSC ? data.poorPercentage : calculatePercentage(data.poor || 0, total),
      };
    }
    case "has_linkage": {
      const total = (data.yes || 0) + (data.no || 0);
      return {
        yes: data.yes || 0,
        no: data.no || 0,
        yesPercentage: isCSC ? data.yesPercentage : calculatePercentage(data.yes || 0, total),
        noPercentage: isCSC ? data.noPercentage : calculatePercentage(data.no || 0, total),
      };
    }
    case "arrester_body_ground":
    case "neutral_ground": {
      const total = (data.available || 0) + (data.notAvailable || 0);
      return {
        available: data.available || 0,
        notAvailable: data.notAvailable || 0,
        availablePercentage: isCSC ? data.availablePercentage : calculatePercentage(data.available || 0, total),
        notAvailablePercentage: isCSC ? data.notAvailablePercentage : calculatePercentage(data.notAvailable || 0, total),
      };
    }
    case "voltage_phase_unbalance": {
      const total = (data.balanced || 0) + (data.unbalanced || 0);
      return {
        balanced: data.balanced || 0,
        unbalanced: data.unbalanced || 0,
        balancedPercentage: isCSC ? data.balancedPercentage : calculatePercentage(data.balanced || 0, total),
        unbalancedPercentage: isCSC ? data.unbalancedPercentage : calculatePercentage(data.unbalanced || 0, total),
      };
    }
    case "average_voltage": {
      const total = (data.underVoltage || 0) + (data.normal || 0) + (data.overVoltage || 0);
      return {
        underVoltage: data.underVoltage || 0,
        normal: data.normal || 0,
        overVoltage: data.overVoltage || 0,
        underVoltagePercentage: isCSC ? data.underVoltagePercentage : calculatePercentage(data.underVoltage || 0, total),
        normalPercentage: isCSC ? data.normalPercentage : calculatePercentage(data.normal || 0, total),
        overVoltagePercentage: isCSC ? data.overVoltagePercentage : calculatePercentage(data.overVoltage || 0, total),
      };
    }
    case "total_transformer_load": {
      const total = (data.below20 || 0) + (data.load20_50 || 0) + (data.load50_80 || 0) + 
                   (data.load80_100 || 0) + (data.above100 || 0);
      return {
        below20: data.below20 || 0,
        load20_50: data.load20_50 || 0,
        load50_80: data.load50_80 || 0,
        load80_100: data.load80_100 || 0,
        above100: data.above100 || 0,
        below20Percentage: isCSC ? data.below20Percentage : calculatePercentage(data.below20 || 0, total),
        load20_50Percentage: isCSC ? data.load20_50Percentage : calculatePercentage(data.load20_50 || 0, total),
        load50_80Percentage: isCSC ? data.load50_80Percentage : calculatePercentage(data.load50_80 || 0, total),
        load80_100Percentage: isCSC ? data.load80_100Percentage : calculatePercentage(data.load80_100 || 0, total),
        above100Percentage: isCSC ? data.above100Percentage : calculatePercentage(data.above100 || 0, total),
      };
    }
    default:
      return {};
  }
};

// Main Component
const InspectionAnalysis: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("voltage_phase_unbalance");
  const [data, setData] = useState<InspectionData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // Define column specifications for each tab
  const specificColumns: Record<string, string[]> = {
    body_condition: ["Good", "Fair", "Poor"],
    arrester: ["Ok", "One Missed", "Two Missed", "All Missed"],
    drop_out: ["Ok", "One Missed", "Two Missed", "All Missed"],
    fuse_link: ["Ok", "One Missed", "Two Missed", "All Missed"],
    mv_bushing: ["Ok", "One Missed", "Two Missed", "All Missed"],
    mv_cable_lug: ["Ok", "One Missed", "Two Missed", "All Missed"],
    lv_bushing: ["Ok", "One Missed", "Two Missed", "All Missed"],
    lv_cable_lug: ["Ok", "One Missed", "Two Missed", "All Missed"],
    oil_level: ["Full", "0.75", "0.5", "0.25"],
    insulation_level: ["Acceptable", "Not Acceptable"],
    horn_gap: ["Good", "Poor"],
    silica_gel: ["Good", "Fair", "Poor"],
    has_linkage: ["Yes", "No"],
    arrester_body_ground: ["Available", "Not Available"],
    neutral_ground: ["Available", "Not Available"],
    status_of_mounting: ["Good", "Fair", "Poor"],
    mounting_condition: ["Good", "Fair", "Poor"],
    voltage_phase_unbalance: ["Balanced", "Unbalanced"],
    average_voltage: ["Under Voltage", "Normal", "Over Voltage"],
    total_transformer_load: ["Below 20%", "20-50%", "50-80%", "80-100%", "Above 100%"],
  };

  // Fetch data dynamically based on active tab
  const fetchInspectionData = useCallback(async (analysisType: string) => {
    setLoading(true);
    try {
      // const response = await axios.get(`${import.meta.env.VITE_APP_BASE_API}/api/transformer/inspection-analysis/${analysisType}/`);
      const response = await apiClient.get<any>({ url: `/api/transformer/inspection-analysis/${analysisType}`});

      const transformedData = response.map((region: any) => ({
        key: region.key,
        region: region.region,
        csc: "",
        children: region.children?.map((csc: any) => ({
          key: csc.key,
          region: csc.region,
          csc: csc.csc,
          ...getAnalysisTypeData(analysisType, csc, true),
        })),
        ...getAnalysisTypeData(analysisType, region, false),
      }));
      setData(transformedData);
    } catch (error) {
      console.error("Error fetching inspection data:", error);
    } finally {
      setLoading(false);
    }
  }, []);


  useEffect(() => {
    fetchInspectionData(activeTab);
  }, [activeTab, fetchInspectionData]);

  // Dynamically generate columns based on tabKey
  const getColumns = useCallback((tabKey: string): ColumnsType<InspectionData> => {
    const baseColumns: ColumnsType<InspectionData> = [
      { title: "Region", dataIndex: "region", key: "region", fixed: "left" as const },
      { title: "CSC", dataIndex: "csc", key: "csc", fixed: "left" as const },
    ];

    const columns = specificColumns[tabKey]?.map((title: string) => {
      let dataIndex = title.toLowerCase();
      let columnTitle = title;

      // Add ranges to specific column titles
      if (tabKey === 'average_voltage') {
        if (title === "Under Voltage") columnTitle = "Under Voltage\n(< 360V)";
        if (title === "Normal") columnTitle = "Normal\n(360V - 440V)";
        if (title === "Over Voltage") columnTitle = "Over Voltage\n(> 440V)";
      } else if (tabKey === 'voltage_phase_unbalance') {
        if (title === "Balanced") columnTitle = "Balanced\n(≤ 3%)";
        if (title === "Unbalanced") columnTitle = "Unbalanced\n(> 3%)";
      }

      // Special case mappings
      if (title === "One Missed") dataIndex = "oneMissed";
      if (title === "Two Missed") dataIndex = "twoMissed";
      if (title === "All Missed") dataIndex = "allMissed";
      if (title === "Not Acceptable") dataIndex = "notAcceptable";
      if (title === "Not Available") dataIndex = "notAvailable";
      if (title === "0.75") dataIndex = "threeFourths";
      if (title === "0.5") dataIndex = "half";
      if (title === "0.25") dataIndex = "oneFourth";
      if (title === "Under Voltage") dataIndex = "underVoltage";
      if (title === "Over Voltage") dataIndex = "overVoltage";
      
      // Total transformer load mappings
      if (title === "Below 20%") dataIndex = "below20";
      if (title === "20-50%") dataIndex = "load20_50";
      if (title === "50-80%") dataIndex = "load50_80";
      if (title === "80-100%") dataIndex = "load80_100";
      if (title === "Above 100%") dataIndex = "above100";

      return {
        title: columnTitle,
        children: [
          { 
            title: "Count", 
            dataIndex: dataIndex, 
            key: dataIndex 
          },
          {
            title: "%",
            dataIndex: `${dataIndex}Percentage`,
            key: `${dataIndex}Percentage`,
            render: (value: number) => `${value}%`,
          },
        ],
      };
    }) || [];

    // Keep existing total column logic
    columns.push({
      title: "Total",
      children: [
        {
          title: "Count",
          dataIndex: "total",
          key: "total",
          render: function(value: number, record: Record<string, any>) {
            const total = Object.keys(record)
              .filter((k) => !k.includes("Percentage") && !["key", "region", "csc", "children"].includes(k))
              .reduce((sum, k) => sum + (record[k] || 0), 0);
            return total.toString();
          }
        },
        {
          title: "%",
          dataIndex: "totalPercentage",
          key: "totalPercentage",
          render: function(value: number) { return "100%"; }
        },
      ],
    });

    return [...baseColumns, ...columns];
  }, [specificColumns]);

  // Render grand total row
  const renderGrandTotalRow = useCallback((tabKey: string, data: InspectionData[]) => {
    // Get the properties to calculate based on the tab type
    const propertyMap: Record<string, string[]> = {
      body_condition: ['good', 'fair', 'poor'],
      arrester: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      drop_out: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      fuse_link: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      mv_bushing: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      mv_cable_lug: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      lv_bushing: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      lv_cable_lug: ['ok', 'oneMissed', 'twoMissed', 'allMissed'],
      oil_level: ['full', 'threeFourths', 'half', 'oneFourth'],
      insulation_level: ['acceptable', 'notAcceptable'],
      horn_gap: ['good', 'poor'],
      silica_gel: ['good', 'fair', 'poor'],
      has_linkage: ['yes', 'no'],
      arrester_body_ground: ['available', 'notAvailable'],
      neutral_ground: ['available', 'notAvailable'],
      status_of_mounting: ['good', 'fair', 'poor'],
      mounting_condition: ['good', 'fair', 'poor'],
      voltage_phase_unbalance: ['balanced', 'unbalanced'],
      average_voltage: ['underVoltage', 'normal', 'overVoltage'],
      total_transformer_load: ['below20', 'load20_50', 'load50_80', 'load80_100', 'above100'],
    };

    // Get the properties for the current tab
    const properties = propertyMap[tabKey] || [];

    // Calculate totals for each property
    const totals = data.reduce(
      (acc, curr) => {
        properties.forEach(prop => {
          acc[prop] = (acc[prop] || 0) + (curr[prop] || 0);
        });
        return acc;
      },
      {} as Record<string, number>
    );

    // Calculate the grand total
    const grandTotal = properties.reduce((sum, prop) => sum + (totals[prop] || 0), 0);

    // Calculate percentages
    const percentages = properties.reduce((acc, prop) => {
      acc[`${prop}Percentage`] = grandTotal > 0 ? ((totals[prop] || 0) / grandTotal * 100).toFixed(1) : '0';
      return acc;
    }, {} as Record<string, string>);

    // Map column titles to properties
    const titleToProperty: Record<string, string> = {
      'Good': 'good',
      'Fair': 'fair',
      'Poor': 'poor',
      'Ok': 'ok',
      'One Missed': 'oneMissed',
      'Two Missed': 'twoMissed',
      'All Missed': 'allMissed',
      'Full': 'full',
      '0.75': 'threeFourths',
      '0.5': 'half',
      '0.25': 'oneFourth',
      'Acceptable': 'acceptable',
      'Not Acceptable': 'notAcceptable',
      'Yes': 'yes',
      'No': 'no',
      'Available': 'available',
      'Not Available': 'notAvailable',
      'Balanced': 'balanced',
      'Unbalanced': 'unbalanced',
      'Normal': 'normal',
      'Under Voltage': 'underVoltage',
      'Over Voltage': 'overVoltage',
      'Below 20%': 'below20',
      '20-50%': 'load20_50',
      '50-80%': 'load50_80',
      '80-100%': 'load80_100',
      'Above 100%': 'above100',
    };

    // Get the column titles for the current tab
    const columnTitles = specificColumns[tabKey] || [];

    // Create the summary cells
    const summaryCells = [];

    // Add the Grand Total cell
    summaryCells.push(
      <Table.Summary.Cell key="title" index={0} colSpan={2}>
        Grand Total
      </Table.Summary.Cell>
    );

    // Add cells for each column
    columnTitles.forEach((title, idx) => {
      const prop = titleToProperty[title] || title.toLowerCase();
      summaryCells.push(
        <Table.Summary.Cell key={`${prop}-count`} index={idx * 2 + 2}>
          {totals[prop] || 0}
        </Table.Summary.Cell>
      );
      summaryCells.push(
        <Table.Summary.Cell key={`${prop}-percentage`} index={idx * 2 + 3}>
          {percentages[`${prop}Percentage`] || '0'}%
        </Table.Summary.Cell>
      );
    });

    // Add the total cells
    summaryCells.push(
      <Table.Summary.Cell key="total-count" index={columnTitles.length * 2 + 2}>
        {grandTotal}
      </Table.Summary.Cell>
    );
    summaryCells.push(
      <Table.Summary.Cell key="total-percentage" index={columnTitles.length * 2 + 3}>
        100%
      </Table.Summary.Cell>
    );

    return (
      <Table.Summary.Row>
        {summaryCells}
      </Table.Summary.Row>
    );
  }, [specificColumns]);

  // Render content for each tab
  const renderContent = useCallback(
    (tabKey: string) => (
      <div>
        <Scrollbar>
          <Table
            columns={getColumns(tabKey)}
            dataSource={data}
            pagination={false}
            bordered
            size="middle"
            scroll={{ x: "max-content" }}
            className="shadow-lg"
            summary={(currentData) => renderGrandTotalRow(tabKey, currentData as InspectionData[])}
          />
        </Scrollbar>
        <div className="flex flex-wrap justify-center gap-4 mt-4">
          <InspectionGraph tabKey={tabKey} data={data} />
          <InspectionPieChart tabKey={tabKey} data={data} />
        </div>
      </div>
    ),
    [data, getColumns, renderGrandTotalRow]
  );

  // Generate tab panes dynamically
  const tabPanes = useMemo(
    () =>
      Object.entries(INSPECTION_TABS).map(([key, label]) => (
        <TabPane tab={label} key={key}>
          {renderContent(key)}
        </TabPane>
      )),
    [renderContent]
  );

  if (loading) {
    return (
      <div className="h-[400px]">
        <CircleLoading />
      </div>
    );
  }

  return (
    <div className="w-full p-4">
      <h2 className="text-xl font-bold mb-4">Inspection Analysis</h2>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        {tabPanes}
      </Tabs>
    </div>
  );
};

export default React.memo(InspectionAnalysis);










