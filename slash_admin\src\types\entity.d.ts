export interface Feeder {
    id: string;
    feeder_name: string;
    voltage_level: string;
    peak_load: number;
    length: number;
    number_of_transformer: number;
    substation_id: number;
}

export interface Substation {
    id: string;
    name: string;
    feeders: Feeder[];
}

export interface Organization {
    csc_code: string;
    name: string;
    type?: 'region' | 'csc';
    substations?: Substation[];
}
