import React, { useCallback, useState, useEffect } from "react";
import { debounce } from "lodash";
import { cleanParams, cn } from "@/lib/utils";
import { Button } from "@/pages/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/pages/components/ui/select";
import { Label } from "@/pages/components/ui/label";
import { Slider } from "@/pages/components/ui/slider";
import { PropertyTypeIcons, SearchTypeDisplay, SearchTypeEnum } from "@/lib/constants";
import { FilterType, useDashboardStore } from "@/store/dashboardStore";
import transformerService from "@/api/services/transformerService";
import { useFilteredStore } from "@/store/filteredStore";
import orgService from "@/api/services/orgService";
import { CircleLoading } from "@/components/loading";

// Define choice constants to match the backend model
const SERVICE_TYPE_CHOICES = [
	{ value: "Dedicated", label: "Dedicated" },
	{ value: "Public", label: "Public" },
];

const CONDITION_CHOICES = [
	{ value: "Good", label: "Good" },
	{ value: "Fair", label: "Fair" },
	{ value: "Poor", label: "Poor" },
];

const STATUS_CHOICES = [
	{ value: "Ok", label: "Ok" },
	{ value: "one missed", label: "One Missed" },
	{ value: "two missed", label: "Two Missed" },
	{ value: "all missed", label: "All Missed" },
];

const OIL_LEVEL_CHOICES = [
	{ value: "Full", label: "Full" },
	{ value: "0.75", label: "0.75" },
	{ value: "0.5", label: "0.5" },
	{ value: "0.25", label: "0.25" },
];

const INSULATION_LEVEL_CHOICES = [
	{ value: "Acceptable", label: "Acceptable" },
	{ value: "Not Acceptable", label: "Not Acceptable" },
];

const HORN_GAP_CHOICES = [
	{ value: "Good", label: "Good" },
	{ value: "Poor", label: "Poor" },
];

// Constants for slider values
const YEAR_RANGE = [1900, 2025];
const DEBOUNCE_DELAY = 300; // ms

// Define interfaces for better type safety
interface Station {
	basestation?: {
		gps_location?: string;
		region?: string;
		csc?: string;
	};
	transformer_data?: {
		basestation?: {
			gps_location?: string;
			region?: string;
			csc?: string;
		};
	};
	[key: string]: any;
}

interface ProcessedData {
	validData: Station[];
	missingBasestation: Station[];
	missingGPS: Station[];
}

interface LocalFilters {
	searchType: string;
	trafo_type?: string;
	capacity?: string;
	primary_voltage?: string;
	colling_type?: string;
	manufacturer?: string;
	vector_group?: string;
	year_of_manufacturing?: [number, number];
	// ... other fields
}


const FiltersFull: React.FC = () => {
	const { setFilters } = useDashboardStore();
	const { 
		setFilteredbaseStation, 
		setSearchType, 
		setMissingBasestations, 
		setMissingGPSStations,
		setIsLoading 
	} = useFilteredStore();

	// Local state for filters
	const [localFilters, setLocalFilters] = useState<FilterType>({});

	// State for regions and selections
	const [regions, setRegions] = useState<any[]>([]);
	const [selectedCSCs, setSelectedCSCs] = useState<any[]>([]);
	const [selectedSubstations, setSelectedSubstations] = useState<any[]>([]);
	const [selectedFeeders, setSelectedFeeders] = useState<any[]>([]);

	// Load regions when component mounts
	useEffect(() => {
		const loadRegions = async () => {
			try {
				const data = await orgService.getOrgList();
				setRegions(data);
			} catch (error) {
				console.error("Error fetching regions:", error);
			}
		};
		loadRegions();
	}, []);

	// Memoize the filter update function to prevent recreation on each render
	const updateURL = useCallback(
		debounce(async (newFilters: FilterType) => {
			try {
				setIsLoading(true);  // Start loading
				const cleanFilters = cleanParams(newFilters);
				const response = await transformerService.getBasestationsFiltered(cleanFilters);

				if (response) {
					// Set search type from filters
					if (newFilters.searchType) {
						setSearchType(newFilters.searchType as string);
					}

					// Process and filter data
					const { validData, missingBasestation, missingGPS } = response.reduce(
						(acc: ProcessedData, station: Station) => {
							const hasBasestation =
								(newFilters.searchType === "Transformer" && station.basestation) ||
								(newFilters.searchType === "LatestInspection" && station.transformer_data?.basestation) ||
								(newFilters.searchType === "BaseStation" && station.gps_location);

							const hasGPS =
								hasBasestation &&
								((newFilters.searchType === "Transformer" && station.basestation?.gps_location) ||
								(newFilters.searchType === "LatestInspection" && station.transformer_data?.basestation?.gps_location)) ||
								(newFilters.searchType === "BaseStation" && station.gps_location);

							if (hasBasestation && hasGPS) {
								acc.validData.push(station);
							} else if (!hasBasestation) {
								acc.missingBasestation.push(station);
							} else if (!hasGPS) {
								acc.missingGPS.push(station);
							}
							return acc;
						},
						{ validData: [], missingBasestation: [], missingGPS: [] }
					);


					// Process valid data
					const processedData = validData.map((station) => ({
						...station,
						gps_location:
							newFilters.searchType === "Transformer"
								? station.basestation.gps_location : newFilters.searchType === "LatestInspection" ? station.transformer_data.basestation.gps_location
								: station.gps_location,
					}));

					// Update state
					setFilteredbaseStation(processedData);
					setMissingBasestations(missingBasestation);
					setMissingGPSStations(missingGPS);
				}
			} catch (error) {
				console.error("Error fetching filtered data:", error);
			} finally {
				setIsLoading(false);  // Stop loading
			}
		}, DEBOUNCE_DELAY),
		[setFilteredbaseStation, setSearchType, setMissingBasestations, setMissingGPSStations, setIsLoading]
	);

	// Handle form submission
	const handleSubmit = useCallback(() => {
		updateURL(localFilters);
	}, [localFilters, updateURL]);

	// Reset all filters
	const handleReset = useCallback(() => {
		setLocalFilters({});
		setFilters({});
		updateURL({});
	}, [setFilters, updateURL]);

	// Handle filter changes
	const handleFilterChange = useCallback(
		(key: string, value: any) => {
			setLocalFilters((prev) => ({
				...prev,
				[key]: value,
			}));
		},
		[]
	);

	// if (!isFiltersFullOpen) return null;

	return (
		<div className="p-4 space-y-4 overflow-y-auto relative">
			{/* Location */}
			{/* <div>
        <Label>Location</Label>
        <div className="flex items-center">
          <Input
            placeholder="Search location"
            value={localFilters.location || ""}
            onChange={(e) =>
              setLocalFilters((prev) => ({
                ...prev,
                location: e.target.value,
              }))
            }
            className="rounded-l-xl rounded-r-none border-r-0"
          />
          <Button onClick={handleLocationSearch} className="rounded-r-xl">
            <Search className="w-4 h-4" />
          </Button>
        </div>
      </div> */}

			{/* Property Type */}

			<div>
				<h4 className="font-bold mb-2">{localFilters.searchType || "Transformer"}</h4>
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
					{Object.entries(PropertyTypeIcons).map(([type, Icon]) => (
						<div
							key={type}
							className={cn(
								"flex flex-col items-center justify-center p-4 border rounded-xl cursor-pointer",
								localFilters.searchType === type ? "border-black bg-primary" : "border-gray-200",
							)}
							onClick={() => {
								setLocalFilters((prev) => ({
									// Reset to default values
									searchType: type as SearchTypeEnum,
									region: undefined,
									csc: undefined,
									Substation: undefined,
									Feeder: undefined,
									trafo_type: undefined,
									capacity: undefined,
									primary_voltage: undefined,
									colling_type: undefined,
									manufacturer: undefined,
									vector_group: undefined,
									year_of_manufacturing: undefined,
								}));
								updateURL({
									searchType: type as SearchTypeEnum,
								});
							}}
						>
							<Icon className="w-6 h-6 mb-2" />
							<span className="text-center">{SearchTypeDisplay[type as SearchTypeEnum]}</span>
						</div>
					))}
				</div>
			</div>

			{/* BaseStation */}
			{localFilters.searchType == "BaseStation" && (
				<div>
					<div>
						{/* Region */}
						<Label>Region</Label>
						<Select
							value={localFilters.region || "any"}
							onValueChange={(value) => {
								const selectedRegion = regions.find(region => region.csc_code === value);
								if (selectedRegion) {
									setSelectedCSCs(selectedRegion.csc_centers);
									setSelectedSubstations(selectedRegion.substations);
									setLocalFilters((prev) => ({ 
										...prev, 
										region: value,
										csc: undefined,
										Substation: undefined,
										Feeder: undefined
									}));
								}
							}}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Region" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{regions.map(region => (
									<SelectItem key={region.csc_code} value={region.csc_code}>
										{region.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						{/* CSC */}
						<Label>CSC</Label>
						<Select
							value={localFilters.csc || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, csc: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="CSC" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{selectedCSCs.map(csc => (
									<SelectItem key={csc.csc_code} value={csc.name}>
										{csc.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						{/* Substation */}
						<Label>Substation</Label>
						<Select
							value={localFilters.Substation || "any"}
							onValueChange={(value) => {
								const selectedRegion = regions.find(region => 
									region.substations.some((sub: any) => sub.name === value)
								);
								
								if (selectedRegion) {
									const substation = selectedRegion.substations.find(
										(sub: any) => sub.name === value
									);
									if (substation) {
										setSelectedFeeders(substation.feeders || []);
										setLocalFilters((prev) => ({ 
											...prev, 
											Substation: value,
											Feeder: undefined 
										}));
									}
								}
							}}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Substation" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{selectedSubstations.map((sub: any) => (
									<SelectItem key={sub.id} value={sub.name}>
										{sub.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Feeder</Label>
						<Select
							value={localFilters.Feeder || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, Feeder: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Feeder" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{selectedFeeders.map((feeder: any) => (
									<SelectItem key={feeder.id} value={feeder.feeder_name}>
										{feeder.feeder_name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>
			)}

			{/* Transformer */}
			{localFilters.searchType == "Transformer" && (
				<div>
					<div>
						{/* Region */}
						<Label>Region</Label>
						<Select
							value={localFilters.region || "any"}
							onValueChange={(value) => {
								const selectedRegion = regions.find(region => region.csc_code === value);
								if (selectedRegion) {
									setSelectedCSCs(selectedRegion.csc_centers);
									setSelectedSubstations(selectedRegion.substations);
									setLocalFilters((prev) => ({ 
										...prev, 
										region: value,
										csc: undefined,
										Substation: undefined,
										Feeder: undefined
									}));
								}
							}}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Region" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{regions.map(region => (
									<SelectItem key={region.csc_code} value={region.csc_code}>
										{region.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						{/* CSC */}
						<Label>CSC</Label>
						<Select
							value={localFilters.csc || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, csc: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="CSC" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{selectedCSCs.map(csc => (
									<SelectItem key={csc.csc_code} value={csc.name}>
										{csc.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Transformer Type</Label>
						<Select
							value={localFilters.trafo_type || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, trafo_type: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Transformer Type" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								<SelectItem value="Conservator">Conservator</SelectItem>
								<SelectItem value="Hermatical">Hermatical</SelectItem>
								<SelectItem value="Compact">Compact</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Capacity</Label>
						<Select
							value={localFilters.capacity || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, capacity: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Capacity" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								<SelectItem value="10">10 kVA</SelectItem>
								<SelectItem value="25">25 kVA</SelectItem>
								<SelectItem value="50">50 kVA</SelectItem>
								<SelectItem value="100">100 kVA</SelectItem>
								<SelectItem value="200">200 kVA</SelectItem>
								<SelectItem value="315">315 kVA</SelectItem>
								<SelectItem value="400">400 kVA</SelectItem>
								<SelectItem value="500">500 kVA</SelectItem>
								<SelectItem value="630">630 kVA</SelectItem>
								<SelectItem value="800">800 kVA</SelectItem>
								<SelectItem value="1250">1250 kVA</SelectItem>
								<SelectItem value="2500">2500 kVA</SelectItem>
								<SelectItem value="null">Other</SelectItem>
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Primary Voltage</Label>
						<Select
							value={localFilters.primary_voltage || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, primary_voltage: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Primary Voltage" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								<SelectItem value="15">15 kV</SelectItem>
								<SelectItem value="19">19 kV</SelectItem>
								<SelectItem value="33">33 kV</SelectItem>
								{/* <SelectItem value="null">Other</SelectItem> */}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Cooling Type</Label>
						<Select
							value={localFilters.colling_type || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, colling_type: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Cooling Type" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								<SelectItem value="ONAN">ONAN</SelectItem>
								<SelectItem value="Dry Type">Dry Type</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* <div>
						<Label>Manufacturer</Label>
						<Select
							value={localFilters.manufacturer || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, manufacturer: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Manufacturer" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								<SelectItem value="ABB Tanzania">ABB Tanzania</SelectItem>
								<SelectItem value="Apex">Apex</SelectItem>
								<SelectItem value="China Natinal Electric wire and cable Imp/Exp corporations">
									China Natinal Electric wire and cable Imp/Exp corporations
								</SelectItem>
								<SelectItem value="Iran Transformer">Iran Transformer</SelectItem>
								<SelectItem value="Kobera">Kobera</SelectItem>
								<SelectItem value="Koncar">Koncar</SelectItem>
								<SelectItem value="Mar son's">Mar son's</SelectItem>
								<SelectItem value="METEC">METEC</SelectItem>
								<SelectItem value="Minel Transformer">Minel Transformer</SelectItem>
							</SelectContent>
						</Select>
					</div> */}

					<div>
						<Label>Vector Group</Label>
						<Select
							value={localFilters.vector_group || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, vector_group: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Vector Group" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								<SelectItem value="DY1">DY1</SelectItem>
								<SelectItem value="DY5">DY5</SelectItem>
								<SelectItem value="DY11">DY11</SelectItem>
								<SelectItem value="Other">Other</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* <div>
						<Label>Year of Manufacturing</Label>
						<Slider
							defaultValue={[1900, 2025]}
							min={1900}
							max={2025}
							step={1}
							value={(localFilters.year_of_manufacturing as [number, number]) || [1900, 2025]}
							onValueChange={(value) =>
								setLocalFilters((prev) => ({
									...prev,
									year_of_manufacturing: value as [number, number],
								}))
							}
						/>
						<div className="flex justify-between text-sm">
							<span>{localFilters.year_of_manufacturing?.[0] ?? 1900}</span>
							<span>{localFilters.year_of_manufacturing?.[1] ?? 2025}</span>
						</div>
					</div> */}
				</div>
			)}

			{/* Transformer */}
			{localFilters.searchType == "LatestInspection" && (
				<div>
					<div>
						{/* Region */}
						<Label>Region</Label>
						<Select
							value={localFilters.region || "any"}
							onValueChange={(value) => {
								const selectedRegion = regions.find(region => region.csc_code === value);
								if (selectedRegion) {
									setSelectedCSCs(selectedRegion.csc_centers);
									setSelectedSubstations(selectedRegion.substations);
									setLocalFilters((prev) => ({ 
										...prev, 
										region: value,
										csc: undefined,
										Substation: undefined,
										Feeder: undefined
									}));
								}
							}}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Region" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{regions.map(region => (
									<SelectItem key={region.csc_code} value={region.csc_code}>
										{region.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						{/* CSC */}
						<Label>CSC</Label>
						<Select
							value={localFilters.csc || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, csc: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="CSC" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{selectedCSCs.map(csc => (
									<SelectItem key={csc.csc_code} value={csc.name}>
										{csc.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					
					<div>
						<Label>Arrester</Label>
						<Select
							value={localFilters.Arrester || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, Arrester: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Arrester" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{STATUS_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Drop Out Fuse</Label>
						<Select
							value={localFilters.DropOutFuse || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, DropOutFuse: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Drop Out Fuse" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{STATUS_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>MV Bushing</Label>
						<Select
							value={localFilters.MVBushing || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, MVBushing: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="MV Bushing" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{STATUS_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>LV Bushing</Label>
						<Select
							value={localFilters.LVBushing || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, LVBushing: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="LV Bushing" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{STATUS_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Oil Level</Label>
						<Select
							value={localFilters.OilLevel || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, OilLevel: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Oil Level" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{OIL_LEVEL_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Insulation Level</Label>
						<Select
							value={localFilters.InsulationLevel || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, InsulationLevel: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Insulation Level" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{INSULATION_LEVEL_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Horn Gap</Label>
						<Select
							value={localFilters.HornGap || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, HornGap: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Horn Gap" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{HORN_GAP_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Service Type</Label>
						<Select
							value={localFilters.ServiceType || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, ServiceType: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Service Type" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{SERVICE_TYPE_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label>Silica Gel</Label>
						<Select
							value={localFilters.silica_gel || "any"}
							onValueChange={(value) => setLocalFilters((prev) => ({ ...prev, silica_gel: value }))}
						>
							<SelectTrigger className="w-full rounded-xl">
								<SelectValue placeholder="Silica Gel" />
							</SelectTrigger>
							<SelectContent className="bg-gray-100">
								{CONDITION_CHOICES.map((choice) => (
									<SelectItem key={choice.value} value={choice.value}>
										{choice.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>
			)}

			{/* <div>
        <Label>Price Range (Monthly)</Label>
        <Slider
          defaultValue={[0, 10000]}
          min={0}
          max={10000}
          step={100}
          value={(localFilters.priceRange as [number, number]) || [0, 10000]}
          onValueChange={(value) =>
            setLocalFilters((prev) => ({
              ...prev,
              priceRange: value as [number, number],
            }))
          }
        />
        <div className="flex justify-between text-sm">
          <span>${localFilters.priceRange?.[0] ?? 0}</span>
          <span>${localFilters.priceRange?.[1] ?? 10000}</span>
        </div>
      </div>

      <div>
        <Label>Beds</Label>
        <Select
              value={localFilters.beds || "any"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({ ...prev, beds: value }))
              }
            >
              <SelectTrigger className="w-full rounded-xl">
                <SelectValue placeholder="Beds" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any beds</SelectItem>
                <SelectItem value="1">1+ bed</SelectItem>
                <SelectItem value="2">2+ beds</SelectItem>
                <SelectItem value="3">3+ beds</SelectItem>
                <SelectItem value="4">4+ beds</SelectItem>
              </SelectContent>
            </Select>
      </div>
      <div>
        <Label>Baths</Label>
        <Select
              value={localFilters.baths || "any"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({ ...prev, baths: value }))
              }
            >
              <SelectTrigger className="w-full rounded-xl">
                <SelectValue placeholder="Baths" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="any">Any baths</SelectItem>
                <SelectItem value="1">1+ bath</SelectItem>
                <SelectItem value="2">2+ baths</SelectItem>
                <SelectItem value="3">3+ baths</SelectItem>
              </SelectContent>
            </Select>
      </div>

      <div>
        <Label>Square Feet</Label>
        <Slider
          defaultValue={[0, 5000]}
          min={0}
          max={5000}
          step={100}
          value={(localFilters.squareFeet as [number, number]) || [0, 5000]}
          onValueChange={(value) =>
            setLocalFilters((prev) => ({
              ...prev,
              squareFeet: value as [number, number],
            }))
          }
        />
        <div className="flex justify-between text-sm">
          <span>{localFilters.squareFeet?.[0] ?? 0} sq ft</span>
          <span>{localFilters.squareFeet?.[1] ?? 5000} sq ft</span>
        </div>
      </div>

      <div>
        <Label>Amenities</Label>
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(AmenityIcons).map(([amenity, Icon]) => (
            <Button
              key={amenity}
              variant={localFilters.amenities?.includes(amenity) ? "default" : "outline"}
              onClick={() => handleAmenityChange(amenity)}
            >
              <Icon className="w-4 h-4 mr-2" />
              {formatEnumString(amenity)}
            </Button>
          ))}
        </div>
      </div>

      <div>
        <Label>Available From</Label>
        <Input
          type="date"
          value={localFilters.availableFrom || ""}
          onChange={(e) =>
            setLocalFilters((prev) => ({
              ...prev,
              availableFrom: e.target.value || "",
            }))
          }
          className="rounded-xl"
        />
      </div> */}

			<div className="flex justify-between">
				<Button onClick={handleSubmit}>APPLY</Button>
				<Button variant="outline" onClick={handleReset}>
					Reset Filters
				</Button>
			</div>
		</div>
	);
};

export default FiltersFull;










