#!/usr/bin/env python3
"""
Script to restart Django server with proper ASGI support
"""
import os
import sys
import subprocess
import time
import requests

def check_server_running():
    """Check if Django server is running"""
    try:
        response = requests.get('http://localhost:8000/api/websocket/test/', timeout=2)
        return response.status_code == 200
    except:
        return False

def kill_existing_server():
    """Kill any existing Django server processes"""
    try:
        # On Windows, find and kill Django processes
        if os.name == 'nt':
            subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                         capture_output=True, text=True)
        else:
            # On Unix-like systems
            subprocess.run(['pkill', '-f', 'runserver'], 
                         capture_output=True, text=True)
        print("🔄 Stopped existing Django server processes")
        time.sleep(2)
    except Exception as e:
        print(f"Note: {e}")

def start_server():
    """Start Django server with ASGI support"""
    print("🚀 Starting Django server with ASGI support...")
    print("📡 WebSocket endpoints will be available at:")
    print("   - ws://localhost:8000/ws/messages/")
    print("   - ws://localhost:8000/ws/notifications/<user_id>/")
    print("   - ws://localhost:8000/ws/assignments/")
    print("=" * 60)
    
    # Start the server
    try:
        subprocess.run([sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000'], 
                      cwd=os.path.dirname(os.path.abspath(__file__)))
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")

def main():
    """Main function"""
    print("🔧 Django ASGI Server Restart Script")
    print("=" * 40)
    
    # Check if server is running
    if check_server_running():
        print("📍 Django server is currently running")
        kill_existing_server()
    else:
        print("📍 No Django server detected")
    
    # Wait a moment
    time.sleep(1)
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()
