#!/usr/bin/env python
"""
Final comprehensive test to verify all SMS app functionality.
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the core directory to Python path
sys.path.append(os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from sms_app.models import SMSMessage, Department
from account.models import User, Role

def test_django_setup():
    """Test Django setup and database connectivity"""
    try:
        print("🧪 Testing Django setup...")
        
        # Test database connection
        user_count = User.objects.count()
        message_count = SMSMessage.objects.count()
        
        print(f"✅ Database connected - Users: {user_count}, Messages: {message_count}")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def test_wsgi_application():
    """Test WSGI application loading"""
    try:
        print("🧪 Testing WSGI application...")
        from core.wsgi import application
        print("✅ WSGI application loaded successfully")
        return True
    except Exception as e:
        print(f"❌ WSGI application failed: {e}")
        return False

def test_asgi_application():
    """Test ASGI application loading"""
    try:
        print("🧪 Testing ASGI application...")
        from core.asgi import application
        print("✅ ASGI application loaded successfully")
        return True
    except Exception as e:
        print(f"❌ ASGI application failed: {e}")
        return False

def test_channel_layers():
    """Test channel layers configuration"""
    try:
        print("🧪 Testing channel layers...")
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        
        if channel_layer:
            print(f"✅ Channel layer configured: {channel_layer.__class__.__name__}")
            return True
        else:
            print("❌ No channel layer configured")
            return False
    except Exception as e:
        print(f"❌ Channel layer test failed: {e}")
        return False

def test_websocket_consumers():
    """Test WebSocket consumer imports"""
    try:
        print("🧪 Testing WebSocket consumers...")
        from sms_app.consumers import MessageConsumer, NotificationConsumer, AssignmentConsumer
        print("✅ All WebSocket consumers imported successfully")
        return True
    except Exception as e:
        print(f"❌ WebSocket consumer test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints accessibility"""
    try:
        print("🧪 Testing API endpoints...")
        base_url = "http://127.0.0.1:8000"
        
        # Test basic endpoints
        endpoints = [
            "/admin/",
            "/api/messages/",
            "/api/users/",
            "/auth/signin",
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                status = "✅" if response.status_code in [200, 301, 302, 401, 403] else "❌"
                print(f"  {status} {endpoint} - Status: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"  ❌ {endpoint} - Error: {e}")
        
        return True
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

def test_model_operations():
    """Test basic model operations"""
    try:
        print("🧪 Testing model operations...")
        
        # Test User model
        user_count = User.objects.count()
        print(f"  ✅ User model - Count: {user_count}")
        
        # Test SMSMessage model
        message_count = SMSMessage.objects.count()
        print(f"  ✅ SMSMessage model - Count: {message_count}")
        
        # Test Department model
        dept_count = Department.objects.count()
        print(f"  ✅ Department model - Count: {dept_count}")
        
        return True
    except Exception as e:
        print(f"❌ Model operations test failed: {e}")
        return False

def test_serializers():
    """Test serializer imports"""
    try:
        print("🧪 Testing serializers...")
        from sms_app.serializers import (
            SMSMessageSerializer, UserSerializer, ReplySerializer,
            NotificationSerializer, DepartmentSerializer
        )
        print("✅ All serializers imported successfully")
        return True
    except Exception as e:
        print(f"❌ Serializer test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting comprehensive SMS app test...")
    print("=" * 50)
    
    tests = [
        test_django_setup,
        test_wsgi_application,
        test_asgi_application,
        test_channel_layers,
        test_websocket_consumers,
        test_model_operations,
        test_serializers,
        test_api_endpoints,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! SMS app is working perfectly!")
        print()
        print("✅ WSGI Application: Working")
        print("✅ ASGI Application: Working") 
        print("✅ WebSocket Support: Configured")
        print("✅ Database Models: Working")
        print("✅ API Endpoints: Accessible")
        print("✅ Serializers: Working")
        print()
        print("🌐 WebSocket Endpoints Available:")
        print("  - ws://localhost:8000/ws/messages/")
        print("  - ws://localhost:8000/ws/notifications/{user_id}/")
        print("  - ws://localhost:8000/ws/assignments/")
        print()
        print("🔗 API Endpoints Available:")
        print("  - http://localhost:8000/admin/")
        print("  - http://localhost:8000/api/messages/")
        print("  - http://localhost:8000/api/users/")
        print("  - http://localhost:8000/auth/signin")
    else:
        print(f"⚠️ {total - passed} tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test runner crashed: {e}")
        sys.exit(1)
