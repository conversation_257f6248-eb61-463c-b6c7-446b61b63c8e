import React, { useState, useEffect } from 'react';
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { User } from '../../../../types';
import apiClient from '../../../../api/apiClient';

interface AdvancedFiltersProps {
  filters: {
    status: string;
    category: string;
    priority: string;
    assignee: string;
    tags: string[];
    dateRange: { start: string; end: string };
    search: string;
    archived: boolean;
  };
  onFiltersChange: (filters: any) => void;
  isExpanded: boolean;
  totalMessages: number;
  onToggleExpanded: () => void;
}

export default function AdvancedFilters({
  filters,
  onFiltersChange,
  isExpanded,
  totalMessages,
  onToggleExpanded
}: AdvancedFiltersProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  const availableTags = [
    'urgent', 'fraud', 'technical', 'billing-dispute', 'safety-hazard',
    'repeat-customer', 'vip', 'escalated', 'follow-up-required'
  ];

  // Fetch users for assignee dropdown
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get({ url: '/users/' });
        setUsers(response.results || response);
      } catch (error) {
        console.error('Error fetching users:', error);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };

    if (isExpanded) {
      fetchUsers();
    }
  }, [isExpanded]);

  const handleTagToggle = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag];
    onFiltersChange({ ...filters, tags: newTags });
  };

  const clearFilters = () => {
    onFiltersChange({
      status: 'all',
      category: 'all',
      priority: 'all',
      assignee: 'all',
      tags: [],
      dateRange: { start: '', end: '' },
      search: '',
      archived: false
    });
  };

  const hasActiveFilters = filters.status !== 'all' || 
    filters.category !== 'all' || 
    filters.priority !== 'all' || 
    filters.assignee !== 'all' || 
    filters.tags.length > 0 || 
    filters.dateRange.start || 
    filters.dateRange.end || 
    filters.search || 
    filters.archived;

  return (
    <div className="border-b border-gray-200">
      {/* Search Bar */}
      <div className="p-4">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search messages, phone numbers, case IDs..."
            value={filters.search}
            onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
          />
        </div>
      </div>

      {/* Filter Toggle */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between">
          {/* <button
            onClick={onToggleExpanded}
            className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900"
          >
            <FunnelIcon className="h-4 w-4" />
            Advanced Filters
            {hasActiveFilters && (
              <span className="bg-sky-100 text-sky-800 px-2 py-0.5 rounded-full text-xs">
                Active
              </span>
            )}
          </button> */}
          <button
                      onClick={onToggleExpanded}
                      className={`flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                        isExpanded 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      <FunnelIcon className="h-4 w-4" />
                      Filters
                      {hasActiveFilters && (
                        <span className="ml-1 px-1.5 py-0.5 bg-blue-500 text-white rounded-full text-xs">
                          {[filters.status, filters.category, filters.priority, filters.assignee]
                            .filter(f => f !== 'all').length + 
                            (filters.tags.length > 0 ? 1 : 0) +
                            (filters.search ? 1 : 0) +
                            (filters.archived ? 1 : 0)}
                        </span>
                      )}
                    </button>
          <div className="text-sm text-gray-600">
            {totalMessages} {totalMessages === 1 ? 'message' : 'messages'}
            {hasActiveFilters && (
              <span className="ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                Filtered
              </span>
            )}
          </div>
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-1 text-sm text-red-600 hover:text-red-700"
            >
              <XMarkIcon className="h-4 w-4" />
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="px-4 pb-4 space-y-4 bg-gray-50">
          {/* Basic Filters Row */}
          <div className="grid grid-cols-2 gap-3">
            <select
              value={filters.status}
              onChange={(e) => onFiltersChange({ ...filters, status: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="in-progress">In Progress</option>
              <option value="replied">Replied</option>
              <option value="closed">Closed</option>
            </select>

            <select
              value={filters.category}
              onChange={(e) => onFiltersChange({ ...filters, category: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
            >
              <option value="all">All Categories</option>
              <option value="power-outage">Power Outage</option>
              <option value="billing">Billing</option>
              <option value="wire-cut">Wire Cut</option>
              <option value="fallen-pole">Fallen Pole</option>
              <option value="corruption">Corruption</option>
              <option value="general">General</option>
              <option value="other">Other</option>
            </select>
          </div>

          {/* Priority and Assignee Row */}
          <div className="grid grid-cols-2 gap-3">
            <select
              value={filters.priority}
              onChange={(e) => onFiltersChange({ ...filters, priority: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
            >
              <option value="all">All Priorities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={filters.assignee}
              onChange={(e) => onFiltersChange({ ...filters, assignee: e.target.value })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
              disabled={loading}
            >
              <option value="all">All Assignees</option>
              <option value="unassigned">Unassigned</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name || user.username || user.email}
                </option>
              ))}
              {loading && <option disabled>Loading users...</option>}
            </select>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">From Date</label>
              <input
                type="date"
                value={filters.dateRange.start}
                onChange={(e) => onFiltersChange({ 
                  ...filters, 
                  dateRange: { ...filters.dateRange, start: e.target.value }
                })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">To Date</label>
              <input
                type="date"
                value={filters.dateRange.end}
                onChange={(e) => onFiltersChange({ 
                  ...filters, 
                  dateRange: { ...filters.dateRange, end: e.target.value }
                })}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-sky-500 focus:border-sky-500"
              />
            </div>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-2">Tags</label>
            <div className="flex flex-wrap gap-2">
              {availableTags.map(tag => (
                <button
                  key={tag}
                  onClick={() => handleTagToggle(tag)}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                    filters.tags.includes(tag)
                      ? 'bg-sky-100 text-sky-800 border border-sky-200'
                      : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>

          {/* Archive Toggle */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="archived"
              checked={filters.archived}
              onChange={(e) => onFiltersChange({ ...filters, archived: e.target.checked })}
              className="h-4 w-4 text-sky-600 focus:ring-sky-500 border-gray-300 rounded"
            />
            <label htmlFor="archived" className="ml-2 block text-sm text-gray-700">
              Show archived messages
            </label>
          </div>
        </div>
      )}
    </div>
  );
}