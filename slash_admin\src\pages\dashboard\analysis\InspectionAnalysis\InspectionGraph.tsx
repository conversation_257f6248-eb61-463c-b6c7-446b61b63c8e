import React from "react";
import { Column } from "@ant-design/plots";
import { InspectionData } from "./index";

interface InspectionGraphProps {
  tabKey: string;
  data: InspectionData[];
}

const InspectionGraph: React.FC<InspectionGraphProps> = ({ tabKey, data }) => {
  const prepareGraphData = () => {
    // Filter out the grand total row if it exists
    const filteredData = data.filter((item) => !item.key.includes("grand_total"));
    
    // Prepare data based on the tab key
    switch (tabKey) {
      case "body_condition":
      case "status_of_mounting":
      case "mounting_condition":
      case "silica_gel":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Good", value: region.good || 0 },
          { region: region.region, type: "Fair", value: region.fair || 0 },
          { region: region.region, type: "Poor", value: region.poor || 0 },
        ]);
      case "arrester":
      case "drop_out":
      case "fuse_link":
      case "mv_bushing":
      case "mv_cable_lug":
      case "lv_bushing":
      case "lv_cable_lug":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Ok", value: region.ok || 0 },
          { region: region.region, type: "One Missed", value: region.oneMissed || 0 },
          { region: region.region, type: "Two Missed", value: region.twoMissed || 0 },
          { region: region.region, type: "All Missed", value: region.allMissed || 0 },
        ]);
      case "oil_level":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Full", value: region.full || 0 },
          { region: region.region, type: "0.75", value: region.threeFourths || 0 },
          { region: region.region, type: "0.5", value: region.half || 0 },
          { region: region.region, type: "0.25", value: region.oneFourth || 0 },
        ]);
      case "insulation_level":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Acceptable", value: region.acceptable || 0 },
          { region: region.region, type: "Not Acceptable", value: region.notAcceptable || 0 },
        ]);
      case "horn_gap":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Good", value: region.good || 0 },
          { region: region.region, type: "Poor", value: region.poor || 0 },
        ]);
      case "has_linkage":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Yes", value: region.yes || 0 },
          { region: region.region, type: "No", value: region.no || 0 },
        ]);
      case "arrester_body_ground":
      case "neutral_ground":
        return filteredData.flatMap((region) => [
          { region: region.region, type: "Available", value: region.available || 0 },
          { region: region.region, type: "Not Available", value: region.notAvailable || 0 },
        ]);
      default:
        return [];
    }
  };

  const config = {
    data: prepareGraphData(),
    isGroup: true,
    xField: "region",
    yField: "value",
    seriesField: "type",
    label: {
      position: "middle",
      layout: [
        { type: "interval-adjust-position" },
        { type: "interval-hide-overlap" },
        { type: "adjust-color" },
      ],
    },
    columnStyle: {
      radius: [4, 4, 0, 0],
    },
  };

  return (
    <div className="mt-8">
      <h3 className="text-lg font-semibold mb-4">Distribution Graph</h3>
      <Column {...config} height={400} width={400} />
    </div>
  );
};

export default InspectionGraph;
