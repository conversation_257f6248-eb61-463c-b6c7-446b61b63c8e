#!/usr/bin/env python3
"""
Test ASGI application directly to verify WebSocket routing
"""
import os
import sys
import django
import asyncio

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

async def test_asgi_application():
    """Test the ASGI application directly"""
    from core.asgi import application
    
    # Create a mock WebSocket scope
    scope = {
        'type': 'websocket',
        'path': '/ws/messages/',
        'query_string': b'',
        'headers': [],
        'client': ['127.0.0.1', 0],
        'server': ['127.0.0.1', 8000],
    }
    
    # Mock receive and send functions
    messages = []
    
    async def receive():
        # Simulate WebSocket connect
        return {'type': 'websocket.connect'}
    
    async def send(message):
        messages.append(message)
        print(f"📤 ASGI Send: {message}")
    
    try:
        print("🔧 Testing ASGI application directly...")
        print(f"📍 Scope: {scope}")
        
        # Call the ASGI application
        await application(scope, receive, send)
        
        print("✅ ASGI application executed successfully")
        print(f"📨 Messages sent: {len(messages)}")
        
        # Check if WebSocket was accepted
        accept_messages = [msg for msg in messages if msg.get('type') == 'websocket.accept']
        if accept_messages:
            print("✅ WebSocket connection would be accepted")
        else:
            print("❌ WebSocket connection would not be accepted")
            
    except Exception as e:
        print(f"❌ Error testing ASGI application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_asgi_application())
