import { SMSMessage, User, Analytics } from '../types';
import { subDays, subHours, addMinutes } from 'date-fns';

export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    department: 'Administration',
    isActive: true
  },
  {
    id: '2',
    name: '<PERSON> Supervisor',
    email: '<EMAIL>',
    role: 'supervisor',
    department: 'Operations',
    isActive: true
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'technician',
    department: 'Field Operations',
    isActive: true
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'technician',
    department: 'Customer Service',
    isActive: true
  },
  {
    id: '5',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'viewer',
    department: 'Management',
    isActive: true
  }
];

export const mockMessages: SMSMessage[] = [
  {
    id: '1',
    phone_number: '+****************',
    content: 'Power is out in my entire neighborhood on Oak Street. Been without electricity for 3 hours now.',
    timestamp: subHours(new Date(), 1),
    status: 'new',
    category: 'power-outage',
    priority: 'high',
    tags: ['neighborhood', 'extended-outage'],
    caseId: 'CASE-2024-001',
    assigned_to: mockUsers[2],
    replies: [],
    attachments: [],
    isArchived: false
  },
  {
    id: '2',
    phone_number: '+****************',
    content: 'There is a wire down across Pine Avenue after the storm. It looks dangerous.',
    timestamp: subHours(new Date(), 2),
    status: 'in-progress',
    category: 'wire-cut',
    priority: 'critical',
    tags: ['storm-damage', 'safety-hazard'],
    caseId: 'CASE-2024-002',
    assigned_to: mockUsers[2],
    replies: [
      {
        id: 'r1',
        content: 'Thank you for reporting this. We have dispatched a crew to secure the area. Please stay away from the downed wire.',
        timestamp: subHours(new Date(), 1.5),
        sender: mockUsers[2],
        is_from_customer: false,
        attachments: []
      }
    ],
    attachments: [],
    isArchived: false
  },
  {
    id: '3',
    phone_number: '+****************',
    content: 'My electricity bill seems way too high this month. Can someone check if there\'s an error?',
    timestamp: subHours(new Date(), 4),
    status: 'replied',
    category: 'billing',
    priority: 'medium',
    tags: ['billing-inquiry'],
    caseId: 'CASE-2024-003',
    assigned_to: mockUsers[3],
    replies: [
      {
        id: 'r2',
        content: 'I understand your concern. Let me review your account and get back to you within 24 hours with details.',
        timestamp: subHours(new Date(), 3),
        sender: mockUsers[3],
        is_from_customer: false,
        attachments: []
      }
    ],
    attachments: [],
    isArchived: false
  },
  {
    id: '4',
    phone_number: '+****************',
    content: 'There\'s a utility pole that looks like it\'s leaning dangerously on Maple Drive. It might fall.',
    timestamp: subHours(new Date(), 6),
    status: 'new',
    category: 'fallen-pole',
    priority: 'high',
    tags: ['structural-damage', 'preventive'],
    caseId: 'CASE-2024-004',
    replies: [],
    attachments: [],
    isArchived: false
  },
  {
    id: '5',
    phone_number: '+****************',
    content: 'I want to report that one of your workers demanded payment under the table for priority service. This is corruption.',
    timestamp: subDays(new Date(), 1),
    status: 'closed',
    category: 'corruption',
    priority: 'critical',
    tags: ['ethics-violation', 'investigation'],
    caseId: 'CASE-2024-005',
    assigned_to: mockUsers[1],
    replies: [
      {
        id: 'r3',
        content: 'Thank you for reporting this serious matter. This has been escalated to our ethics department for investigation.',
        timestamp: subHours(new Date(), 18),
        sender: mockUsers[1],
        is_from_customer: false,
        attachments: []
      }
    ],
    attachments: [],
    isArchived: false
  }
];

export const mockAnalytics: Analytics = {
  totalCases: 156,
  resolvedCases: 128,
  pendingCases: 28,
  averageResponseTime: 2.4,
  categoryCounts: {
    'power-outage': 45,
    'billing': 38,
    'wire-cut': 22,
    'general': 18,
    'fallen-pole': 15,
    'corruption': 8,
    'other': 10
  },
  statusCounts: {
    'new': 12,
    'in-progress': 16,
    'replied': 8,
    'closed': 120
  },
  dailyTrends: [
    { date: '2024-01-15', cases: 8, resolved: 6 },
    { date: '2024-01-16', cases: 12, resolved: 10 },
    { date: '2024-01-17', cases: 15, resolved: 12 },
    { date: '2024-01-18', cases: 9, resolved: 8 },
    { date: '2024-01-19', cases: 18, resolved: 15 },
    { date: '2024-01-20', cases: 22, resolved: 18 },
    { date: '2024-01-21', cases: 16, resolved: 14 }
  ]
};

export const responseTemplates = {
  'power-outage': [
    'Thank you for reporting the power outage. Our crew has been dispatched and we estimate restoration within 2-4 hours.',
    'We are aware of the power outage in your area. Updates will be provided as work progresses.',
    'Power has been restored in your area. Please contact us if you continue experiencing issues.'
  ],
  'billing': [
    'Thank you for your billing inquiry. I will review your account and respond within 24 hours.',
    'Your payment has been received and processed. Thank you.',
    'I have reviewed your account and will send detailed information via mail.'
  ],
  'wire-cut': [
    'Thank you for reporting the downed wire. Please stay away from the area for safety. A crew is en route.',
    'The damaged wire has been secured. Full repairs will be completed within 48 hours.',
    'Wire repairs have been completed in your area.'
  ],
  'general': [
    'Thank you for contacting us. Your request has been received and will be processed.',
    'Your inquiry has been forwarded to the appropriate department.',
    'Thank you for your feedback. We appreciate your input.'
  ]
};