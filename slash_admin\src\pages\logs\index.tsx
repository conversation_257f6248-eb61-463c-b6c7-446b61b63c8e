import React, { useState, useEffect } from 'react';
import { Table, Card, Tabs, TabsProps, Tag, Typography, Button, Popconfirm, message } from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import logService from '@/api/services/logService';
import type { ActivityLog, ErrorLog, DataChangeLog } from '@/api/services/logService';
import { Iconify } from '@/components/icon';
import LogFilters from './LogFilters';
import { useUserInfo } from '@/store/userStore';

const LogsPage: React.FC = () => {
    const [activeTab, setActiveTab] = useState<string>('activity');
    const [loading, setLoading] = useState<boolean>(false);
    const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
    const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([]);
    const [dataChangeLogs, setDataChangeLogs] = useState<DataChangeLog[]>([]);
    const [pagination, setPagination] = useState<TablePaginationConfig>({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [filters, setFilters] = useState({
        start_date: null,
        end_date: null,
        user: '',
        action: undefined,
        model_name: '',
        level: undefined,
        message: '',
        changed_by: '',
        field_name: '',
        record_id: '',
    });
    const { role } = useUserInfo();

    const handleDeleteLogs = async () => {
        try {
            switch (activeTab) {
                case 'activity':
                    await logService.deleteActivityLogs();
                    break;
                case 'error':
                    await logService.deleteErrorLogs();
                    break;
                case 'changes':
                    await logService.deleteDataChangeLogs();
                    break;
            }
            message.success(`${activeTab} logs cleared successfully`);
            fetchLogs(1); // Refresh the data
        } catch (error) {
            message.error(`Failed to clear ${activeTab} logs`);
        }
    };

    const handleFilterChange = (newFilters: any) => {
        setFilters(newFilters);
        fetchLogs(1, pagination.pageSize, newFilters);
    };

    const handleFilterReset = () => {
        setFilters({
            start_date: null,
            end_date: null,
            user: '',
            action: undefined,
            model_name: '',
            level: undefined,
            message: '',
            changed_by: '',
            field_name: '',
        });
        fetchLogs(1, pagination.pageSize, {});
    };

    // Column definitions
    const activityColumns = [
        {
            title: 'Timestamp',
            dataIndex: 'timestamp',
            render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: 'User',
            dataIndex: 'user',
        },
        {
            title: 'Action',
            dataIndex: 'action',
            render: (action: string) => (
                <Tag color={
                    action === 'CREATE' ? 'green' :
                    action === 'UPDATE' ? 'blue' :
                    'red'
                }>
                    {action}
                </Tag>
            ),
        },
        {
            title: 'Model',
            dataIndex: 'model_name',
        },
        {
            title: 'Record ID',
            dataIndex: 'record_id',
        },
        {
            title: 'Changes',
            dataIndex: 'changes',
            render: (changes: string) => (
                <Typography.Text ellipsis={{ tooltip: changes }}>
                    {changes}
                </Typography.Text>
            ),
        },
        {
            title: 'IP Address',
            dataIndex: 'ip_address',
        },
    ];

    const errorColumns = [
        {
            title: 'Timestamp',
            dataIndex: 'timestamp',
            render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: 'Level',
            dataIndex: 'level',
            render: (level: string) => (
                <Tag color={
                    level === 'ERROR' ? 'red' :
                    level === 'WARNING' ? 'orange' :
                    'blue'
                }>
                    {level}
                </Tag>
            ),
        },
        {
            title: 'Message',
            dataIndex: 'message',
            render: (message: string) => (
                <Typography.Text ellipsis={{ tooltip: message }}>
                    {message}
                </Typography.Text>
            ),
        },
        {
            title: 'User',
            dataIndex: 'user',
        },
        {
            title: 'IP Address',
            dataIndex: 'ip_address',
        },
    ];

    const changeColumns = [
        {
            title: 'Timestamp',
            dataIndex: 'timestamp',
            render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: 'changed_by',
            dataIndex: 'changed_by',
        },
        {
            title: 'Model',
            dataIndex: 'model_name',
        },
        {
            title: 'Record ID',
            dataIndex: 'record_id',
        },
        {
            title: 'Field',
            dataIndex: 'field_name',
        },
        {
            title: 'Old Value',
            dataIndex: 'old_value',
            render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                    {value}
                </Typography.Text>
            ),
        },
        {
            title: 'New Value',
            dataIndex: 'new_value',
            render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                    {value}
                </Typography.Text>
            ),
        },
        {
            title: 'Reason',
            dataIndex: 'reason',
        },
        {
            title: 'IP Address',
            dataIndex: 'ip_address',
        },
    ];

    const fetchLogs = async (page: number = 1, pageSize: number = 10, filterParams = filters) => {
        setLoading(true);
        try {
            const params = {
                page,
                pageSize,
                ...filterParams
            };

            let data;
            switch (activeTab) {
                case 'activity':
                    data = await logService.getActivityLogs(params);
                    setActivityLogs(data.results);
                    break;
                case 'error':
                    data = await logService.getErrorLogs(params);
                    setErrorLogs(data.results);
                    break;
                case 'changes':
                    data = await logService.getDataChangeLogs(params);
                    setDataChangeLogs(data.results);
                    break;
            }

            setPagination({
                ...pagination,
                current: page,
                pageSize: pageSize,
                total: data.count,
            });
        } catch (error) {
            console.error(`Error fetching ${activeTab} logs:`, error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchLogs(1);
    }, [activeTab]);

    const handleTableChange = (newPagination: TablePaginationConfig) => {
        fetchLogs(newPagination.current, newPagination.pageSize);
    };

    const baseItems: TabsProps['items'] = [
        {
            key: 'activity',
            label: 'Activity Logs',
            children: (
                <>
                    <LogFilters
                        type="activity"
                        filters={filters}
                        onFilterChange={handleFilterChange}
                        onReset={handleFilterReset}
                    />
                    {role?.name === "SuperAdmin" && (
                    <div className="mb-4 flex justify-end">
                        <Popconfirm
                            title="Clear Activity Logs"
                            description="Are you sure you want to delete all activity logs?"
                            onConfirm={handleDeleteLogs}
                            okText="Yes"
                            cancelText="No"
                            placement="left"
                        >
                            <Button type="primary" danger icon={<Iconify icon="mingcute:delete-2-fill" />}>
                                Clear Logs
                            </Button>
                        </Popconfirm>
                    </div>
                    )}
                    <Table
                        columns={activityColumns}
                        dataSource={activityLogs}
                        rowKey="id"
                        loading={loading}
                        pagination={{
                            ...pagination,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total) => `Total ${total} items`,
                        }}
                        onChange={handleTableChange}
                        bordered
                        className="logs-table"
                        size="middle"
                        scroll={{ x: 1150 }}
                        rowClassName={(_, index) => 
                            index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        }
                    />
                </>
            ),
        },
        {
            key: 'changes',
            label: 'Change Logs',
            children: (
                <>
                    <LogFilters
                        type="changes"
                        filters={filters}
                        onFilterChange={handleFilterChange}
                        onReset={handleFilterReset}
                    />
                    {role?.name === "SuperAdmin" && (
                    <div className="mb-4 flex justify-end">
                        <Popconfirm
                            title="Clear Change Logs"
                            description="Are you sure you want to delete all change logs?"
                            onConfirm={handleDeleteLogs}
                            okText="Yes"
                            cancelText="No"
                            placement="left"
                        >
                            <Button type="primary" danger icon={<Iconify icon="mingcute:delete-2-fill" />}>
                                Clear Logs
                            </Button>
                        </Popconfirm>
                    </div>
                    )}
                    <Table
                        columns={changeColumns}
                        dataSource={dataChangeLogs}
                        rowKey="id"
                        loading={loading}
                        pagination={{
                            ...pagination,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total) => `Total ${total} items`,
                        }}
                        onChange={handleTableChange}
                        bordered
                        className="logs-table"
                        size="middle"
                        scroll={{ x: 1150 }}
                        rowClassName={(_, index) => 
                            index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        }
                    />
                </>
            ),
        }
    ];

    const errorLogTab = {
        key: 'error',
        label: 'Error Logs',
        children: (
            <>
                <LogFilters
                    type="error"
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    onReset={handleFilterReset}
                />
                <div className="mb-4 flex justify-end">
                    <Popconfirm
                        title="Clear Error Logs"
                        description="Are you sure you want to delete all error logs?"
                        onConfirm={handleDeleteLogs}
                        okText="Yes"
                        cancelText="No"
                        placement="left"
                    >
                        <Button type="primary" danger icon={<Iconify icon="mingcute:delete-2-fill" />}>
                            Clear Logs
                        </Button>
                    </Popconfirm>
                </div>
                <Table
                    columns={errorColumns}
                    dataSource={errorLogs}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                        ...pagination,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `Total ${total} items`,
                    }}
                    onChange={handleTableChange}
                    bordered
                    className="logs-table"
                    size="middle"
                    scroll={{ x: 1150 }}
                    rowClassName={(_, index) => 
                        index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                    }
                />
            </>
        ),
    };

    const items = role?.name === "SuperAdmin" 
        ? [...baseItems, errorLogTab]
        : baseItems;

    return (
        <Card title="System Logs">
            <Tabs
                activeKey={activeTab}
                items={items}
                onChange={(key) => {
                    setActiveTab(key);
                    setPagination({ ...pagination, current: 1 });
                }}
            />
        </Card>
    );
};

export default LogsPage;
