import { Form, Input, Modal, Select } from "antd";
import { useEffect, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import orgService from "@/api/services/orgService";
import { Feeder } from "@/types/entity";

export type SubstationModalProps = {
    formValue: {
        id?: number;  // Add id field
        name: string;
        region?: string;
    };
    title: string;
    show: boolean;
    onOk: () => void;
    onCancel: () => void;
};

export function SubstationModal({ title, show, formValue, onOk, onCancel }: SubstationModalProps) {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    // Fetch regions
    const { data: regions = [] } = useQuery({
        queryKey: ["regions"],
        queryFn: orgService.getRegionOnly,
    });

    useEffect(() => {
        form.setFieldsValue(formValue);
    }, [formValue, form]);

    const substationMutation = useMutation({
        mutationFn: orgService.createSubstation,
        onSuccess: () => {
            toast.success("Substation created successfully!");
            onOk();
            form.resetFields();
        },
        onError: (error: any) => {
            toast.error(error.message || "Failed to create substation");
        },
    });

    const handleOk = () => {
        form.validateFields()
            .then(async (values) => {
                setLoading(true);
                try {
                    if (title === "Create Substation") {
                        await substationMutation.mutateAsync(values);
                    } else {
                        // Use ID instead of name for updates
                        await orgService.updateSubstation(formValue.id!, values);
                        toast.success("Substation updated successfully");
                        onOk();
                    }
                } catch (error: any) {
                    toast.error(error.message || "Operation failed");
                } finally {
                    setLoading(false);
                }
            })
            .catch((info) => {
                console.log('Validate Failed:', info);
            });
    };

    return (
        <Modal
            title={title}
            open={show}
            onOk={handleOk}
            onCancel={onCancel}
            confirmLoading={loading}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={formValue}
            >
                <Form.Item
                    name="region"
                    label="Region"
                    rules={[{ required: true, message: 'Please select a region!' }]}
                >
                    <Select
                        showSearch
                        placeholder="Select a region"
                        filterOption={(input, option) =>
                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                        options={regions.map((region: any) => ({
                            value: region.csc_code,
                            label: region.name,
                        }))}
                    />
                </Form.Item>

                <Form.Item
                    name="name"
                    label="Substation Name"
                    rules={[{ required: true, message: 'Please enter the substation name!' }]}
                >
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    );
}



