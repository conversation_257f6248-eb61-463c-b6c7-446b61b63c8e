import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Zap,
  MapPin,
  Settings,
  Activity,
  TrendingUp,
  Search,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Shield,
  Gauge,
  Battery,
  Power,
  Factory,
  Loader2,
  RefreshCw,
  Download
} from 'lucide-react';
import transformerService from '../../../api/services/transformerService';
import orgService from '@/api/services/orgService';
import { getItem, setItem } from '../../../utils/storage';

// Cache configuration
// const CACHE_KEY = 'dashboard_statistics_cache';
// const REGIONS_CACHE_KEY = 'regions_cache';
// const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds
//   basestations: {
//         total: 12426,
//         byType: {
//             "Double Concrete Pole": 2211,
//             "Double Steel Pole": 102,
//             "Double Wooden Pole": 7188,
//             "Ground Seat Foundation Elevated": 1142,
//             "Ground Seat Foundation Ground Level": 832,
//             "Net Station": 562,
//             "Quadruple Concrete Pole": 11,
//             "Quadruple Steel Pole": 87,
//             "Quadruple Wooden Pole": 22,
//             "Single Concrete Pole": 188,
//             "Single Steel Pole": 2,
//             "Single Wooden Pole": 14,
//             "Triple Concrete Pole": 10,
//             "Triple Steel Pole": 4,
//             "Triple Wooden Pole": 51
//         },
//         byRegion: {
//             "ADAMA REGION": 1679,
//             "AFAR REGION": 718,
//             "AMBO REGION": 131,
//             "ARBAMINCH REGION": 510,
//             "B/GUMZ REGION": 231,
//             "BAHIRDAR REGION": 571,
//             "BALE ROBE REGION": 122,
//             "CHIRO REGION": 265,
//             "Centeral Ethiopia Region": 230,
//             "D/BIRHAN REGION": 291,
//             "D/MARKOS REGION": 554,
//             "DESSIE REGION": 324,
//             "DIRE DAWA REGION": 111,
//             "EAAR": 29,
//             "FINFINE REGION": 7,
//             "GAMBELLA REGION": 226,
//             "GONDER REGION": 303,
//             "HARAR REGION": 291,
//             "JIJIGA REGION": 326,
//             "JIMMA REGION": 1694,
//             "MEKELE REGION": 909,
//             "METU REGION": 86,
//             "NAAR": 12,
//             "NEKEMT REGION": 559,
//             "SAAR": 202,
//             "SHIRE REGION": 8,
//             "SIDAMA REGION": 790,
//             "SOUTH WEST REGION": 298,
//             "W/SODO REGION": 668,
//             "WAAR": 64,
//             "WOLDIYA REGION": 217
//         },
//         byCSC: {
//             "ABALA CSC": 40,
//             "ADABA CSC": 1,
//             "ADAMA CSC. NO.1": 407,
//             "ADAMA CSC. NO.2": 693,
//             "ADAMA CSC. NO.3": 202,
//             "ADAMA CSC. NO.4": 200,
//             "ADARIKAYE CSC": 21,
//             "ADIHAGERAY CSC": 6,
//             "AFDERA CSC": 8,
//             "AGARFA CSC": 14,
//             "AKESTA CSC": 25,
//             "ALABA": 103,
//             "ALBKO CSC": 10,
//             "ALETA WONDO CSC": 183,
//             "AMANUEL CSC": 1,
//             "AMBA GIORGIES CSC": 21,
//             "AMBO CSC": 131,
//             "ARBAMINCH NO. 1 CSC": 171,
//             "ARBAMINCH NO. 2 CSC": 91,
//             "ARBEGONA CSC": 7,
//             "AREKA": 58,
//             "ARERTI CSC": 17,
//             "ARJO GUDITU": 34,
//             "ASAYTA CSC": 67,
//             "ASSOSA CSC": 231,
//             "ATAYE CSC": 20,
//             "AWASH 7 KILL O CSC": 134,
//             "AWBERE CSC": 2,
//             "BAHERDAR NO.3(ABAYEMADO)CSC": 318,
//             "BAHIR DAR 1 CSC": 52,
//             "BAHIR DAR 2 CSC": 201,
//             "BALE GOBA CSC": 14,
//             "BALE ROBE CSC": 12,
//             "BATI CSC": 66,
//             "BEDESA": 24,
//             "BEDESSA CSC": 12,
//             "BERHALE CSC": 24,
//             "BICHENA CSC": 33,
//             "BISTIMA CSC": 2,
//             "BODITI": 77,
//             "BONGA CSC": 143,
//             "BURAYU CSC": 2,
//             "BURIE CSC": 34,
//             "CHEFA ROBIT CSC": 1,
//             "CHENCHA CSC": 72,
//             "CHIFRA CSC": 29,
//             "CHIRI CSC": 11,
//             "CHIRO CSC": 79,
//             "CHUKO CSC": 49,
//             "DABAT CSC": 39,
//             "DALOCHA": 67,
//             "DAWENT CSC": 6,
//             "DE JEN CSC": 94,
//             "DEBARK CSC": 94,
//             "DEBERE BIRHAN NO 1 CSC": 190,
//             "DEBERE BIRHAN NO 2 CSC": 21,
//             "DEBEREMARKOS NO. 1 CSC": 27,
//             "DEBEREMARKOS NO. 2 CSC": 32,
//             "DEBRELIAS CSC": 1,
//             "DEBREWORK CSC": 6,
//             "DECHETO CSC": 15,
//             "DEDER CSC": 14,
//             "DEGEHABUR CSC": 1,
//             "DELOMENA CSC": 4,
//             "DENBECHA CSC": 38,
//             "DENSA CSC": 9,
//             "DESSIE 1 CSC": 60,
//             "DESSIE 2 CSC": 6,
//             "DESSIE 3 CSC": 1,
//             "DIMMA CSC": 66,
//             "DINSHO CSC": 26,
//             "DIRE DAWA 1": 8,
//             "DIRE DAWA 2": 103,
//             "DITA CSC": 22,
//             "DODOLA CSC": 14,
//             "DUBTI CSC": 23,
//             "EAAR CSC NO.3": 2,
//             "EAAR CSC NO.5": 8,
//             "EAAR CSC NO.6": 2,
//             "EAAR CSC NO.9": 17,
//             "ELIWUHA CSC": 19,
//             "ENEWARI CSC": 2,
//             "F/SELAM CSC": 84,
//             "FINCHA": 11,
//             "GAMBELLA CSC": 20,
//             "GASERA CSC": 12,
//             "GASHENA CSC": 2,
//             "GELAN CSC": 5,
//             "GEWANE CSC": 33,
//             "GIDOLE/DERASHE CSC": 1,
//             "GIMBI": 32,
//             "GIMBICHU": 16,
//             "GIMBO CSC": 142,
//             "GINDEWOYIN CSC": 37,
//             "GINIRE CSC": 4,
//             "GIRJA CSC": 2,
//             "GONDER 1 CSC": 17,
//             "GONDER 2 CSC": 1,
//             "GONDER D-CHUAHI T CSC": 17,
//             "GORO CSC": 10,
//             "GRESEBONKE CSC": 37,
//             "H/SELAME CSC": 31,
//             "HAIK CSC": 2,
//             "HARA DUMELE CSC": 6,
//             "HARARE NO.1 CSC": 130,
//             "HARARE NO.2 CSC": 161,
//             "HARBU CSC": 10,
//             "HAROMAYA CSC": 41,
//             "HAWASSA NO.1 CSC": 99,
//             "HAWASSA NO.2 CSC": 86,
//             "HAWASSA NO.3 CSC": 140,
//             "HAWASSA NO.4 (INDUSTRY)": 58,
//             "HIRNA CSC": 41,
//             "HOSSAIENA CSCS-1": 62,
//             "HOSSAIENA CSCS-2": 3,
//             "HUMBO": 13,
//             "JAMMA CSC": 10,
//             "JIJIGA CSCNO.1": 26,
//             "JIJIGA CSCNO.2": 297,
//             "JIMMA CSC NO. 1": 322,
//             "JIMMA CSC NO.2": 1372,
//             "JINKA CSC": 18,
//             "KARAMILE CSC": 50,
//             "KEBADO CSC": 11,
//             "KELEWAN CSC": 24,
//             "KEMISSE CSC": 16,
//             "KENBA CSC": 32,
//             "KERSSA CSC": 28,
//             "KOBO CSC": 1,
//             "KOLLADIBA CSC": 36,
//             "KOMBOLCHANO.1 CSC": 2,
//             "KOMBOLCHANO.2 CSC": 8,
//             "KUMI CSC": 43,
//             "KUNEBA CSC": 12,
//             "KUTABER CSC": 2,
//             "KUYE CSC": 45,
//             "LEGEHIDA CSC": 10,
//             "LOGIA CSC": 94,
//             "LUMMAME CSC": 51,
//             "MAICHEW": 221,
//             "MEKANE BIRHAN CSC": 25,
//             "MEKANESELAM CSC": 5,
//             "MEKELLE S.C.1": 248,
//             "MEKELLE S.C.2": 266,
//             "MEKELLE S.C.4 (INDUSTRY PARK)": 42,
//             "MEKONI": 43,
//             "MEKOY CSC": 10,
//             "MELKA WERER CSC": 31,
//             "METOLMARIAM CSC": 19,
//             "METTI CSC": 97,
//             "METU CSC": 86,
//             "MIERABABAY CSC": 66,
//             "MILE CSC": 27,
//             "MIZAN CSC": 1,
//             "MODJO": 177,
//             "MOTA CSC": 20,
//             "MUJA CSC": 5,
//             "NAAR CSC NO.1": 2,
//             "NAAR CSC NO.2": 1,
//             "NAAR CSC NO.3": 1,
//             "NAAR CSC NO.4": 8,
//             "NEKEMT NO1": 367,
//             "NEKEMT NO2": 115,
//             "SAAR CSC NO.6": 1,
//             "SAAR CSC NO.7": 1,
//             "SAAR CSC NO.9": 199,
//             "SAINT AJIBAR CSC": 15,
//             "SEKOTA CSC": 12,
//             "SEMEMA CSC": 2,
//             "SEMERA CSC": 115,
//             "SHEBEDINO/LAKU CSC": 30,
//             "SHENDI CSC": 9,
//             "SHOAROBIT CSC": 41,
//             "SHONE": 51,
//             "SUNULITA CSC": 11,
//             "TEKELDINGAYE CSC": 32,
//             "TELALAK CSC": 12,
//             "TENTA CSC": 32,
//             "TEPPI CSC": 1,
//             "TORA": 71,
//             "TULUDIMTU CUSTOMER CSC": 1,
//             "WAAD CSC NO 7 (NEW)": 22,
//             "WAAD CSC NO 8 (NEW)": 40,
//             "WAAR CSC NO.3": 1,
//             "WEGDI CSC": 1,
//             "WEGL TENA CSC": 8,
//             "WELAYITA SODO NO.1": 153,
//             "WELAYITA SODO NO.2": 189,
//             "WENDOGENET CSC": 34,
//             "WERKA CSC": 5,
//             "WEST AA CSC NO.1": 1,
//             "WOLDIYA CSC 2": 63,
//             "WOLEDIYA CSC": 124,
//             "WORABE": 11,
//             "WUCHALE CSC": 13,
//             "WUKRO": 89,
//             "YEDUHA CSC": 21,
//             "YEJUBE CSC": 2,
//             "YIRBA CSC": 24,
//             "YIRGALME CSC": 36,
//             "ZEQUALA CSC": 4
//         },
//         bySubstation: {
//             "A/minch": 485,
//             "A/minch ": 4,
//             "ADDIS CENTER": 64,
//             "ADDIS EAST": 1,
//             "ADDIS NORTH": 9,
//             "ADDIS WEST": 1,
//             "ADDISU GEBEYA": 2,
//             "AKAKI": 1,
//             "ASSOSA": 231,
//             "Adama": 1217,
//             "Adama New": 214,
//             "Akesta": 107,
//             "Alamata": 1,
//             "Amibara": 72,
//             "Amibara ": 4,
//             "Asebe Teferi -2": 122,
//             "Awash": 54,
//             "Awash 7 kilo substation": 122,
//             "Azezo": 61,
//             "Bahir Dar-I": 80,
//             "Bahir Dar-II": 491,
//             "Bedesa": 11,
//             "Bonga": 296,
//             "COTOBIE": 2,
//             "Chelenko": 88,
//             "Combolcha I": 82,
//             "Combolcha II": 4,
//             "D/brehan shewarobit": 61,
//             "DIDESSA": 44,
//             "Dabat": 186,
//             "Debre_Birhan": 141,
//             "Debre_Birhan_2": 72,
//             "Dessie": 96,
//             "Dicheto": 15,
//             "Dire Dawa substation 1": 93,
//             "Dire Dawa substation 3": 3,
//             "Dorogbir": 187,
//             "FINCHA": 11,
//             "GHIMBI": 33,
//             "Gambella1": 20,
//             "Gashena": 16,
//             "Gefersa": 1,
//             "Gelan": 5,
//             "Ginchi": 2,
//             "Guder Mobile": 125,
//             "Guder Mobile ": 4,
//             "HARAR-3": 15,
//             "HARAR-3 ": 1,
//             "Halaba substation": 154,
//             "Hara mobile": 64,
//             "Haremaya": 28,
//             "Hawassa No. 1": 315,
//             "Hawassa No. 2": 141,
//             "Hossana substation": 81,
//             "Jimma-1": 1611,
//             "Jimma-2": 83,
//             "KALITI": 15,
//             "KALITI-N": 185,
//             "KOYE ABO": 1,
//             "Kemise": 12,
//             "Kemissie": 27,
//             "Key Afer": 18,
//             "Koka Haydro Power": 17,
//             "Kombolcha": 10,
//             "LEGETAFO": 1,
//             "Lalibela": 5,
//             "Lideta": 42,
//             "Maychew": 221,
//             "Mekele": 596,
//             "Mekhoni": 43,
//             "Melkawakena": 20,
//             "Mettu 230kv": 86,
//             "Mizan": 2,
//             "Mizan_Teferi": 66,
//             "Mojo": 17,
//             "Mojo Mobaile": 114,
//             "Mojo New": 62,
//             "Mojo old": 1,
//             "Nekemte": 471,
//             "Rayitu": 4,
//             "Robe Substation": 94,
//             "Sawla": 3,
//             "Sekota": 16,
//             "Shegole": 1,
//             "Shire": 14,
//             "Tepi": 140,
//             "WEREGENU": 25,
//             "WUKRO": 89,
//             "Warabe substation": 85,
//             "Wolayita _Sodo_Old": 232,
//             "Wolayita_Soddo_New": 282,
//             "Wukero": 36,
//             "Yirgalem 1": 66,
//             "Yirgalem 2 /Wara/": 266,
//             "adami tullu substation": 64,
//             "afdera": 8,
//             "axum": 2,
//             "bichena": 212,
//             "d/markos": 101,
//             "f/selam": 165,
//             "harar substation -II": 182,
//             "harar substation -III": 109,
//             "jigjiga": 326,
//             "mota": 76,
//             "semera  substation": 335,
//             "shakisso": 2,
//             "shire": 6,
//             "waesela substation": 15,
//             "yadot": 4
//         },
//         byFeeder: {
//             " CHIRO/15": 2,
//             " Sikela Feder": 1,
//             "(R3-G6) INCOMING (15KV)": 2,
//             "A/giorgis": 23,
//             "ADC-11": 1,
//             "ADC-15": 9,
//             "ADC-4": 53,
//             "ADC-7": 1,
//             "ADE-1": 1,
//             "ADG-3": 2,
//             "ADW-5": 1,
//             "AKA-1": 1,
//             "ANBESA BEER": 2,
//             "AND-2": 1,
//             "AND-3": 1,
//             "AND-6": 7,
//             "ARJO GUDETU": 35,
//             "AWADAY": 16,
//             "Abala Longena F-2": 5,
//             "Abasem": 1,
//             "Abay_Mado_15kV": 152,
//             "Adaba Dodola": 3,
//             "Adama Water": 3,
//             "Adego Line-1 -15kV": 65,
//             "Adet_15kV": 1,
//             "Agansa RMU (F3)": 21,
//             "Agarfa": 27,
//             "Akasta /Line-4/33kv": 5,
//             "Amanuael": 2,
//             "Amba 14": 94,
//             "Ambo New": 52,
//             "Ambo Town": 55,
//             "Ambo Water": 22,
//             "Angereb": 1,
//             "Ankober_15_KV": 67,
//             "Areka F-1": 64,
//             "Arera   Line-1": 1,
//             "Arroressa line": 2,
//             "Asayta": 71,
//             "Assosa ketema feeder": 88,
//             "Assosa university": 8,
//             "Ataye 15kv": 3,
//             "Ataye 33kv": 16,
//             "Awash 40": 65,
//             "Awash7": 48,
//             "Azezo": 2,
//             "BATI/15": 7,
//             "BEDESA/15": 7,
//             "Bambasi Feeder": 15,
//             "Baro_Mado_15_KV": 9,
//             "Bata_15kV": 3,
//             "Bati Line-1 33kv": 4,
//             "Bati Line-2 15kv": 62,
//             "Bati/Bureka": 10,
//             "Bechefa": 118,
//             "Bedesa Bilate F-3": 15,
//             "Bele  F-2": 50,
//             "Bele_Sorto F-4": 7,
//             "Bete_mengist_15kV": 24,
//             "Bichena": 30,
//             "Bilate_ F-5": 41,
//             "Birsheleko": 6,
//             "Blanket_Factory_15_KV": 22,
//             "Boditi F-4": 105,
//             "Bonga ( 15 KV )": 85,
//             "Boroda Feder": 98,
//             "Burie": 42,
//             "Butajira 15kv": 26,
//             "CHELENKO LOLA/33": 8,
//             "CHELENKO/15": 35,
//             "CHIRO/15": 44,
//             "CITY (R2-G1 ,15KV)  INCOMING": 5,
//             "COT-7": 2,
//             "Chencha Feder": 83,
//             "Chifra-Roba-Line3": 1,
//             "Chiri ( 15 KV )": 14,
//             "D/markos 3": 35,
//             "D/markos 4": 19,
//             "DEDER/15": 15,
//             "DLF-03": 20,
//             "DLF_1": 1,
//             "Dabat": 29,
//             "Debark": 90,
//             "Debark university/dedicated": 10,
//             "Debresina 15kv": 7,
//             "Debrework": 28,
//             "Dejen": 25,
//             "Delgi": 1,
//             "Denebecha": 41,
//             "Dessie Line-1 -15kV": 20,
//             "Dessie Line-2 -15kV": 56,
//             "Dicheto": 14,
//             "Dima": 66,
//             "Dire Dawa motor": 3,
//             "Dire Dawa water": 4,
//             "Dire steel": 8,
//             "Dodola": 17,
//             "Dubti": 23,
//             "Durame F-3": 52,
//             "F 1 / R 1": 2,
//             "F 3 / R 3": 16,
//             "F 4 / chineksene": 2,
//             "F 6 ( Wendogenet)": 34,
//             "F- 4": 33,
//             "F-1 1 /jijiga": 17,
//             "F-2": 250,
//             "F-2 (wechale)": 2,
//             "F-3 jijiga Kilil": 14,
//             "F-3 kebribayah": 2,
//             "F/Selam": 44,
//             "F1/R6": 34,
//             "F2/R3": 34,
//             "F3/R2": 46,
//             "F4/R1": 86,
//             "F5/R5": 14,
//             "F6/R4": 63,
//             "F8/R7": 54,
//             "FINCHA": 10,
//             "Fafen F-1": 6,
//             "Feeder 1/Chuko and kebado": 11,
//             "Feeder 1/Leku": 22,
//             "Feeder 11": 5,
//             "Feeder 12": 2,
//             "Feeder 2/ Chuko & Kebado": 43,
//             "Feeder 2/Aleta wendo& Hagere selam": 212,
//             "Feeder 2/yirgalem": 36,
//             "Feeder 4/ daye": 2,
//             "Feeder 5/ FURA CHANCHO": 2,
//             "Feeder 5/fura chancho": 4,
//             "Feeder 9": 2,
//             "FelegeBirhan": 14,
//             "Feresbet": 1,
//             "GENJI": 1,
//             "GHIMBI": 32,
//             "Gassera": 25,
//             "Gebeya_15_KV": 11,
//             "Gecha (15kV)": 1,
//             "Gesuba F-1": 95,
//             "Gewane": 34,
//             "Ghion_15kV": 167,
//             "Gidole  Feder": 17,
//             "Gimbo ( 33 KV )": 145,
//             "Ginchi": 2,
//             "Ginnir": 4,
//             "Goba": 17,
//             "Gondar ber Line-4 -15kV": 55,
//             "Gondar wuha/dedicated": 1,
//             "Gorgora": 2,
//             "HAKA/33": 6,
//             "HAREMYA/15": 13,
//             "HIRNA/15": 53,
//             "HO2_Debre_Berhan_33KV": 26,
//             "HO3_Enewari_33KV": 2,
//             "Halaba F-2": 75,
//             "Halaba_Alem Gebeya F-4": 27,
//             "Hamusit_15kV": 56,
//             "Harar old Three (HR2-F3)": 74,
//             "Harar old four (HR2-F4)": 52,
//             "Harar old six (HR2-F6) dire teyara": 1,
//             "Harar old two (HR2-F2)": 55,
//             "Harbu University Line-4 15KV": 10,
//             "INDUSTRY  ANTENXS": 54,
//             "INDUSTRY PARK (33KV)": 15,
//             "Industry": 7,
//             "JAJA/33": 3,
//             "JU(L4)  (15KV)": 11,
//             "Janamora": 34,
//             "K 2": 44,
//             "K-1": 12,
//             "K-1$3": 43,
//             "K-12": 46,
//             "K-4": 69,
//             "K000": 1,
//             "K02": 3,
//             "K03": 57,
//             "K04": 244,
//             "K05": 16,
//             "K06": 163,
//             "K10": 13,
//             "K12 date center  hebi 15kv": 4,
//             "K13_Zamu pls_15kv": 3,
//             "K14 data center  hebi 15kv": 2,
//             "K2_Debre_Berhan_15KV": 35,
//             "KAL.GIS-6": 15,
//             "KAL.N-K4": 142,
//             "KAL.N-K6": 43,
//             "KERSA/15": 27,
//             "KETO FURIDESA15KV)": 13,
//             "KO3": 2,
//             "KOMBOLCHA": 1,
//             "KOY-4": 1,
//             "Kanba Feder": 70,
//             "Kemissie-Line1": 14,
//             "Ko6 - 15kv": 1,
//             "Kombolicha Line-1 15 kv": 8,
//             "Kombolicha Line-3 15kv": 2,
//             "Kone-Line3": 16,
//             "Kunba/atibe H3": 12,
//             "Kuy": 45,
//             "L-2": 3,
//             "L-3": 32,
//             "L-5": 8,
//             "L1-Angacha feeder": 13,
//             "L1-Balessa feeder": 5,
//             "L2-Gimbichu feeder": 16,
//             "L4-Hossana 15kv feeder": 22,
//             "L5-Fonko 15 feeder": 3,
//             "L6-Univeersity": 11,
//             "L7-Heto": 11,
//             "LIMMU(33KV)": 169,
//             "LINE 1/15KV": 58,
//             "LINE 1/33KV": 2,
//             "LINE-4": 1,
//             "LINE1": 216,
//             "LINE2": 54,
//             "LINE3": 88,
//             "LINE4": 103,
//             "LINE5": 12,
//             "LINE6": 287,
//             "LINE7": 46,
//             "Line-1": 6,
//             "Line-11": 1,
//             "Line-4": 17,
//             "Line-5": 1,
//             "Logya": 100,
//             "Lumame": 41,
//             "M/wash": 1,
//             "MESELA/33": 14,
//             "MIEASO/15": 4,
//             "MOBILE1": 158,
//             "MOBILE2": 103,
//             "MOBILE3": 76,
//             "MOBILE4": 68,
//             "MOBILE5": 112,
//             "MOBILE6": 8,
//             "MSA_15kV": 1,
//             "Maryam RMU(F4)": 2,
//             "Maytsbri": 14,
//             "Mechare -33Kv": 30,
//             "Mekane Selam /Line-1/ 33kV": 6,
//             "Mekoy-Line4": 12,
//             "Meliyu": 16,
//             "Melka werer": 17,
//             "Melkasedi": 10,
//             "Mendida_15_KV": 50,
//             "Mera Tello ( 33 KV": 7,
//             "Mera Tello ( 33 KV ": 1,
//             "Mertolemariam": 40,
//             "Merye 15kv": 1,
//             "Mesmer 11": 4,
//             "Mesmer 12": 28,
//             "Mesmer 7": 2,
//             "Mesmer 9": 7,
//             "Mettu Town Feeder": 68,
//             "Mettu University": 18,
//             "Midroc": 48,
//             "Mille": 36,
//             "Mizan ( 15 KV )": 1,
//             "Motta": 22,
//             "Muja/Line-3 -15kV": 5,
//             "ODA BULTUM  /15": 5,
//             "OLD CITY (15KV)": 9,
//             "P4": 6,
//             "Palsa": 14,
//             "Papyrus_15kV": 167,
//             "Piasa Line-3 -15kV": 37,
//             "Quarit": 30,
//             "R1-G3(SEKA)": 6,
//             "R1-G4(MERKATO)": 20,
//             "R1-G5(SARIS)": 38,
//             "R2": 15,
//             "R2-G2(DEDO)": 29,
//             "R2-G3(MUZIEM)": 1,
//             "R2-G4(YETABABARUT)": 1306,
//             "R3": 127,
//             "R3-G2(KOCI)": 33,
//             "R3-L4": 8,
//             "R4": 77,
//             "R5": 149,
//             "Robit 15kv": 34,
//             "Robit Line-1 -15kV": 5,
//             "Robit Line-2 -15kV": 14,
//             "SERBO   (15KV)": 29,
//             "SORORO/33": 4,
//             "Sabure": 15,
//             "Sahila -33kV": 4,
//             "Sanja": 5,
//             "Sayint/Line-5/ 33KV": 24,
//             "Secha Feder": 82,
//             "Sekota /Asketema/woleh/ -15kV": 10,
//             "Semera": 105,
//             "Serdo": 1,
//             "Shelewasho": 38,
//             "Sikela Feder": 91,
//             "Sinana": 9,
//             "Sodo F-2": 51,
//             "Sodo F-5": 81,
//             "Sununta": 64,
//             "T/Dingay": 42,
//             "TOLE": 9,
//             "Tana": 49,
//             "Telalak": 12,
//             "Telima": 1,
//             "Tenta /Line-2/ 33kV": 42,
//             "Tepi_15_KV": 21,
//             "Tepi_66 kv": 1,
//             "Tik": 35,
//             "Tsemera -15 Kv": 2,
//             "Tuluawulia university/Line6/ 33KV": 3,
//             "Txtail Feder": 6,
//             "UNIVERSITY/15": 2,
//             "University ( 33 KV": 24,
//             "WER-8": 25,
//             "Waber": 1,
//             "Woreilu /Line-3/33kV": 27,
//             "Wushwush ( 15 KV": 18,
//             "Wushwush ( 15 KV ": 2,
//             "afdera": 8,
//             "arjo awuraja(k01)": 2,
//             "bake-jama(k03)": 268,
//             "boniya l-13": 2,
//             "dalocha feeder": 73,
//             "factory": 9,
//             "fedis": 13,
//             "feeder 1": 11,
//             "feeder 3": 52,
//             "feeder 4": 30,
//             "hospital(k04)": 171,
//             "jinka Feder": 18,
//             "ko-2": 20,
//             "kurmuk feeder": 26,
//             "line 1": 4,
//             "line 2": 1,
//             "line 3": 2,
//             "lugeda  h4": 24,
//             "new feeder five (HR3-F5)": 43,
//             "new feeder four (HR3-F4) harar beer": 23,
//             "new feeder two (HR3-F2) Bisidimo": 25,
//             "sawula Feder2": 3,
//             "seladingay 33kv": 2,
//             "tele": 3,
//             "uke": 5,
//             "universty Feder": 41,
//             "warabe feeder": 12,
//             "wollega(k06)": 20
//         }
//     },


//   transformers: {
//         total: 8919,
//         byType: {
//             "Compact": 284,
//             "Conservator": 4818,
//             "Hermatical": 3817
//         },
//         byCapacity: {
//             "00": 1,
//             "000000": 1,
//             "1": 1,
//             "10": 1,
//             "100": 1594,
//             "1000": 3,
//             "1250": 208,
//             "150": 1,
//             "1500": 4,
//             "160": 1,
//             "1600": 18,
//             "200": 1846,
//             "2000": 16,
//             "25": 849,
//             "2500": 8,
//             "2700": 3,
//             "300": 3,
//             "3000": 9,
//             "31": 1,
//             "315": 2279,
//             "3600": 2,
//             "400": 133,
//             "4000": 3,
//             "50": 1240,
//             "500": 149,
//             "5000": 8,
//             "6000": 1,
//             "63": 1,
//             "630": 368,
//             "700": 1,
//             "800": 165,
//             "invisible": 1
//         },
//         byPrimaryVoltage: {
//             "15": 7304,
//             "19": 8,
//             "33": 1607
//         },
//         byStatus: {
//             "Damaged": 117,
//             "Maintained": 1274,
//             "New": 7528
//         },
//         byManufacturer: {
//             "ABB Tanzania": 77,
//             "Abebebbbbbb": 1,
//             "Apex": 24,
//             "China Natinal Electric wire and cable Imp/Exp corporations": 537,
//             "Iran Transformer": 116,
//             "Kobera": 56,
//             "Koncar": 81,
//             "METEC": 3588,
//             "Mar son's": 462,
//             "Minel Transformer": 13,
//             "Other": 3189,
//             "Pauwels": 49,
//             "Siemens": 1,
//             "Stromberg": 32,
//             "Vijai Electrical Ltd Hyderabad": 537,
//             "Zennaro": 156
//         },
//         byServiceType: {
//             "Dedicated": 4105,
//             "Public": 4814
//         },
//         byCoolingType: {
//             "Dry Type": 75,
//             "ONAN": 8844
//         },
//         byVectorGroup: {
//             "DY1": 147,
//             "DY11": 662,
//             "DY5": 7143,
//             "Other": 967
//         },
//         byYearOfManufacturing: {
//             "2021-2025": 1369,
//             "2016-2020": 2005,
//             "2011-2015": 874,
//             "2006-2010": 3030,
//             "Before 2005": 1415
//         }
//     },

//   inspections: {
//         total: 7939,
//         byBodyCondition: {
//             "Fair": 538,
//             "Good": 7177,
//             "Poor": 224
//         },
//         byArrester: {
//             "Ok": 7194,
//             "all missed": 556,
//             "one missed": 144,
//             "two missed": 45
//         },
//         byDropOut: {
//             "Ok": 7417,
//             "all missed": 170,
//             "one missed": 278,
//             "two missed": 74
//         },
//         byFuseLink: {
//             "Ok": 4652,
//             "all missed": 2848,
//             "one missed": 283,
//             "two missed": 156
//         },
//         byMvBushing: {
//             "Ok": 7766,
//             "all missed": 138,
//             "one missed": 26,
//             "two missed": 9
//         },
//         byMvCableLug: {
//             "Ok": 3402,
//             "all missed": 4485,
//             "one missed": 38,
//             "two missed": 14
//         },
//         byLvBushing: {
//             "Ok": 7739,
//             "all missed": 151,
//             "one missed": 38,
//             "two missed": 11
//         },
//         byLvCableLug: {
//             "Ok": 5690,
//             "all missed": 2122,
//             "one missed": 75,
//             "two missed": 52
//         },
//         byOilLevel: {
//             "0.25": 360,
//             "0.5": 722,
//             "0.75": 3464,
//             "Full": 3393
//         },
//         byInsulationLevel: {
//             "null": 914,
//             "Acceptable": 6009,
//             "Not Acceptable": 1016
//         },
//         byHornGap: {
//             "Good": 7093,
//             "Poor": 846
//         },
//         bySilicaGel: {
//             "Fair": 1496,
//             "Good": 3486,
//             "Poor": 2957
//         },
//         byHasLinkage: {
//             "No": 6434,
//             "Yes": 1505
//         },
//         byArresterBodyGround: {
//             "Available": 6627,
//             "Not Available": 1312
//         },
//         byNeutralGround: {
//             "Available": 5299,
//             "Not Available": 2640
//         },
//         byStatusOfMounting: {
//             "Fair": 1297,
//             "Good": 6139,
//             "Poor": 503
//         },
//         byMountingCondition: {
//             "Fair": 1338,
//             "Good": 6098,
//             "Poor": 503
//         },
//         byVoltageRanges: {
//             "220-240V": 17,
//             "200-220V": 5,
//             "180-200V": 12,
//             "Below 180V": 158,
//             "Above 240V": 7738
//         },
//         byLoadCurrentRanges: {
//             "0-50A": 6814,
//             "50-100A": 791,
//             "100-150A": 213,
//             "150-200A": 55,
//             "Above 200A": 66
//         },
//         byVoltageUnbalanceRanges: {
//             "0-2%": 6815,
//             "2-5%": 657,
//             "5-10%": 91,
//             "Above 10%": 260
//         },
//         byTransformerLoadRanges: {
//             "0-25%": 4608,
//             "25-50%": 1660,
//             "50-75%": 583,
//             "75-100%": 174,
//             "Above 100%": 172
//         }
//     },


//   lvFeeders: {
//         total: 12345,
//         byRFuseRating: {
//             "0": 21,
//             "1": 2,
//             "1.2": 1,
//             "100": 1214,
//             "1000": 3,
//             "1200": 5,
//             "125": 3,
//             "13": 1,
//             "150": 26,
//             "16": 1,
//             "160": 1196,
//             "163": 4,
//             "200": 1717,
//             "210": 1,
//             "25": 1,
//             "250": 3253,
//             "29": 1,
//             "30": 1,
//             "300": 1033,
//             "308": 1,
//             "315": 45,
//             "35": 1,
//             "350": 777,
//             "355": 1,
//             "400": 762,
//             "45": 1,
//             "450": 3,
//             "5": 2,
//             "50": 3,
//             "500": 51,
//             "6": 2,
//             "60": 7,
//             "600": 49,
//             "63": 1130,
//             "630": 18,
//             "700": 1,
//             "8": 1,
//             "80": 264,
//             "800": 9,
//             "915": 1,
//             "99": 1,
//             "Direct": 349,
//             "other": 382
//         },
//         "bySFuseRating": {
//             "0": 22,
//             "1": 3,
//             "100": 1206,
//             "1000": 3,
//             "12": 1,
//             "1200": 5,
//             "125": 3,
//             "150": 32,
//             "16": 1,
//             "160": 1210,
//             "163": 4,
//             "2.9": 1,
//             "20": 1,
//             "200": 1660,
//             "250": 3258,
//             "258": 1,
//             "300": 1024,
//             "315": 41,
//             "350": 767,
//             "355": 2,
//             "40": 1,
//             "400": 766,
//             "450": 2,
//             "5": 1,
//             "50": 2,
//             "500": 50,
//             "6": 1,
//             "60": 7,
//             "600": 45,
//             "63": 1142,
//             "630": 18,
//             "8": 1,
//             "80": 260,
//             "800": 9,
//             "9": 1,
//             "915": 1,
//             "Direct": 391,
//             "other": 402
//         },
//         "byTFuseRating": {
//             "0": 22,
//             "0.9": 1,
//             "1": 2,
//             "100": 1206,
//             "1000": 3,
//             "1200": 5,
//             "125": 3,
//             "150": 32,
//             "16": 1,
//             "160": 1170,
//             "163": 3,
//             "200": 1642,
//             "208": 1,
//             "23": 1,
//             "250": 3194,
//             "258": 1,
//             "30": 1,
//             "300": 1093,
//             "308": 1,
//             "315": 42,
//             "350": 766,
//             "355": 4,
//             "358": 1,
//             "40": 1,
//             "400": 781,
//             "450": 2,
//             "5": 1,
//             "50": 2,
//             "500": 51,
//             "6": 1,
//             "60": 6,
//             "600": 42,
//             "63": 1108,
//             "630": 18,
//             "8": 1,
//             "80": 251,
//             "800": 10,
//             "9": 1,
//             "915": 1,
//             "Direct": 452,
//             "other": 421
//         },
//         "byLoadCurrentRanges": {
//             "0-50A": 6369,
//             "50-100A": 4683,
//             "100-150A": 1806,
//             "150-200A": 782,
//             "200-250A": 214,
//             "Above 250A": 306
//         },
//         "byCurrentUnbalanceRanges": {
//             "0-5%": 1845,
//             "5-10%": 437,
//             "10-15%": 463,
//             "15-20%": 455,
//             "Above 20%": 9036
//         },
//         "byTransformerLoadRanges": {
//             "0-25%": 10068,
//             "25-50%": 1690,
//             "50-75%": 277,
//             "75-100%": 89,
//             "Above 100%": 131
//         },
//         "byNeutralLoadRanges": {
//             "0-10%": 2151,
//             "10-20%": 879,
//             "20-30%": 829,
//             "30-40%": 623,
//             "Above 40%": 7764
//         },
//         "region_filter": null,
//       "timestamp": "2025-07-25T16:39:44.707590+00:00"
//     },
// };

interface AnimatedCounts {
  basestations?: number;
  transformers?: number;
  inspections?: number;
  lvFeeders?: number;
}

interface DashboardData {
  basestations: any;
  transformers: any;
  inspections: any;
  lvFeeders: any;
  region_filter: string | null;
  timestamp: string;
}

interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// Cache keys for dashboard data and regions
const CACHE_KEY = 'dashboard_statistics_cache';
const REGIONS_CACHE_KEY = 'regions_cache';
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds

// Helper function to sort data by value (descending)
const sortDataByValue = (data: Record<string, number>) => {
  return Object.entries(data)
    .sort(([, a], [, b]) => b - a)
    .reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {} as Record<string, number>);
};

function InspectionDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState<string>('');
  const [activeTab, setActiveTab] = useState('inspection');
  const [regions, setRegions] = useState<any[]>([]);
  const [animatedCounts, setAnimatedCounts] = useState<AnimatedCounts>({});
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: true,
    error: null,
    lastUpdated: null
  });

  // Check if cached data is still valid
  const isCacheValid = useCallback((cachedData: any) => {
    if (!cachedData || !cachedData.timestamp) return false;
    const cacheTime = new Date(cachedData.timestamp).getTime();
    const now = Date.now();
    return (now - cacheTime) < CACHE_DURATION;
  }, []);

  // Fetch regions from API with caching
  const fetchRegions = useCallback(async () => {
    try {
      // Check cache first
      const cachedRegions = getItem(REGIONS_CACHE_KEY as any);

      if (cachedRegions && isCacheValid(cachedRegions)) {
        setRegions((cachedRegions as any).data);
        return;
      }

      // Fetch fresh data
      const response = await orgService.getRegionOnly();

      if (response) {
        const regionsWithTimestamp = {
          data: response,
          timestamp: new Date().toISOString()
        };

        // Cache the data
        setItem(REGIONS_CACHE_KEY as any, regionsWithTimestamp);
        setRegions(response);
      }
    } catch (error) {
      console.error('Error fetching regions:', error);
      // Set empty array as fallback
      setRegions([]);
    }
  }, [isCacheValid]);

  // Fetch dashboard data from API
  const fetchDashboardData = useCallback(async (region?: string) => {
    try {
      setLoadingState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check cache first
      const cacheKey = region ? `${CACHE_KEY}_${region}` : CACHE_KEY;
      const cachedData = getItem(cacheKey as any);

      if (cachedData && isCacheValid(cachedData)) {
        setDashboardData(cachedData as DashboardData);
        setLoadingState({
          isLoading: false,
          error: null,
          lastUpdated: (cachedData as any).timestamp
        });
        return;
      }

      // Fetch fresh data
      const params = region ? { region } : {};
      const response = await transformerService.getDashbordStatistics(params);

      if (response) {
        const dataWithTimestamp = {
          ...response,
          timestamp: new Date().toISOString()
        };

        // Cache the data
        setItem(cacheKey as any, dataWithTimestamp);
        setDashboardData(dataWithTimestamp);
        setLoadingState({
          isLoading: false,
          error: null,
          lastUpdated: dataWithTimestamp.timestamp
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoadingState({
        isLoading: false,
        error: 'Failed to load dashboard data. Please try again.',
        lastUpdated: null
      });
    }
  }, [isCacheValid]);

  // Animate numbers when data changes
  const animateNumber = useCallback((target: number, key: string) => {
    let current = 0;
    const increment = target / 10;
    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      setAnimatedCounts(prev => ({ ...prev, [key]: Math.floor(current) }));
    }, 100);
  }, []);

  // Effect to fetch data on mount and when region changes
  useEffect(() => {
    fetchDashboardData(selectedRegion || undefined);
    fetchRegions(); // Fetch regions on component mount
  }, [fetchDashboardData, fetchRegions, selectedRegion]);

  // Effect to animate numbers when data is loaded
  useEffect(() => {
    if (dashboardData) {
      animateNumber(dashboardData.basestations?.total || 0, 'basestations');
      animateNumber(dashboardData.transformers?.total || 0, 'transformers');
      animateNumber(dashboardData.inspections?.total || 0, 'inspections');
      animateNumber(dashboardData.lvFeeders?.total || 0, 'lvFeeders');
    }
  }, [dashboardData, animateNumber]);

  // Helper function to get current data (API data or fallback to mock)
  const getCurrentData = useMemo(() => {
    if (dashboardData) {
      return {
        basestations: {
          ...dashboardData.basestations,
          byType: sortDataByValue(dashboardData.basestations?.byType || {}),
          byRegion: sortDataByValue(dashboardData.basestations?.byRegion || {}),
          byCSC: sortDataByValue(dashboardData.basestations?.byCSC || {}),
          bySubstation: sortDataByValue(dashboardData.basestations?.bySubstation || {}),
          byFeeder: sortDataByValue(dashboardData.basestations?.byFeeder || {})
        },
        transformers: {
          ...dashboardData.transformers,
          byType: sortDataByValue(dashboardData.transformers?.byType || {}),
          byCapacity: sortDataByValue(dashboardData.transformers?.byCapacity || {}),
          byPrimaryVoltage: sortDataByValue(dashboardData.transformers?.byPrimaryVoltage || {}),
          byManufacturer: sortDataByValue(dashboardData.transformers?.byManufacturer || {}),
          byVectorGroup: sortDataByValue(dashboardData.transformers?.byVectorGroup || {}),
          byStatus: sortDataByValue(dashboardData.transformers?.byStatus || {})
        },
        inspections: {
          ...dashboardData.inspections,
          byVoltageRanges: sortDataByValue(dashboardData.inspections?.byVoltageRanges || {}),
          byLoadCurrentRanges: sortDataByValue(dashboardData.inspections?.byLoadCurrentRanges || {}),
          byVoltageUnbalanceRanges: sortDataByValue(dashboardData.inspections?.byVoltageUnbalanceRanges || {}),
          byTransformerLoadRanges: sortDataByValue(dashboardData.inspections?.byTransformerLoadRanges || {}),
          byBodyCondition: sortDataByValue(dashboardData.inspections?.byBodyCondition || {}),
          byHornGap: sortDataByValue(dashboardData.inspections?.byHornGap || {}),
          bySilicaGel: sortDataByValue(dashboardData.inspections?.bySilicaGel || {}),
          byInsulationLevel: sortDataByValue(dashboardData.inspections?.byInsulationLevel || {}),
          byArresterBodyGround: sortDataByValue(dashboardData.inspections?.byArresterBodyGround || {}),
          byNeutralGround: sortDataByValue(dashboardData.inspections?.byNeutralGround || {}),
          byArrester: sortDataByValue(dashboardData.inspections?.byArrester || {}),
          byOilLevel: sortDataByValue(dashboardData.inspections?.byOilLevel || {})
        },
        lvFeeders: {
          ...dashboardData.lvFeeders,
          byRFuseRating: sortDataByValue(dashboardData.lvFeeders?.byRFuseRating || {}),
          byLoadCurrentRanges: sortDataByValue(dashboardData.lvFeeders?.byLoadCurrentRanges || {}),
          byCurrentUnbalanceRanges: sortDataByValue(dashboardData.lvFeeders?.byCurrentUnbalanceRanges || {}),
          byTransformerLoadRanges: sortDataByValue(dashboardData.lvFeeders?.byTransformerLoadRanges || {}),
          byNeutralLoadRanges: sortDataByValue(dashboardData.lvFeeders?.byNeutralLoadRanges || {})
        }
      };
    }
    return {
    basestations: {
      total: 0,
      byType: {},
      byRegion: {},
      byCSC: {},
      bySubstation: {},
      byFeeder: {}
    },
    transformers: {
      total: 0,
      byType: {},
      byCapacity: {},
      byPrimaryVoltage: {},
      byManufacturer: {},
      byVectorGroup: {},
      byStatus: {},
      byServiceType: {},
      byCoolingType: {},
      byYearOfManufacturing: {}
    },
    inspections: {
      total: 0,
      byVoltageRanges: {},
      byLoadCurrentRanges: {},
      byVoltageUnbalanceRanges: {},
      byTransformerLoadRanges: {},
      byBodyCondition: {},
      byHornGap: {},
      bySilicaGel: {},
      byInsulationLevel: {},
      byArresterBodyGround: {},
      byNeutralGround: {},
      byArrester: {},
      byOilLevel: {},
      byDropOut: {},
      byFuseLink: {},
      byMvBushing: {},
      byMvCableLug: {},
      byLvBushing: {},
      byLvCableLug: {},
      byHasLinkage: {},
      byStatusOfMounting: {},
      byMountingCondition: {}
    },
    lvFeeders: {
      total: 0,
      byRFuseRating: {},
      byLoadCurrentRanges: {},
      byCurrentUnbalanceRanges: {},
      byTransformerLoadRanges: {},
      byNeutralLoadRanges: {}
    }
  }; // Fallback to empty data structure
  }, [dashboardData]);

  // Loading component
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <Loader2 className="w-12 h-12 animate-spin text-indigo-600 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-700 mb-2">Loading Dashboard</h2>
        <p className="text-gray-500">Fetching latest statistics...</p>
      </div>
    </div>
  );

  // Error component
  const ErrorDisplay = () => (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center max-w-md">
        <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Data</h2>
        <p className="text-gray-500 mb-4">{loadingState.error}</p>
        <button
          onClick={() => fetchDashboardData(selectedRegion || undefined)}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center mx-auto"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </button>
      </div>
    </div>
  );

  if (loadingState.isLoading) return <LoadingSpinner />;
  if (loadingState.error) return <ErrorDisplay />;

  const currentData = getCurrentData;

  // Region filter handler
  const handleRegionChange = (region: string) => {
    setSelectedRegion(region);
  };

  const StatCard = ({ title, total, icon: Icon, color, data, animatedTotal }: any) => (
    <div className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:scale-105 p-6 border-l-4 ${color} relative overflow-hidden`}>
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-blue-50/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>

      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-1">{title}</h3>
            <div className="flex items-center space-x-2">
              <p className={`text-3xl font-bold transition-all duration-300 ${color.replace('border-l-', 'text-')}`}>
                {loadingState.isLoading ? (
                  <div className="flex items-center">
                    <Loader2 className="w-6 h-6 animate-spin text-gray-400 mr-2" />
                    <span className="text-lg text-gray-400">Loading...</span>
                  </div>
                ) : (
                  (animatedTotal || total)?.toLocaleString?.() || animatedTotal || total || '0'
                )}
              </p>
              {!loadingState.isLoading && (
                <div className="flex items-center text-green-600 text-sm">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  <span>Live</span>
                </div>
              )}
            </div>
          </div>
          <div className={`p-3 rounded-full shadow-inner ${color.replace('border-l-', 'bg-').replace('-500', '-100')}`}>
            <Icon className={`w-8 h-8 ${color.replace('border-l-', 'text-')}`} />
          </div>
        </div>

        <div className="space-y-3 max-h-64 overflow-y-auto">
        {Object.entries(data || {}).map(([key, value]: [string, unknown]) => {
          const numValue = value as number;
          const percentage = total > 0 ? ((numValue / total) * 100).toFixed(1) : '0.0';
          return (
            <div key={key} className="flex items-center justify-between">
              <span className="text-sm text-gray-600 truncate pr-2" title={key}>{key}</span>
              <div className="flex items-center space-x-2 min-w-0">
                <div className="flex-1 bg-gray-200 rounded-full h-2 min-w-[60px]">
                  <div 
                    className={`h-2 rounded-full transition-all duration-1000 ${color.replace('border-l-', 'bg-')}`}
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <span className="text-xs font-medium text-gray-500 min-w-[35px] text-right">
                  {percentage}%
                </span>
                <span className="text-xs font-medium text-gray-700 min-w-[25px] text-right">
                  {numValue}
                </span>
              </div>
            </div>
          );
        })}
        </div>
      </div>
    </div>
  );

  const InspectionDetailCard = ({ title, data, icon: Icon, color }: any) => (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
        <Icon className={`w-5 h-5 mr-2 ${color}`} />
        {title}
      </h3>
      <div className="space-y-4">
        {Object.entries(data || {}).map(([status, count]: [string, unknown]) => {
          const numCount = count as number;
          const total = Object.values(data || {}).reduce((sum: number, val: unknown) => sum + (val as number), 0);
          const percentage = total > 0 ? ((numCount / total) * 100).toFixed(1) : '0.0';
          
          // Determine color based on status
          let statusColor = 'bg-gray-500';
          if (status.toLowerCase().includes('good') || status === 'Ok' || status === 'Full' || 
              status === 'Acceptable' || status === 'Available'  || status === 'No') {
            statusColor = 'bg-green-500';
          } else if (status.toLowerCase().includes('fair') || status === '0.75' || status === '0.5') {
            statusColor = 'bg-yellow-500';
          } else if (status.toLowerCase().includes('poor') || status.toLowerCase().includes('missed') || 
                     status === 'Not Acceptable' || status === 'Not Available'  || status === '0.25' || status === 'Yes') {
            statusColor = 'bg-red-500';
          }

          return (
            <div key={status} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full ${statusColor} mr-3`}></div>
                <span className="text-sm font-medium text-gray-700">{status}</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex-1 bg-gray-200 rounded-full h-2 w-24">
                  <div 
                    className={`h-2 rounded-full ${statusColor} transition-all duration-1000`}
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <span className="text-sm font-bold text-gray-900 min-w-[40px] text-right">
                  {numCount}
                </span>
                <span className="text-xs text-gray-500 min-w-[35px] text-right">
                  ({percentage}%)
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const OverviewCard = ({ title, value, change, icon: Icon, color }: any) => (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {/* <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'} flex items-center mt-1`}>
            <TrendingUp className="w-4 h-4 mr-1" />
            {change >= 0 ? '+' : ''}{change}% from last month
          </p> */}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </div>
  );

  // Tab Navigation Component
  const TabNavigation = () => {
    const tabs = [
      { id: 'inspection', name: 'Inspection', icon: Activity, color: 'text-purple-600' },
      { id: 'transformer', name: 'Transformer', icon: Settings, color: 'text-green-600' },
      { id: 'lvfeeder', name: 'LV Feeder', icon: Zap, color: 'text-orange-600' },
      { id: 'basestation', name: 'Base Station', icon: MapPin, color: 'text-blue-600' }
    ];

    return (
      <div className="mb-8">
        <nav className="flex space-x-8 border-b border-gray-200">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? `border-indigo-500 ${tab.color} bg-indigo-50`
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm flex items-center space-x-2 transition-all duration-200 rounded-t-lg`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>
    );
  };

  // Tab Content Components
  const InspectionTabContent = () => (
    <div className="space-y-8">
      {/* Physical Condition Inspections */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Shield className="w-5 h-5 mr-2 text-blue-600" />
          Physical Condition Assessment
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <InspectionDetailCard
            title="Body Condition"
            data={currentData.inspections?.byBodyCondition || {}}
            icon={Shield}
            color="text-blue-600"
          />
          <InspectionDetailCard
            title="Horn Gap"
            data={currentData.inspections?.byHornGap || {}}
            icon={Zap}
            color="text-yellow-600"
          />
          <InspectionDetailCard
            title="Silica Gel"
            data={currentData.inspections?.bySilicaGel || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Insulation Level"
            data={currentData.inspections?.byInsulationLevel || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Oil Level"
            data={currentData.inspections?.byOilLevel || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Has Linkage"
            data={currentData.inspections?.byHasLinkage || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Arrester Body Ground"
            data={currentData.inspections?.byArresterBodyGround || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Neutral Ground"
            data={currentData.inspections?.byNeutralGround || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Status of Mounting"
            data={currentData.inspections?.byStatusOfMounting || {}}
            icon={CheckCircle}
            color="text-green-600"
          />
          <InspectionDetailCard
            title="Mounting Condition"
            data={currentData.inspections?.byMountingCondition || {}}
            icon={CheckCircle}
            color="text-green-600"
          />

        </div>
      </div>

      {/* Electrical Components */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Zap className="w-5 h-5 mr-2 text-yellow-600" />
          Electrical Components Status
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <InspectionDetailCard
            title="Arrester"
            data={currentData.inspections?.byArrester || {}}
            icon={Shield}
            color="text-red-600"
          />
          <InspectionDetailCard
            title="Drop Out"
            data={currentData.inspections?.byDropOut || {}}
            icon={XCircle}
            color="text-orange-600"
          />
          <InspectionDetailCard
            title="Fuse Link"
            data={currentData.inspections?.byFuseLink || {}}
            icon={Zap}
            color="text-yellow-600"
          />
          <InspectionDetailCard
            title="MV Bushing"
            data={currentData.inspections?.byMvBushing || {}}
            icon={Settings}
            color="text-blue-600"
          />
          <InspectionDetailCard
            title="MV CableLug"
            data={currentData.inspections?.byMvCableLug || {}}
            icon={Settings}
            color="text-blue-600"
          />
          <InspectionDetailCard
            title="LV Bushing"
            data={currentData.inspections?.byLvBushing || {}}
            icon={Settings}
            color="text-blue-600"
          />
          <InspectionDetailCard
            title="LV CableLug"
            data={currentData.inspections?.byLvCableLug || {}}
            icon={Settings}
            color="text-blue-600"
          />
        </div>
      </div>

      {/* Electrical Measurements */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Gauge className="w-5 h-5 mr-2 text-yellow-600" />
          Electrical Measurements & Performance
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <InspectionDetailCard
            title="Average Voltage Ranges"
            data={currentData.inspections?.byVoltageRanges || {}}
            icon={Gauge}
            color="text-yellow-600"
          />
          <InspectionDetailCard
            title="Load Current Ranges"
            data={currentData.inspections?.byLoadCurrentRanges || {}}
            icon={Battery}
            color="text-blue-600"
          />
          <InspectionDetailCard
            title="Voltage Unbalance"
            data={currentData.inspections?.byVoltageUnbalanceRanges || {}}
            icon={AlertTriangle}
            color="text-red-600"
          />
          <InspectionDetailCard
            title="Transformer Load"
            data={currentData.inspections?.byTransformerLoadRanges || {}}
            icon={Power}
            color="text-green-600"
          />
        </div>
      </div>
    </div>
  );

  const TransformerTabContent = () => (
    <div className="space-y-8">
      {/* Transformer Types and Capacity */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2 text-green-600" />
          Transformer Specifications
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <StatCard
            title="Transformers by Type"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Settings}
            color="border-l-green-500"
            data={currentData.transformers?.byType || {}}
          />
          <StatCard
            title="Transformer Capacity (kVA)"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byCapacity || {}}
          />
          <StatCard
            title="Primary Voltage"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byPrimaryVoltage || {}}
          />
          <StatCard
            title="Status"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byStatus || {}}
          />
          <StatCard
            title="Manufacturer"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byManufacturer || {}}
          />
          <StatCard
            title="Service Type"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byServiceType || {}}
          />
          <StatCard
            title="Cooling Type"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byCoolingType || {}}
          />
          <StatCard
            title="Vector Group"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byVectorGroup || {}}
          />
          <StatCard
            title="Year of Manufacturing"
            total={currentData.transformers?.total || 0}
            animatedTotal={animatedCounts.transformers || 0}
            icon={Zap}
            color="border-l-green-500"
            data={currentData.transformers?.byYearOfManufacturing || {}}
          />

        </div>
      </div>
    </div>
  );

  const LVFeederTabContent = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Zap className="w-5 h-5 mr-2 text-orange-600" />
          LV Feeder Analysis
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <StatCard
            title="LV Feeders by R Fuse Rating"
            total={currentData.lvFeeders?.total || 0}
            animatedTotal={animatedCounts.lvFeeders || 0}
            icon={Zap}
            color="border-l-orange-500"
            data={currentData.lvFeeders?.byRFuseRating || {}}
          />
          <StatCard
            title="LV Feeders by S Fuse Rating"
            total={currentData.lvFeeders?.total || 0}
            animatedTotal={animatedCounts.lvFeeders || 0}
            icon={Zap}
            color="border-l-orange-500"
            data={currentData.lvFeeders?.bySFuseRating || {}}
          />
          <StatCard
            title="LV Feeders by T Fuse Rating"
            total={currentData.lvFeeders?.total || 0}
            animatedTotal={animatedCounts.lvFeeders || 0}
            icon={Zap}
            color="border-l-orange-500"
            data={currentData.lvFeeders?.byTFuseRating || {}}
          />
          <StatCard
            title="LV Feeders by Load Current Ranges"
            total={currentData.lvFeeders?.total || 0}
            animatedTotal={animatedCounts.lvFeeders || 0}
            icon={Settings}
            color="border-l-orange-500"
            data={currentData.lvFeeders?.byLoadCurrentRanges || {}}
          />
          <StatCard
            title="LV Feeders by Current Unbalance Ranges"
            total={currentData.lvFeeders?.total || 0}
            animatedTotal={animatedCounts.lvFeeders || 0}
            icon={Settings}
            color="border-l-orange-500"
            data={currentData.lvFeeders?.byCurrentUnbalanceRanges || {}}
          />
          <StatCard
            title="LV Feeders by Neutral Load Ranges"
            total={currentData.lvFeeders?.total || 0}
            animatedTotal={animatedCounts.lvFeeders || 0}
            icon={Settings}
            color="border-l-orange-500"
            data={currentData.lvFeeders?.byNeutralLoadRanges || {}}
          />
          
        </div>
      </div>
    </div>
  );

  const BaseStationTabContent = () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <MapPin className="w-5 h-5 mr-2 text-blue-600" />
          Base Station Distribution
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <StatCard
            title="Base Stations by Type"
            total={currentData.basestations?.total || 0}
            animatedTotal={animatedCounts.basestations || 0}
            icon={MapPin}
            color="border-l-blue-500"
            data={currentData.basestations?.byType || {}}
          />
          <StatCard
            title="Base Stations by Region"
            total={currentData.basestations?.total || 0}
            animatedTotal={animatedCounts.basestations || 0}
            icon={MapPin}
            color="border-l-blue-500"
            data={currentData.basestations?.byRegion || {}}
          />
          <StatCard
            title="Base Stations by CSC"
            total={currentData.basestations?.total || 0}
            animatedTotal={animatedCounts.basestations || 0}
            icon={MapPin}
            color="border-l-blue-500"
            data={currentData.basestations?.byCSC || {}}
          />
          <StatCard
            title="Base Stations by Substation"
            total={currentData.basestations?.total || 0}
            animatedTotal={animatedCounts.basestations || 0}
            icon={MapPin}
            color="border-l-blue-500"
            data={currentData.basestations?.bySubstation || {}}
          />
          <StatCard
            title="Base Stations by Feeder"
            total={currentData.basestations?.total || 0}
            animatedTotal={animatedCounts.basestations || 0}
            icon={MapPin}
            color="border-l-blue-500"
            data={currentData.basestations?.byFeeder || {}}
          />
        </div>
      </div>
    </div>
  );

  // Function to render active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'inspection':
        return <InspectionTabContent />;
      case 'transformer':
        return <TransformerTabContent />;
      case 'lvfeeder':
        return <LVFeederTabContent />;
      case 'basestation':
        return <BaseStationTabContent />;
      default:
        return <InspectionTabContent />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">THMS</h1>
                <p className="text-sm text-gray-600">Comprehensive Transformer Health Monitoring System</p>
                {selectedRegion && (
                  <div className="flex items-center mt-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                    <span className="text-xs text-blue-600 font-medium">Filtered by: {selectedRegion}</span>
                  </div>
                )}
                {loadingState.lastUpdated && (
                  <p className="text-xs text-gray-500 mt-1">
                    Last updated: {new Date(loadingState.lastUpdated).toLocaleString()}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search assets..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div> */}

              <select
                value={selectedRegion}
                onChange={(e) => handleRegionChange(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              >
                <option value="">All Regions</option>
                {regions
                  .sort((a, b) => (a.name || '').localeCompare(b.name || ''))
                  .map((region) => (
                    <option key={region.csc_code || region.id} value={region.name}>
                      {region.name}
                    </option>
                  ))}
              </select>

              <button
                onClick={() => fetchDashboardData(selectedRegion || undefined)}
                disabled={loadingState.isLoading}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${loadingState.isLoading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>

              {/* <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button> */}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* <OverviewCard
            title="Total Assets"
            value={(animatedCounts.basestations || 0) + (animatedCounts.transformers || 0) + (animatedCounts.inspections || 0) + (animatedCounts.lvFeeders || 0) || 0}
            change={12.5}
            icon={BarChart3}
            color="bg-blue-600"
          /> */}
          <OverviewCard
            title="Base Stations"
            value={animatedCounts.basestations || currentData.basestations.total}
            change={8.2}
            icon={MapPin}
            color="bg-green-600"
          />
          <OverviewCard
            title="Transformers"
            value={animatedCounts.transformers || currentData.transformers.total}
            change={-2.1}
            icon={Settings}
            color="bg-purple-600"
          />
          <OverviewCard
            title="Recent Inspections"
            value={animatedCounts.inspections || currentData.inspections.total}
            change={15.3}
            icon={Activity}
            color="bg-orange-600"
          />
          <OverviewCard
            title="LV Feeders"
            value={animatedCounts.lvFeeders || currentData.lvFeeders.total}
            change={5.7}
            icon={Zap}
            color="bg-indigo-600"
          />
        </div>

        {/* Key Performance Indicators */}
        {/* <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <BarChart3 className="w-6 h-6 mr-3 text-indigo-600" />
            Key Performance Indicators
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-l-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Good Condition Assets</h3>
                  <p className="text-2xl font-bold text-green-600">
                    {Math.round(((currentData.inspections.byBodyCondition?.Good || 0) + (currentData.inspections.byHornGap?.Good || 0) + (currentData.inspections.bySilicaGel?.Good || 0)) / (currentData.inspections.total * 3) * 100) || 0}%
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-l-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Operational Efficiency</h3>
                  <p className="text-2xl font-bold text-blue-600">
                    {Math.round(((currentData.inspections.byInsulationLevel?.Acceptable || 0) / currentData.inspections.total) * 100) || 0}%
                  </p>
                </div>
                <Gauge className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-l-orange-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">Safety Compliance</h3>
                  <p className="text-2xl font-bold text-orange-600">
                    {Math.round((((currentData.inspections.byArresterBodyGround?.Available || 0) + (currentData.inspections.byNeutralGround?.Available || 0)) / (currentData.inspections.total * 2)) * 100) || 0}%
                  </p>
                </div>
                <Shield className="w-8 h-8 text-orange-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 border-l-4 border-l-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium text-gray-600">New Equipment</h3>
                  <p className="text-2xl font-bold text-purple-600">
                    {Math.round(((currentData.transformers.byStatus?.New || 0) / currentData.transformers.total) * 100) || 0}%
                  </p>
                </div>
                <Factory className="w-8 h-8 text-purple-500" />
              </div>
            </div>
          </div>
        </div> */}

        {/* Tab Navigation */}
        <TabNavigation />

        {/* Tab Content */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          {renderTabContent()}
        </div>

        {/* Footer */}
        <footer className="mt-12 text-center text-gray-500 text-sm">
          <p>Last updated: {new Date().toLocaleString()}</p>
          <p className="mt-2">Comprehensive Transformer Health Monitoring System</p>
        </footer>
      </div>
    </div>
  );
}

export default InspectionDashboard;

