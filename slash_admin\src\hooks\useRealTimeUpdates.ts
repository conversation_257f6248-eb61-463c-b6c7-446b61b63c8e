import { useState, useEffect, useCallback } from 'react';
import { SMSMessage, Notification } from '../types';

export function useRealTimeUpdates() {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Simulate WebSocket connection
  useEffect(() => {
    // In a real app, this would be a WebSocket connection
    const interval = setInterval(() => {
      setIsConnected(true);
      setLastUpdate(new Date());
      
      // Simulate random updates
      if (Math.random() > 0.9) {
        // Trigger update event
        window.dispatchEvent(new CustomEvent('smsUpdate', {
          detail: { type: 'new_message', timestamp: new Date() }
        }));
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const sendMessage = useCallback((messageId: string, content: string) => {
    // Simulate sending message
    return new Promise((resolve) => {
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('messageSent', {
          detail: { messageId, content, timestamp: new Date() }
        }));
        resolve(true);
      }, 1000);
    });
  }, []);

  return {
    isConnected,
    lastUpdate,
    sendMessage
  };
}