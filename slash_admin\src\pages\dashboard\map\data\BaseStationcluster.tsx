// import React, { useCallback, useEffect, useState } from "react";
// import L from "leaflet";
// import "./BaseStationcluster.css";
// import useSupercluster from "use-supercluster";
// import { Marker, Popup, useMap } from "react-leaflet";

// // Create icons for clusters dynamically
// const icons = {};
// const fetchIcon = (count, size) => {
//   if (!icons[count]) {
//     icons[count] = L.divIcon({
//       html: `<div class="cluster-marker" style="width: ${size}px; height: ${size}px;">
//         ${count}
//       </div>`,
//     });
//   }
//   return icons[count];
// };

// // Custom icon for single crime markers
// const cuffs = new L.Icon({
//   iconUrl: "/handcuffs.svg",
//   iconSize: [25, 25],
// });

// function BaseStationcluster({ data }) {
//   const maxZoom = 22;
//   const [bounds, setBounds] = useState(null);
//   const [zoom, setZoom] = useState(12);
//   const map = useMap();

//   // Function to update map bounds and zoom
//   function updateMap() {
//     const b = map.getBounds();
//     setBounds([
//       b.getSouthWest().lng,
//       b.getSouthWest().lat,
//       b.getNorthEast().lng,
//       b.getNorthEast().lat,
//     ]);
//     setZoom(map.getZoom());
//   }

//   const onMove = useCallback(() => {
//     updateMap();
//   }, [map]);

//   useEffect(() => {
//     updateMap();
//   }, [map]);

//   useEffect(() => {
//     map.on("move", onMove);
//     return () => {
//       map.off("move", onMove);
//     };
//   }, [map, onMove]);

//   // Convert crime data into GeoJSON format
//   const points = data.map((crime) => ({
//     type: "Feature",
//     properties: {
//       cluster: false,
//       crimeId: crime.station_code,
//       category: crime.Adama,
//       location: crime.Bekoji,
//     },
//     geometry: {
//       type: "Point",
//       // coordinates: crime.gps_location
//       //     ? crime.gps_location.split(',').map(coord => parseFloat(coord))
//       //     : [null, null]

//       // coordinates: [
//       //   parseFloat(crime.location.longitude),
//       //   parseFloat(crime.location.latitude),
//       // ],

//       // coordinates: [
//       //   parseFloat(crime.gps_location.split(',')[0]),
//       //   parseFloat(crime.gps_location.split(',')[1]),
//       // ],
//       // coordinates: [
//       //   7.53201489999999, 39.24526749999999
//       // ],

//       coordinates: crime.gps_location
//         .split(',')
//         .map(coord => parseFloat(coord))
//         .reverse()
//     },
//   }));

//   console.log("points.geometry.coordinates", points?.geometry?.coordinates)

//   // Use Supercluster to generate clusters
//   const { clusters, supercluster } = useSupercluster({
//     points: points,
//     bounds: bounds,
//     zoom: zoom,
//     options: { radius: 75, maxZoom: 17 },
//   });

//   return (
//     <>
//       {clusters.map((cluster) => {
//         const [longitude, latitude,] = cluster.geometry.coordinates;
//         const { cluster: isCluster, point_count: pointCount } =
//           cluster.properties;

//         // Render a cluster marker
//         if (isCluster) {
//           return (
//             <Marker
//               key={`cluster-${cluster.id}`}
//               position={[latitude, longitude]}
//               icon={fetchIcon(
//                 pointCount,
//                 10 + (pointCount / points.length) * 40
//               )}
//               eventHandlers={{
//                 click: () => {
//                   const expansionZoom = Math.min(
//                     supercluster.getClusterExpansionZoom(cluster.id),
//                     maxZoom
//                   );
//                   map.setView([latitude, longitude], expansionZoom, {
//                     animate: true,
//                   });
//                 },
//               }}
//             >
//               {/* Optional: Add a popup for clusters */}
//               {/* <Popup>
//                 <div>
//                   <p>{pointCount} crimes in this area</p>
//                 </div>
//               </Popup> */}
//             </Marker>
//           );
//         }
//       else{
//         // Render a single crime marker
//       return (
//           <Marker
//             key={`crime-${cluster.properties.station_code}`}
//             position={[latitude, longitude]}
//             icon={cuffs}
//           >
//             {/* Popup for individual crimes */}
//             <Popup>
//               <div>
//                 <h3>Crime Details</h3>
//                 <p><strong>station_code:</strong> {cluster.properties.station_code}</p>
//                 {/* <p><strong>Category:</strong> {cluster.properties.category}</p>
//                 <p><strong>Location:</strong> {cluster.properties.location.street.name}</p> */}
//               </div>
//             </Popup>
//           </Marker>
//         );
//       }
//       })
//     }
//     </>
//   );
// }

// export default BaseStationcluster;

import React, { useCallback, useEffect, useState } from "react";
import L from "leaflet";
import "./BaseStationcluster.css";
import useSupercluster from "use-supercluster";
import { Marker, Popup, useMap } from "react-leaflet";
import { Link, useNavigate } from "react-router";

// Create icons for clusters dynamically
const icons = {};
const fetchIcon = (count, size) => {
	if (!icons[count]) {
		icons[count] = L.divIcon({
			html: `<div class="cluster-marker" style="width: ${size}px; height: ${size}px;">
        ${count}
      </div>`,
		});
	}
	return icons[count];
};

// Custom icon for single crime markers
const cuffs = new L.Icon({
	iconUrl: "/energy.svg",
	iconSize: [25, 25],
});

function BaseStationcluster({ data }) {
	const maxZoom = 22;
	const [bounds, setBounds] = useState(null);
	const [zoom, setZoom] = useState(12);
	const map = useMap();
	const navigate = useNavigate();

	// Function to update map bounds and zoom
	function updateMap() {
		const b = map.getBounds();
		setBounds([b.getSouthWest().lng, b.getSouthWest().lat, b.getNorthEast().lng, b.getNorthEast().lat]);
		setZoom(map.getZoom());
	}

	const onMove = useCallback(() => {
		updateMap();
	}, [map]);

	useEffect(() => {
		updateMap();
	}, [map]);

	useEffect(() => {
		map.on("move", onMove);
		return () => {
			map.off("move", onMove);
		};
	}, [map, onMove]);

	// Convert crime data into GeoJSON format
	const points = data.map((crime) => {
		const [longitude, latitude] = crime.gps_location
			?.split(",") // Split by comma
			.map((coord) => parseFloat(coord.trim())) // Parse and trim whitespace
			.reverse(); // Reverse to [latitude, longitude]

		return {
			type: "Feature",
			properties: {
				cluster: false,
				station_code: crime.station_code,
				region: crime.region,
				csc: crime.csc,
				address: crime.address,
				substation: crime.substation,
				feeder: crime.feeder,
				// category: crime.Adama,
				// location: crime.Bekoji,
			},
			geometry: {
				type: "Point",
				coordinates: [longitude, latitude], // Store as [longitude, latitude]
			},
		};
	});

	// console.log("Parsed points:", points);

	// Use Supercluster to generate clusters
	const { clusters, supercluster } = useSupercluster({
		points: points,
		bounds: bounds,
		zoom: zoom,
		options: { radius: 75, maxZoom: 17 },
	});

	return (
		<>
			{clusters.map((cluster) => {
				const [longitude, latitude] = cluster.geometry.coordinates;
				const { cluster: isCluster, point_count: pointCount } = cluster.properties;

				// Render a cluster marker
				if (isCluster) {
					return (
						<Marker
							key={`cluster-${cluster.id}`}
							position={[latitude, longitude]} // Leaflet expects [latitude, longitude]
							icon={fetchIcon(pointCount, 10 + (pointCount / points.length) * 40)}
							eventHandlers={{
								click: () => {
									const expansionZoom = Math.min(supercluster.getClusterExpansionZoom(cluster.id), maxZoom);
									map.setView([latitude, longitude], expansionZoom, {
										animate: true,
									});
								},
							}}
						>
							{/* Optional: Add a popup for clusters */}
							{/* <Popup>
                <div>
                  <p>{pointCount} crimes in this area</p>
                </div>
              </Popup> */}
						</Marker>
					);
				} else {
					// Render a single crime marker
					return (
						<Marker
							key={`crime-${cluster.properties.station_code}`}
							position={[latitude, longitude]} // Leaflet expects [latitude, longitude]
							icon={cuffs}
						>
							{/* Popup for individual crimes */}
							<Popup>
								<div>
									<h3>Base Station</h3>
									<p>
										<strong>Station Code:</strong>
										{/* <a  navigate("/basestation") /> */}
										<Link to={`/basestation/${cluster.properties.station_code}`}>
											{cluster.properties.station_code}
										</Link>
										{/* </a> */}
									</p>
									<p>
										<strong>region:</strong> {cluster.properties.region}
									</p>
									<p>
										<strong>csc:</strong> {cluster.properties.csc}
									</p>
									<p>
										<strong>address:</strong> {cluster.properties.address}
									</p>
									<p>
										<strong>substation:</strong> {cluster.properties.substation}
									</p>
									<p>
										<strong>feeder:</strong> {cluster.properties.feeder}
									</p>
								</div>
							</Popup>
						</Marker>
					);
				}
			})}
		</>
	);
}

export default BaseStationcluster;
