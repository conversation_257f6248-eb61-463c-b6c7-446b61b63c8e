import { useState, useEffect, useCallback } from 'react';
import { Reply } from '../types';
import messageService from '../api/services/messageService';

interface PaginatedReplies {
  count: number;
  next: string | null;
  previous: string | null;
  results: Reply[];
}

interface UseRepliesParams {
  messageId: string;
  phoneNumberFilter?: string;
  searchFilter?: string;
  senderTypeFilter?: 'customer' | 'agent';
}

export function useReplies({ messageId, phoneNumberFilter, searchFilter, senderTypeFilter }: UseRepliesParams) {
  const [replies, setReplies] = useState<Reply[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const fetchReplies = useCallback(async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) {
        setLoading(true);
        setReplies([]);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      const params = {
        message_id: messageId,
        page,
        page_size: 20,
        ...(phoneNumberFilter && { phone_number: phoneNumberFilter }),
        ...(searchFilter && { search: searchFilter }),
        ...(senderTypeFilter && { sender_type: senderTypeFilter })
      };

      const response: PaginatedReplies = await messageService.getReplies(params);
      
      const normalizedReplies = response.results.map((reply: any) => ({
        id: reply.id,
        content: reply.content,
        timestamp: new Date(reply.timestamp),
        sender: reply.sender ? {
          id: reply.sender.id,
          name: reply.sender.name || reply.sender.username,
          email: reply.sender.email,
          avatar: reply.sender.avatar
        } : null,
        is_from_customer: reply.is_from_customer,
        status: reply.status,
        attachments: reply.attachments || []
      }));

      if (append) {
        setReplies(prev => [...prev, ...normalizedReplies]);
      } else {
        setReplies(normalizedReplies);
      }

      setTotalCount(response.count);
      setHasMore(!!response.next);
      setCurrentPage(page);

    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch replies');
      console.error('Error fetching replies:', err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [messageId, phoneNumberFilter, searchFilter, senderTypeFilter]);

  const loadMoreReplies = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchReplies(currentPage + 1, true);
    }
  }, [fetchReplies, currentPage, loadingMore, hasMore]);

  const refreshReplies = useCallback(() => {
    setCurrentPage(1);
    fetchReplies(1, false);
  }, [fetchReplies]);

  // Initial fetch and refetch when dependencies change
  useEffect(() => {
    if (messageId) {
      refreshReplies();
    }
  }, [messageId, phoneNumberFilter, searchFilter, senderTypeFilter, refreshReplies]);

  const addReply = useCallback(async (content: string) => {
    try {
      const newReply = await messageService.addReply(messageId, content);
      
      // Add the new reply to the beginning of the list (most recent first)
      const normalizedReply = {
        id: newReply.id,
        content: newReply.content,
        timestamp: new Date(newReply.timestamp),
        sender: newReply.sender ? {
          id: newReply.sender.id,
          name: newReply.sender.name || newReply.sender.username,
          email: newReply.sender.email,
          avatar: newReply.sender.avatar
        } : null,
        is_from_customer: newReply.is_from_customer,
        status: newReply.status,
        attachments: newReply.attachments || []
      };

      setReplies(prev => [normalizedReply, ...prev]);
      setTotalCount(prev => prev + 1);
      
      return normalizedReply;
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add reply');
      throw err;
    }
  }, [messageId]);

  return {
    replies,
    loading,
    loadingMore,
    error,
    hasMore,
    totalCount,
    fetchReplies: refreshReplies,
    loadMoreReplies,
    addReply
  };
}
