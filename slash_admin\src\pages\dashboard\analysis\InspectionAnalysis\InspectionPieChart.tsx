import React from "react";
import { Pie } from "@ant-design/plots";
import { InspectionData } from "./index";

interface InspectionPieChartProps {
    tabKey: string;
    data: InspectionData[];
}

const InspectionPieChart: React.FC<InspectionPieChartProps> = ({ tabKey, data }) => {
    const prepareChartData = () => {
        const filteredData = data.filter((item) => !item.key.includes("grand_total"));

        // Calculate totals across all regions
        let totalValues: Record<string, number> = {};

        // Aggregate values from all regions
        filteredData.forEach(region => {
            switch (tabKey) {
                case "body_condition":
                case "status_of_mounting":
                case "mounting_condition":
                case "silica_gel":
                    totalValues.good = (totalValues.good || 0) + (region.good || 0);
                    totalValues.fair = (totalValues.fair || 0) + (region.fair || 0);
                    totalValues.poor = (totalValues.poor || 0) + (region.poor || 0);
                    break;
                case "arrester":
                case "drop_out":
                case "fuse_link":
                case "mv_bushing":
                case "mv_cable_lug":
                case "lv_bushing":
                case "lv_cable_lug":
                    totalValues.ok = (totalValues.ok || 0) + (region.ok || 0);
                    totalValues.oneMissed = (totalValues.oneMissed || 0) + (region.oneMissed || 0);
                    totalValues.twoMissed = (totalValues.twoMissed || 0) + (region.twoMissed || 0);
                    totalValues.allMissed = (totalValues.allMissed || 0) + (region.allMissed || 0);
                    break;
                case "oil_level":
                    totalValues.full = (totalValues.full || 0) + (region.full || 0);
                    totalValues.threeFourths = (totalValues.threeFourths || 0) + (region.threeFourths || 0);
                    totalValues.half = (totalValues.half || 0) + (region.half || 0);
                    totalValues.oneFourth = (totalValues.oneFourth || 0) + (region.oneFourth || 0);
                    break;
                case "insulation_level":
                    totalValues.acceptable = (totalValues.acceptable || 0) + (region.acceptable || 0);
                    totalValues.notAcceptable = (totalValues.notAcceptable || 0) + (region.notAcceptable || 0);
                    break;
                case "horn_gap":
                    totalValues.good = (totalValues.good || 0) + (region.good || 0);
                    totalValues.poor = (totalValues.poor || 0) + (region.poor || 0);
                    break;
                case "has_linkage":
                    totalValues.yes = (totalValues.yes || 0) + (region.yes || 0);
                    totalValues.no = (totalValues.no || 0) + (region.no || 0);
                    break;
                case "arrester_body_ground":
                case "neutral_ground":
                    totalValues.available = (totalValues.available || 0) + (region.available || 0);
                    totalValues.notAvailable = (totalValues.notAvailable || 0) + (region.notAvailable || 0);
                    break;
                default:
                    break;
            }
        });

        // Convert to pie chart data format
        switch (tabKey) {
            case "body_condition":
            case "status_of_mounting":
            case "mounting_condition":
            case "silica_gel":
                return [
                    { type: "Good", value: totalValues.good || 0 },
                    { type: "Fair", value: totalValues.fair || 0 },
                    { type: "Poor", value: totalValues.poor || 0 },
                ];
            case "arrester":
            case "drop_out":
            case "fuse_link":
            case "mv_bushing":
            case "mv_cable_lug":
            case "lv_bushing":
            case "lv_cable_lug":
                return [
                    { type: "Ok", value: totalValues.ok || 0 },
                    { type: "One Missed", value: totalValues.oneMissed || 0 },
                    { type: "Two Missed", value: totalValues.twoMissed || 0 },
                    { type: "All Missed", value: totalValues.allMissed || 0 },
                ];
            case "oil_level":
                return [
                    { type: "Full", value: totalValues.full || 0 },
                    { type: "0.75", value: totalValues.threeFourths || 0 },
                    { type: "0.5", value: totalValues.half || 0 },
                    { type: "0.25", value: totalValues.oneFourth || 0 },
                ];
            case "insulation_level":
                return [
                    { type: "Acceptable", value: totalValues.acceptable || 0 },
                    { type: "Not Acceptable", value: totalValues.notAcceptable || 0 },
                ];
            case "horn_gap":
                return [
                    { type: "Good", value: totalValues.good || 0 },
                    { type: "Poor", value: totalValues.poor || 0 },
                ];
            case "has_linkage":
                return [
                    { type: "Yes", value: totalValues.yes || 0 },
                    { type: "No", value: totalValues.no || 0 },
                ];
            case "arrester_body_ground":
            case "neutral_ground":
                return [
                    { type: "Available", value: totalValues.available || 0 },
                    { type: "Not Available", value: totalValues.notAvailable || 0 },
                ];
            default:
                return [];
        }
    };

    const config = {
        data: prepareChartData(),
        angleField: "value",
        colorField: "type",
        radius: 0.8,
        label: {
            type: "outer",
            content: "{name}: {percentage}",
        },
        interactions: [{ type: "element-active" }],
    };

    return (
        <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4">Distribution</h3>
            <Pie {...config} height={400} width={400} />
        </div>
    );
};

export default InspectionPieChart;