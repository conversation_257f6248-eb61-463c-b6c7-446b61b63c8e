from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.db.models import Q, Count
from django.utils import timezone
from .models import SMSMessage, Reply, User


def dashboard(request):
    """Dashboard view with SMS statistics"""
    # Get statistics
    total_cases = SMSMessage.objects.count()
    new_cases = SMSMessage.objects.filter(status='new').count()
    in_progress_cases = SMSMessage.objects.filter(status='in-progress').count()
    replied_cases = SMSMessage.objects.filter(status='replied').count()
    closed_cases = SMSMessage.objects.filter(status='closed').count()

    # Recent messages
    recent_messages = SMSMessage.objects.all()[:10]

    # Status distribution for chart
    status_stats = SMSMessage.objects.values('status').annotate(count=Count('status'))

    context = {
        'total_cases': total_cases,
        'new_cases': new_cases,
        'in_progress_cases': in_progress_cases,
        'replied_cases': replied_cases,
        'closed_cases': closed_cases,
        'recent_messages': recent_messages,
        'status_stats': status_stats,
    }
    return render(request, 'sms_app/dashboard.html', context)


def message_list(request):
    """List all messages with pagination and filtering"""
    print("SSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS")
    messages = SMSMessage.objects.all()

    # Search functionality
    search = request.GET.get('search')
    if search:
        messages = messages.filter(
            Q(phone_number__icontains=search) |
            Q(content__icontains=search) |
            Q(case_id__icontains=search)
        )

    # Status filter
    status_filter = request.GET.get('status')
    if status_filter:
        messages = messages.filter(status=status_filter)

    # Category filter
    category = request.GET.get('category')
    if category:
        messages = messages.filter(category=category)

    # Pagination
    paginator = Paginator(messages, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search': search,
        'status': status_filter,
        'category': category,
        'status_choices': SMSMessage.STATUS_CHOICES,
        'category_choices': SMSMessage.CATEGORY_CHOICES,
    }
    return render(request, 'sms_app/message_list.html', context)


def message_detail(request, message_id):
    """Message detail view"""
    print('kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk')
    message = get_object_or_404(SMSMessage, id=message_id)
    replies = message.replies.all().order_by('timestamp')

    context = {
        'message': message,
        'replies': replies,
    }
    return render(request, 'sms_app/message_detail.html', context)


def api_message_status(request, message_id):
    """API endpoint to get message status"""
    try:
        message = SMSMessage.objects.get(id=message_id)
        return JsonResponse({
            'id': str(message.id),
            'status': message.status,
            'case_id': message.case_id,
            'timestamp': message.timestamp.isoformat(),
            'assigned_to': message.assigned_to.username if message.assigned_to else None,
        })
    except SMSMessage.DoesNotExist:
        return JsonResponse({'error': 'Message not found'}, status=404)