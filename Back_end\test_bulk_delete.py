#!/usr/bin/env python
"""
Test script to verify bulk delete endpoints work correctly.
This script demonstrates the proper usage of the bulk delete endpoints.
"""

import os
import sys
import django
import json

# Add the core directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from transformer.models import Basestation, TransformerData

User = get_user_model()


def test_bulk_delete_endpoints():
    """Test both bulk delete endpoints with proper authentication"""
    
    # Create a test client
    client = Client()
    
    # Create or get a test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Login the user
    client.force_login(user)
    print("✅ User authenticated")
    
    # Create test basestations
    basestation1, created1 = Basestation.objects.get_or_create(
        station_code='TEST-BULK-001',
        defaults={
            'substation': 'Test Substation 1',
            'feeder': 'Test Feeder 1',
            'address': 'Test Address 1',
            'region': 'Test Region',
            'csc': 'TEST01',
            'station_type': 'Distribution',
            'created_by': user,
            'updated_by': user
        }
    )
    
    basestation2, created2 = Basestation.objects.get_or_create(
        station_code='TEST-BULK-002',
        defaults={
            'substation': 'Test Substation 2',
            'feeder': 'Test Feeder 2',
            'address': 'Test Address 2',
            'region': 'Test Region',
            'csc': 'TEST02',
            'station_type': 'Distribution',
            'created_by': user,
            'updated_by': user
        }
    )
    
    print(f"✅ Created/found test basestations: {basestation1.station_code}, {basestation2.station_code}")
    
    # Create test transformers with all required fields
    transformer1, created3 = TransformerData.objects.get_or_create(
        basestation=basestation1,
        defaults={
            'trafo_type': 'Conservator',
            'capacity': '100',
            'primary_voltage': '15',
            'colling_type': 'ONAN',
            'serial_number': 'TEST-SN-001',
            'service_type': 'Residential',
            'status': 'In Service',
            'manufacturer': 'ABB',
            'vector_group': 'DY1',
            'impedance_voltage': 5.5,
            'created_by': user,
            'updated_by': user
        }
    )

    transformer2, created4 = TransformerData.objects.get_or_create(
        basestation=basestation2,
        defaults={
            'trafo_type': 'Hermatical',
            'capacity': '200',
            'primary_voltage': '19',
            'colling_type': 'Dry Type',
            'serial_number': 'TEST-SN-002',
            'service_type': 'Commercial',
            'status': 'In Service',
            'manufacturer': 'Siemens',
            'vector_group': 'DY5',
            'impedance_voltage': 6.0,
            'created_by': user,
            'updated_by': user
        }
    )
    
    print(f"✅ Created/found test transformers: {transformer1.id}, {transformer2.id}")
    
    # Test basestation bulk delete
    print("\n🧪 Testing basestation bulk delete...")
    response = client.post(
        '/api/transformer/basestations/bulk_delete/',
        data=json.dumps({
            'station_codes': ['TEST-BULK-001', 'NONEXISTENT-001']
        }),
        content_type='application/json'
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Response: {json.dumps(data, indent=2)}")
    else:
        print(f"❌ Error: {response.content.decode()}")
    
    # Test transformer bulk delete
    print("\n🧪 Testing transformer bulk delete...")
    response = client.post(
        '/api/transformer/transformerdata/bulk_delete/',
        data=json.dumps({
            'transformer_ids': [transformer2.id, 99999]
        }),
        content_type='application/json'
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Response: {json.dumps(data, indent=2)}")
    else:
        print(f"❌ Error: {response.content.decode()}")
    
    # Test error cases
    print("\n🧪 Testing error cases...")
    
    # Empty list
    response = client.post(
        '/api/transformer/basestations/bulk_delete/',
        data=json.dumps({'station_codes': []}),
        content_type='application/json'
    )
    print(f"Empty list test - Status: {response.status_code}")
    if response.status_code == 400:
        print(f"✅ Correctly rejected empty list: {response.json()}")
    
    # Invalid data type
    response = client.post(
        '/api/transformer/transformerdata/bulk_delete/',
        data=json.dumps({'transformer_ids': 'not-a-list'}),
        content_type='application/json'
    )
    print(f"Invalid data type test - Status: {response.status_code}")
    if response.status_code == 400:
        print(f"✅ Correctly rejected invalid data type: {response.json()}")
    
    print("\n✅ All tests completed!")


if __name__ == '__main__':
    test_bulk_delete_endpoints()
